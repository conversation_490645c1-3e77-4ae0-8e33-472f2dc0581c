/**
 * 切换到专业版本的脚本
 */

const fs = require('fs');
const path = require('path');

console.log('🔄 正在切换到专业版本...');

// 备份当前的index.html
const currentIndex = fs.readFileSync('index.html', 'utf8');
fs.writeFileSync('index-backup.html', currentIndex);
console.log('✅ 已备份当前版本到 index-backup.html');

// 复制专业版本到index.html
const professionalContent = fs.readFileSync('professional-vue-app.html', 'utf8');
fs.writeFileSync('index.html', professionalContent);
console.log('✅ 已切换到专业版本');

console.log('\n🚀 专业版本已激活！');
console.log('📍 访问地址: http://localhost:5173/');
console.log('🔧 要恢复普通版本，请运行: node switch-to-normal.js');
