<template>
  <div class="route-error-fallback">
    <div class="fallback-icon">⚠️</div>
    <h2 class="fallback-title">页面加载失败</h2>
    <p class="fallback-message">{{ message }}</p>
    <button @click="retry" class="retry-button">重新尝试</button>
    <router-link to="/dashboard" class="home-link">返回首页</router-link>
  </div>
</template>

<script setup lang="ts">
interface Props {
  message?: string
}

const props = withDefaults(defineProps<Props>(), {
  message: '页面组件无法加载，请稍后重试'
})

const retry = () => {
  window.location.reload()
}
</script>

<style scoped>
.route-error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: 2rem;
  text-align: center;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, sans-serif;
}

.fallback-icon {
  font-size: 4rem;
  margin-bottom: 1rem;
}

.fallback-title {
  color: #dc2626;
  margin: 0 0 1rem 0;
  font-size: 1.5rem;
}

.fallback-message {
  color: #6b7280;
  margin: 0 0 2rem 0;
  max-width: 500px;
  line-height: 1.5;
}

.retry-button {
  background: #3b82f6;
  color: white;
  border: none;
  padding: 0.75rem 2rem;
  border-radius: 6px;
  cursor: pointer;
  font-size: 1rem;
  margin: 0 0.5rem 1rem 0.5rem;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background: #2563eb;
}

.home-link {
  color: #6b7280;
  text-decoration: none;
  padding: 0.75rem 2rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  transition: all 0.2s;
}

.home-link:hover {
  color: #374151;
  border-color: #9ca3af;
  background: #f9fafb;
}
</style>