<template>
  <div class="stock-detail-page">
    <!-- 顶部导航栏 -->
    <div class="detail-header">
      <div class="header-content">
        <div class="back-button">
          <el-button @click="goBack" :icon="ArrowLeft" type="text" size="large">
            返回行情
          </el-button>
        </div>
        <div class="stock-title" v-if="stockInfo">
          <h1 class="stock-name">{{ stockInfo.name }}</h1>
          <span class="stock-code">{{ stockInfo.symbol }}</span>
        </div>
        <div class="header-actions">
          <el-button @click="addToWatchlist" :icon="Star" type="primary" plain>
            {{ isInWatchlist ? '已关注' : '关注' }}
          </el-button>
          <el-button @click="refreshData" :icon="Refresh" :loading="loading">
            刷新
          </el-button>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="detail-content">
      <!-- 股票基本信息卡片 -->
      <div class="info-section">
        <el-card class="price-card" v-loading="loading">
          <div class="price-info" v-if="stockInfo">
            <div class="current-price">
              <span class="price" :class="getPriceClass(stockInfo.changePercent)">
                ¥{{ formatPrice(stockInfo.currentPrice) }}
              </span>
              <div class="price-change">
                <span class="change-amount" :class="getPriceClass(stockInfo.changePercent)">
                  {{ stockInfo.change >= 0 ? '+' : '' }}{{ formatPrice(stockInfo.change) }}
                </span>
                <span class="change-percent" :class="getPriceClass(stockInfo.changePercent)">
                  {{ stockInfo.changePercent >= 0 ? '+' : '' }}{{ stockInfo.changePercent.toFixed(2) }}%
                </span>
              </div>
            </div>
            <div class="market-status">
              <el-tag :type="getMarketStatusType()" size="small">
                {{ getMarketStatusText() }}
              </el-tag>
              <span class="update-time">更新时间: {{ formatTime(stockInfo.updateTime) }}</span>
            </div>
          </div>
        </el-card>

        <!-- 关键指标 -->
        <el-card class="metrics-card">
          <template #header>
            <span>关键指标</span>
          </template>
          <div class="metrics-grid" v-if="stockInfo">
            <div class="metric-item">
              <span class="metric-label">开盘价</span>
              <span class="metric-value">¥{{ formatPrice(stockInfo.openPrice) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">最高价</span>
              <span class="metric-value">¥{{ formatPrice(stockInfo.highPrice) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">最低价</span>
              <span class="metric-value">¥{{ formatPrice(stockInfo.lowPrice) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">昨收价</span>
              <span class="metric-value">¥{{ formatPrice(stockInfo.prevClose) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">成交量</span>
              <span class="metric-value">{{ formatVolume(stockInfo.volume) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">成交额</span>
              <span class="metric-value">{{ formatAmount(stockInfo.amount) }}</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">换手率</span>
              <span class="metric-value">{{ stockInfo.turnoverRate?.toFixed(2) }}%</span>
            </div>
            <div class="metric-item">
              <span class="metric-label">市盈率</span>
              <span class="metric-value">{{ stockInfo.pe?.toFixed(2) || '--' }}</span>
            </div>
          </div>
        </el-card>
      </div>

      <!-- 图表和数据区域 -->
      <div class="chart-section">
        <el-card class="chart-card">
          <template #header>
            <div class="chart-header">
              <span>K线图</span>
              <div class="chart-controls">
                <el-radio-group v-model="chartPeriod" size="small" @change="onPeriodChange">
                  <el-radio-button label="1m">分时</el-radio-button>
                  <el-radio-button label="5m">5分</el-radio-button>
                  <el-radio-button label="15m">15分</el-radio-button>
                  <el-radio-button label="30m">30分</el-radio-button>
                  <el-radio-button label="1h">1小时</el-radio-button>
                  <el-radio-button label="1d">日K</el-radio-button>
                  <el-radio-button label="1w">周K</el-radio-button>
                </el-radio-group>
              </div>
            </div>
          </template>
          <div class="chart-container">
            <EnhancedKLineChart
              :symbol="stockSymbol"
              :symbol-name="stockInfo?.name"
              :period="chartPeriod"
              :height="400"
              :data="chartData"
              @period-change="onPeriodChange"
            />
          </div>
        </el-card>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ArrowLeft, Star, Refresh } from '@element-plus/icons-vue'
import EnhancedKLineChart from '@/components/charts/KLineChart/EnhancedKLineChart.vue'
import { marketApi } from '@/api/market'
import type { StockInfo, KLineData } from '@/types/market'

const route = useRoute()
const router = useRouter()

// 响应式数据
const loading = ref(false)
const stockInfo = ref<StockInfo | null>(null)
const chartData = ref<KLineData[]>([])
const chartPeriod = ref('1d')
const isInWatchlist = ref(false)
const refreshTimer = ref<number | null>(null)

// 计算属性
const stockSymbol = computed(() => route.params.symbol as string)

// 方法
const goBack = () => {
  router.push('/market')
}

const formatPrice = (price: number | undefined): string => {
  if (price === undefined || price === null) return '--'
  return price.toFixed(2)
}

const formatVolume = (volume: number | undefined): string => {
  if (!volume) return '--'
  if (volume >= 100000000) {
    return (volume / 100000000).toFixed(2) + '亿'
  } else if (volume >= 10000) {
    return (volume / 10000).toFixed(2) + '万'
  }
  return volume.toString()
}

const formatAmount = (amount: number | undefined): string => {
  if (!amount) return '--'
  if (amount >= 100000000) {
    return (amount / 100000000).toFixed(2) + '亿'
  } else if (amount >= 10000) {
    return (amount / 10000).toFixed(2) + '万'
  }
  return amount.toString()
}

const formatTime = (time: string | undefined): string => {
  if (!time) return '--'
  return new Date(time).toLocaleTimeString()
}

const getPriceClass = (changePercent: number): string => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-neutral'
}

const getMarketStatusType = (): string => {
  const now = new Date()
  const hour = now.getHours()
  const minute = now.getMinutes()
  const time = hour * 100 + minute

  // 简化的市场时间判断
  if ((time >= 930 && time <= 1130) || (time >= 1300 && time <= 1500)) {
    return 'success'
  }
  return 'info'
}

const getMarketStatusText = (): string => {
  const now = new Date()
  const hour = now.getHours()
  const minute = now.getMinutes()
  const time = hour * 100 + minute

  if ((time >= 930 && time <= 1130) || (time >= 1300 && time <= 1500)) {
    return '交易中'
  }
  return '休市'
}

const loadStockInfo = async () => {
  if (!stockSymbol.value) return

  loading.value = true
  try {
    const response = await marketApi.getStockInfo(stockSymbol.value)
    if (response.success) {
      stockInfo.value = response.data
    } else {
      ElMessage.error('获取股票信息失败')
    }
  } catch (error) {
    console.error('获取股票信息失败:', error)
    ElMessage.error('获取股票信息失败')
  } finally {
    loading.value = false
  }
}

const loadChartData = async () => {
  if (!stockSymbol.value) return

  try {
    const response = await marketApi.getKLineData(stockSymbol.value, chartPeriod.value)
    if (response.success) {
      chartData.value = response.data
    }
  } catch (error) {
    console.error('获取K线数据失败:', error)
  }
}

const refreshData = async () => {
  await Promise.all([
    loadStockInfo(),
    loadChartData()
  ])
}

const onPeriodChange = (period: string) => {
  chartPeriod.value = period
  loadChartData()
}

const addToWatchlist = async () => {
  if (!stockSymbol.value) return

  try {
    if (isInWatchlist.value) {
      await marketApi.removeFromWatchlist(stockSymbol.value)
      isInWatchlist.value = false
      ElMessage.success('已取消关注')
    } else {
      await marketApi.addToWatchlist(stockSymbol.value)
      isInWatchlist.value = true
      ElMessage.success('已添加关注')
    }
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const checkWatchlistStatus = async () => {
  if (!stockSymbol.value) return

  try {
    const response = await marketApi.getWatchlist()
    if (response.success) {
      isInWatchlist.value = response.data.some((item: any) => item.symbol === stockSymbol.value)
    }
  } catch (error) {
    console.error('检查关注状态失败:', error)
  }
}

const startAutoRefresh = () => {
  // 每30秒自动刷新数据
  refreshTimer.value = window.setInterval(() => {
    if (getMarketStatusText() === '交易中') {
      loadStockInfo()
    }
  }, 30000)
}

const stopAutoRefresh = () => {
  if (refreshTimer.value) {
    clearInterval(refreshTimer.value)
    refreshTimer.value = null
  }
}

// 生命周期
onMounted(async () => {
  console.log('股票详情页面加载，股票代码:', stockSymbol.value)
  await refreshData()
  await checkWatchlistStatus()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.stock-detail-page {
  min-height: calc(100vh - 64px);
  background-color: #f5f7fa;
}

/* 顶部导航栏 */
.detail-header {
  background: white;
  border-bottom: 1px solid #e4e7ed;
  padding: 16px 24px;
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  max-width: 1200px;
  margin: 0 auto;
}

.stock-title {
  flex: 1;
  margin-left: 24px;
}

.stock-name {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin: 0;
  line-height: 1.2;
}

.stock-code {
  font-size: 14px;
  color: #909399;
  margin-left: 8px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

/* 主要内容区域 */
.detail-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 24px;
}

/* 信息区域 */
.info-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.price-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.price-info {
  text-align: center;
  padding: 16px 0;
}

.current-price {
  margin-bottom: 16px;
}

.price {
  font-size: 36px;
  font-weight: 600;
  line-height: 1;
}

.price-change {
  margin-top: 8px;
  display: flex;
  justify-content: center;
  gap: 16px;
}

.change-amount,
.change-percent {
  font-size: 16px;
  font-weight: 500;
}

.market-status {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 12px;
}

.update-time {
  font-size: 12px;
  color: #909399;
}

/* 价格颜色 */
.price-up {
  color: #f56c6c;
}

.price-down {
  color: #67c23a;
}

.price-neutral {
  color: #606266;
}

/* 指标卡片 */
.metrics-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f2f5;
}

.metric-item:last-child {
  border-bottom: none;
}

.metric-label {
  font-size: 14px;
  color: #909399;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

/* 图表区域 */
.chart-section {
  display: flex;
  flex-direction: column;
}

.chart-card {
  border: none;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  height: fit-content;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  padding: 16px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .detail-content {
    grid-template-columns: 1fr;
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .stock-title {
    margin-left: 0;
    text-align: center;
  }

  .metrics-grid {
    grid-template-columns: 1fr;
  }

  .price {
    font-size: 28px;
  }

  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
}

@media (max-width: 480px) {
  .detail-content {
    padding: 12px;
  }

  .price-change {
    flex-direction: column;
    gap: 8px;
  }

  .header-actions {
    flex-direction: column;
  }
}
</style>
