/**
 * 超简单的Vue入口文件 - 用于调试
 */
import { createApp } from 'vue'

console.log('🚀 启动超简单Vue应用...')

const app = createApp({
  template: `
    <div style="padding: 2rem; text-align: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
      <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 600px; margin: 0 auto;">
        <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
        <p style="color: #6b7280; margin-bottom: 2rem;">Vue应用已成功启动！无路由版本</p>
        
        <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981; margin-bottom: 2rem;">
          <p style="color: #065f46; font-weight: 500;">✅ 基础Vue应用工作正常！</p>
          <p style="color: #047857; font-size: 0.9rem;">当前时间: {{ currentTime }}</p>
        </div>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
          <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #3b82f6;">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📊 仪表盘</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">投资数据概览</p>
          </div>
          <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #10b981;">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">📈 市场行情</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">实时市场数据</p>
          </div>
          <div style="padding: 1rem; background: #f8fafc; border-radius: 8px; border-left: 4px solid #f59e0b;">
            <h3 style="color: #2c3e50; margin-bottom: 0.5rem;">💰 智能交易</h3>
            <p style="color: #6b7280; font-size: 0.9rem;">自动化交易系统</p>
          </div>
        </div>
      </div>
    </div>
  `,
  data() {
    return {
      currentTime: new Date().toLocaleString()
    }
  },
  mounted() {
    console.log('✅ Vue组件已挂载')
    // 更新时间
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  }
})

// 全局错误处理
app.config.errorHandler = (err, instance, info) => {
  console.error('应用错误:', err)
  console.error('错误信息:', info)
}

// 挂载应用
app.mount('#app')
console.log('✅ 超简单Vue应用启动成功')