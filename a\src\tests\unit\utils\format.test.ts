import { describe, it, expect } from 'vitest'
import { formatCurrency, formatPercent, formatNumber, formatDate } from '@/utils/format/financial'

describe('Financial Format Utils', () => {
  describe('formatCurrency', () => {
    it('应该正确格式化货币', () => {
      expect(formatCurrency(1234.56)).toBe('¥1,234.56')
      expect(formatCurrency(1234.56, 'USD')).toBe('$1,234.56')
      expect(formatCurrency(0)).toBe('¥0.00')
      expect(formatCurrency(-1234.56)).toBe('-¥1,234.56')
    })

    it('应该处理大数值', () => {
      expect(formatCurrency(1234567.89)).toBe('¥1,234,567.89')
      expect(formatCurrency(1000000)).toBe('¥1,000,000.00')
    })

    it('应该处理小数精度', () => {
      expect(formatCurrency(1234.5678, 'CNY', 4)).toBe('¥1,234.5678')
      expect(formatCurrency(1234.1, 'CNY', 0)).toBe('¥1,234')
    })
  })

  describe('formatPercent', () => {
    it('应该正确格式化百分比', () => {
      expect(formatPercent(0.1234)).toBe('12.34%')
      expect(formatPercent(0.1)).toBe('10.00%')
      expect(formatPercent(-0.0567)).toBe('-5.67%')
      expect(formatPercent(0)).toBe('0.00%')
    })

    it('应该处理精度', () => {
      expect(formatPercent(0.123456, 4)).toBe('12.3456%')
      expect(formatPercent(0.123456, 0)).toBe('12%')
    })

    it('应该处理极值', () => {
      expect(formatPercent(1)).toBe('100.00%')
      expect(formatPercent(-1)).toBe('-100.00%')
      expect(formatPercent(10)).toBe('1000.00%')
    })
  })

  describe('formatNumber', () => {
    it('应该正确格式化数字', () => {
      expect(formatNumber(1234567)).toBe('1,234,567')
      expect(formatNumber(1234.567)).toBe('1,234.567')
      expect(formatNumber(0)).toBe('0')
      expect(formatNumber(-1234)).toBe('-1,234')
    })

    it('应该处理小数精度', () => {
      expect(formatNumber(1234.5678, 2)).toBe('1,234.57')
      expect(formatNumber(1234.1234, 0)).toBe('1,234')
      expect(formatNumber(1234.9999, 2)).toBe('1,235.00')
    })

    it('应该处理大数值简化', () => {
      expect(formatNumber(1000000, 2, true)).toBe('1.00M')
      expect(formatNumber(1000, 2, true)).toBe('1.00K')
      expect(formatNumber(1000000000, 2, true)).toBe('1.00B')
      expect(formatNumber(500, 2, true)).toBe('500')
    })
  })

  describe('formatDate', () => {
    it('应该正确格式化日期', () => {
      const date = new Date('2024-01-15T10:30:00Z')
      expect(formatDate(date, 'YYYY-MM-DD')).toBe('2024-01-15')
      expect(formatDate(date, 'MM/DD/YYYY')).toBe('01/15/2024')
      expect(formatDate(date, 'DD-MM-YYYY HH:mm')).toMatch(/15-01-2024 \d{2}:\d{2}/)
    })

    it('应该处理时间戳', () => {
      const timestamp = new Date('2024-01-15').getTime()
      expect(formatDate(timestamp, 'YYYY-MM-DD')).toBe('2024-01-15')
    })

    it('应该处理字符串日期', () => {
      expect(formatDate('2024-01-15', 'MM/DD/YYYY')).toBe('01/15/2024')
    })

    it('应该处理无效日期', () => {
      expect(formatDate('invalid-date', 'YYYY-MM-DD')).toBe('Invalid Date')
      expect(formatDate(null, 'YYYY-MM-DD')).toBe('')
    })
  })

  describe('边界情况测试', () => {
    it('应该处理null和undefined', () => {
      expect(formatCurrency(null as any)).toBe('¥0.00')
      expect(formatCurrency(undefined as any)).toBe('¥0.00')
      expect(formatPercent(null as any)).toBe('0.00%')
      expect(formatPercent(undefined as any)).toBe('0.00%')
      expect(formatNumber(null as any)).toBe('0')
      expect(formatNumber(undefined as any)).toBe('0')
    })

    it('应该处理NaN', () => {
      expect(formatCurrency(NaN)).toBe('¥0.00')
      expect(formatPercent(NaN)).toBe('0.00%')
      expect(formatNumber(NaN)).toBe('0')
    })

    it('应该处理Infinity', () => {
      expect(formatCurrency(Infinity)).toBe('¥∞')
      expect(formatCurrency(-Infinity)).toBe('-¥∞')
      expect(formatPercent(Infinity)).toBe('∞%')
      expect(formatNumber(Infinity)).toBe('∞')
    })
  })
})