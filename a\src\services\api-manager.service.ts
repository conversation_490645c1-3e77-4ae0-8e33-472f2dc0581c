/**
 * API管理服务
 * 统一管理Mock数据和真实API的切换
 */

import { appConfig } from '@/config'
import { mockService } from './mock.service'
import { httpClient } from '@/api/http'
import type { ApiResponse, ListResponse } from '@/types/api'

export class ApiManagerService {
  private static instance: ApiManagerService
  private useMock: boolean

  constructor() {
    this.useMock = appConfig.enableMock || import.meta.env.DEV
    console.log(`🔧 API管理器初始化: ${this.useMock ? 'Mock模式' : '真实API模式'}`)
  }

  static getInstance(): ApiManagerService {
    if (!ApiManagerService.instance) {
      ApiManagerService.instance = new ApiManagerService()
    }
    return ApiManagerService.instance
  }

  /**
   * 设置API模式
   */
  setMockMode(useMock: boolean): void {
    this.useMock = useMock
    console.log(`🔄 API模式切换: ${this.useMock ? 'Mock模式' : '真实API模式'}`)
  }

  /**
   * 获取当前API模式
   */
  isMockMode(): boolean {
    return this.useMock
  }

  /**
   * 统一的API调用方法
   * 根据配置自动选择Mock数据或真实API
   */
  async call<T>(
    mockMethod: () => Promise<T>,
    realApiCall: () => Promise<T>,
    fallbackToMock: boolean = true
  ): Promise<T> {
    if (this.useMock) {
      try {
        return await mockMethod()
      } catch (error) {
        console.error('Mock数据调用失败:', error)
        throw error
      }
    }

    try {
      return await realApiCall()
    } catch (error) {
      console.error('真实API调用失败:', error)
      
      if (fallbackToMock) {
        console.warn('🔄 API调用失败，回退到Mock数据')
        try {
          return await mockMethod()
        } catch (mockError) {
          console.error('Mock数据回退也失败:', mockError)
          throw error // 抛出原始API错误
        }
      }
      
      throw error
    }
  }

  /**
   * 带重试的API调用
   */
  async callWithRetry<T>(
    mockMethod: () => Promise<T>,
    realApiCall: () => Promise<T>,
    retries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error

    for (let i = 0; i < retries; i++) {
      try {
        return await this.call(mockMethod, realApiCall, false)
      } catch (error) {
        lastError = error as Error
        
        if (i < retries - 1) {
          console.warn(`API调用失败，${delay}ms后重试 (${i + 1}/${retries})`)
          await new Promise(resolve => setTimeout(resolve, delay))
          delay *= 2 // 指数退避
        }
      }
    }

    // 所有重试都失败，尝试Mock数据
    console.warn('🔄 所有API重试失败，使用Mock数据')
    try {
      return await mockMethod()
    } catch (mockError) {
      console.error('Mock数据也失败:', mockError)
      throw lastError
    }
  }

  /**
   * 批量API调用
   */
  async batchCall<T>(
    calls: Array<{
      mockMethod: () => Promise<T>
      realApiCall: () => Promise<T>
      key: string
    }>
  ): Promise<Record<string, T | Error>> {
    const results: Record<string, T | Error> = {}
    
    const promises = calls.map(async ({ mockMethod, realApiCall, key }) => {
      try {
        const result = await this.call(mockMethod, realApiCall)
        results[key] = result
      } catch (error) {
        results[key] = error as Error
      }
    })

    await Promise.allSettled(promises)
    return results
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    mockAvailable: boolean
    apiAvailable: boolean
    currentMode: 'mock' | 'api'
    recommendation: string
  }> {
    let mockAvailable = false
    let apiAvailable = false

    // 检查Mock服务
    try {
      await mockService.getQuoteData(['000001'])
      mockAvailable = true
    } catch (error) {
      console.warn('Mock服务不可用:', error)
    }

    // 检查真实API
    try {
      await httpClient.get('/health')
      apiAvailable = true
    } catch (error) {
      console.warn('真实API不可用:', error)
    }

    const currentMode = this.useMock ? 'mock' : 'api'
    let recommendation = ''

    if (!apiAvailable && !mockAvailable) {
      recommendation = '所有数据源都不可用，请检查网络连接'
    } else if (!apiAvailable && mockAvailable) {
      recommendation = '真实API不可用，建议使用Mock模式进行开发'
    } else if (apiAvailable && !mockAvailable) {
      recommendation = 'Mock服务不可用，建议使用真实API'
    } else {
      recommendation = currentMode === 'mock' 
        ? '当前使用Mock模式，可切换到真实API获取实时数据'
        : '当前使用真实API，数据为实时数据'
    }

    return {
      mockAvailable,
      apiAvailable,
      currentMode,
      recommendation
    }
  }

  /**
   * 智能模式切换
   * 根据API可用性自动选择最佳模式
   */
  async autoSwitchMode(): Promise<void> {
    const health = await this.healthCheck()
    
    if (health.apiAvailable && !this.useMock) {
      // 当前是API模式且API可用，保持不变
      return
    }
    
    if (health.apiAvailable) {
      // API可用，切换到API模式
      this.setMockMode(false)
      console.log('🔄 自动切换到真实API模式')
    } else if (health.mockAvailable) {
      // API不可用但Mock可用，切换到Mock模式
      this.setMockMode(true)
      console.log('🔄 自动切换到Mock模式')
    } else {
      console.error('❌ 所有数据源都不可用')
    }
  }

  /**
   * 获取API状态信息
   */
  getStatus(): {
    mode: 'mock' | 'api'
    config: {
      enableMock: boolean
      isDev: boolean
      apiBaseUrl: string
    }
  } {
    return {
      mode: this.useMock ? 'mock' : 'api',
      config: {
        enableMock: appConfig.enableMock,
        isDev: appConfig.isDev,
        apiBaseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000/api/v1'
      }
    }
  }
}

// 创建单例实例
export const apiManager = ApiManagerService.getInstance()

// 导出便捷方法
export const callApi = apiManager.call.bind(apiManager)
export const callApiWithRetry = apiManager.callWithRetry.bind(apiManager)
export const batchCallApi = apiManager.batchCall.bind(apiManager)

// 在开发环境下暴露到全局，方便调试
if (import.meta.env.DEV) {
  (window as any).apiManager = apiManager
}
