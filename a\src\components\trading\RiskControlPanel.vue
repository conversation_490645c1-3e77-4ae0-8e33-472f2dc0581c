<template>
  <div class="risk-control-panel">
    <!-- 风险概览 -->
    <div class="risk-overview">
      <el-row :gutter="16">
        <el-col :span="6">
          <div class="risk-metric" :class="getRiskLevelClass(overallRisk.level)">
            <div class="metric-label">整体风险</div>
            <div class="metric-value">{{ overallRisk.level }}</div>
            <div class="metric-score">{{ overallRisk.score }}/100</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="risk-metric">
            <div class="metric-label">风险价值(VaR)</div>
            <div class="metric-value risk-loss">{{ formatCurrency(riskMetrics.var) }}</div>
            <div class="metric-change">95%置信度</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="risk-metric">
            <div class="metric-label">最大回撤</div>
            <div class="metric-value risk-loss">{{ riskMetrics.maxDrawdown.toFixed(2) }}%</div>
            <div class="metric-change">历史最大</div>
          </div>
        </el-col>
        <el-col :span="6">
          <div class="risk-metric">
            <div class="metric-label">夏普比率</div>
            <div class="metric-value" :class="riskMetrics.sharpeRatio >= 1 ? 'risk-good' : 'risk-warning'">
              {{ riskMetrics.sharpeRatio.toFixed(2) }}
            </div>
            <div class="metric-change">风险调整收益</div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 风险设置标签页 -->
    <el-tabs v-model="activeTab" type="card" class="risk-tabs">
      <el-tab-pane label="风控规则" name="rules">
        <div class="risk-rules">
          <div class="rules-header">
            <h3>风控规则配置</h3>
            <el-button type="primary" @click="showAddRule = true">
              <el-icon><Plus /></el-icon>
              添加规则
            </el-button>
          </div>

          <div class="rules-list">
            <div v-for="rule in riskRules" :key="rule.id" class="rule-item" :class="{ disabled: !rule.enabled }">
              <div class="rule-header">
                <div class="rule-info">
                  <el-tag :type="getRuleTypeTag(rule.type)" size="small">{{ rule.type }}</el-tag>
                  <span class="rule-name">{{ rule.name }}</span>
                </div>
                <div class="rule-actions">
                  <el-switch v-model="rule.enabled" @change="toggleRule(rule)" />
                  <el-button size="small" @click="editRule(rule)">编辑</el-button>
                  <el-button size="small" type="danger" @click="deleteRule(rule)">删除</el-button>
                </div>
              </div>
              <div class="rule-description">{{ rule.description }}</div>
              <div class="rule-condition">
                <span class="condition-text">{{ rule.condition }}</span>
                <span class="action-text">→ {{ rule.action }}</span>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="仓位管理" name="position">
        <div class="position-management">
          <div class="position-limits">
            <h3>仓位限制设置</h3>
            <el-form :model="positionLimits" label-width="120px">
              <el-form-item label="最大总仓位">
                <el-input-number
                  v-model="positionLimits.maxTotalPosition"
                  :min="0"
                  :max="100"
                  :step="5"
                  style="width: 200px"
                />
                <span class="unit">%</span>
              </el-form-item>
              <el-form-item label="单股最大仓位">
                <el-input-number
                  v-model="positionLimits.maxSinglePosition"
                  :min="0"
                  :max="50"
                  :step="1"
                  style="width: 200px"
                />
                <span class="unit">%</span>
              </el-form-item>
              <el-form-item label="行业集中度">
                <el-input-number
                  v-model="positionLimits.maxIndustryConcentration"
                  :min="0"
                  :max="100"
                  :step="5"
                  style="width: 200px"
                />
                <span class="unit">%</span>
              </el-form-item>
              <el-form-item label="止损比例">
                <el-input-number
                  v-model="positionLimits.stopLossPercent"
                  :min="0"
                  :max="20"
                  :step="0.5"
                  :precision="1"
                  style="width: 200px"
                />
                <span class="unit">%</span>
              </el-form-item>
            </el-form>
          </div>

          <div class="position-analysis">
            <h3>当前仓位分析</h3>
            <div class="analysis-charts">
              <div class="chart-item">
                <div class="chart-title">行业分布</div>
                <div class="mock-pie-chart">
                  <div class="pie-center">行业分布</div>
                </div>
              </div>
              <div class="chart-item">
                <div class="chart-title">仓位集中度</div>
                <div class="concentration-bars">
                  <div v-for="item in concentrationData" :key="item.name" class="bar-item">
                    <span class="bar-label">{{ item.name }}</span>
                    <div class="bar-container">
                      <div class="bar-fill" :style="{ width: item.percent + '%' }"></div>
                    </div>
                    <span class="bar-value">{{ item.percent }}%</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="风险监控" name="monitor">
        <div class="risk-monitoring">
          <div class="monitoring-settings">
            <h3>监控设置</h3>
            <el-form :model="monitorSettings" label-width="120px">
              <el-form-item label="监控频率">
                <el-select v-model="monitorSettings.frequency" style="width: 200px">
                  <el-option label="实时" value="realtime" />
                  <el-option label="1分钟" value="1min" />
                  <el-option label="5分钟" value="5min" />
                  <el-option label="15分钟" value="15min" />
                </el-select>
              </el-form-item>
              <el-form-item label="预警阈值">
                <el-input-number
                  v-model="monitorSettings.alertThreshold"
                  :min="0"
                  :max="100"
                  :step="5"
                  style="width: 200px"
                />
                <span class="unit">风险分</span>
              </el-form-item>
              <el-form-item label="邮件通知">
                <el-switch v-model="monitorSettings.emailAlert" />
              </el-form-item>
              <el-form-item label="短信通知">
                <el-switch v-model="monitorSettings.smsAlert" />
              </el-form-item>
            </el-form>
          </div>

          <div class="risk-alerts">
            <h3>风险警报历史</h3>
            <div class="alerts-list">
              <div v-for="alert in riskAlerts" :key="alert.id" class="alert-item" :class="alert.level">
                <div class="alert-time">{{ formatDateTime(alert.timestamp) }}</div>
                <div class="alert-content">
                  <div class="alert-title">{{ alert.title }}</div>
                  <div class="alert-message">{{ alert.message }}</div>
                </div>
                <div class="alert-actions">
                  <el-tag :type="getAlertTypeTag(alert.level)" size="small">{{ alert.level }}</el-tag>
                  <el-button size="small" @click="viewAlertDetail(alert)">详情</el-button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <el-tab-pane label="压力测试" name="stress">
        <div class="stress-testing">
          <div class="stress-scenarios">
            <h3>压力测试场景</h3>
            <el-row :gutter="16">
              <el-col :span="8">
                <el-card class="scenario-card">
                  <div class="scenario-title">市场崩盘</div>
                  <div class="scenario-description">模拟股市大跌20%的情况</div>
                  <div class="scenario-result">
                    <div class="result-item">
                      <span class="label">预计损失:</span>
                      <span class="value loss">-15.2%</span>
                    </div>
                    <div class="result-item">
                      <span class="label">最大回撤:</span>
                      <span class="value loss">-18.5%</span>
                    </div>
                  </div>
                  <el-button @click="runStressTest('crash')" size="small" type="danger" block>
                    运行测试
                  </el-button>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="scenario-card">
                  <div class="scenario-title">利率冲击</div>
                  <div class="scenario-description">央行加息200个基点</div>
                  <div class="scenario-result">
                    <div class="result-item">
                      <span class="label">预计损失:</span>
                      <span class="value loss">-8.7%</span>
                    </div>
                    <div class="result-item">
                      <span class="label">最大回撤:</span>
                      <span class="value loss">-11.2%</span>
                    </div>
                  </div>
                  <el-button @click="runStressTest('interest')" size="small" type="warning" block>
                    运行测试
                  </el-button>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card class="scenario-card">
                  <div class="scenario-title">流动性危机</div>
                  <div class="scenario-description">市场流动性严重不足</div>
                  <div class="scenario-result">
                    <div class="result-item">
                      <span class="label">预计损失:</span>
                      <span class="value loss">-12.3%</span>
                    </div>
                    <div class="result-item">
                      <span class="label">最大回撤:</span>
                      <span class="value loss">-16.8%</span>
                    </div>
                  </div>
                  <el-button @click="runStressTest('liquidity')" size="small" type="info" block>
                    运行测试
                  </el-button>
                </el-card>
              </el-col>
            </el-row>
          </div>

          <div class="stress-results">
            <h3>历史测试结果</h3>
            <el-table :data="stressTestResults" stripe>
              <el-table-column prop="date" label="测试日期" width="120" />
              <el-table-column prop="scenario" label="测试场景" width="120" />
              <el-table-column prop="portfolioValue" label="组合价值" width="120">
                <template #default="{ row }">{{ formatCurrency(row.portfolioValue) }}</template>
              </el-table-column>
              <el-table-column prop="loss" label="预计损失" width="100">
                <template #default="{ row }">
                  <span class="loss">{{ row.loss.toFixed(2) }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="maxDrawdown" label="最大回撤" width="100">
                <template #default="{ row }">
                  <span class="loss">{{ row.maxDrawdown.toFixed(2) }}%</span>
                </template>
              </el-table-column>
              <el-table-column prop="duration" label="测试时长" width="100" />
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button size="small" @click="viewTestDetail(row)">查看详情</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>

    <!-- 添加规则弹窗 -->
    <el-dialog v-model="showAddRule" title="添加风控规则" width="600px">
      <RiskRuleForm
        @rule-created="handleRuleCreated"
        @cancel="showAddRule = false"
      />
    </el-dialog>

    <!-- 编辑规则弹窗 -->
    <el-dialog v-model="showEditRule" title="编辑风控规则" width="600px">
      <RiskRuleForm
        v-if="editingRule"
        :rule="editingRule"
        @rule-updated="handleRuleUpdated"
        @cancel="showEditRule = false"
      />
    </el-dialog>

    <!-- 警报详情弹窗 -->
    <el-dialog v-model="showAlertDetail" title="风险警报详情" width="800px">
      <AlertDetailPanel
        v-if="selectedAlert"
        :alert="selectedAlert"
        @close="showAlertDetail = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'

// Props
interface Props {
  account: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'settings-update': [settings: any]
  'close': []
}>()

// 状态数据
const activeTab = ref('rules')
const showAddRule = ref(false)
const showEditRule = ref(false)
const showAlertDetail = ref(false)
const editingRule = ref(null)
const selectedAlert = ref(null)

// 整体风险评估
const overallRisk = reactive({
  level: '中等',
  score: 68,
  trend: 'stable'
})

// 风险指标
const riskMetrics = reactive({
  var: -50000, // 风险价值
  maxDrawdown: -12.5, // 最大回撤
  sharpeRatio: 1.32, // 夏普比率
  beta: 0.85 // 贝塔系数
})

// 风控规则
const riskRules = ref([
  {
    id: 'rule_001',
    name: '单日亏损限制',
    type: '损失控制',
    enabled: true,
    description: '当日亏损超过账户资金的2%时自动停止交易',
    condition: '当日亏损 > 2%',
    action: '停止所有交易'
  },
  {
    id: 'rule_002',
    name: '单股持仓限制',
    type: '仓位控制',
    enabled: true,
    description: '单只股票持仓不超过总资产的10%',
    condition: '单股仓位 > 10%',
    action: '禁止继续买入'
  },
  {
    id: 'rule_003',
    name: '连续亏损保护',
    type: '损失控制',
    enabled: false,
    description: '连续5笔交易亏损后暂停交易30分钟',
    condition: '连续亏损 >= 5笔',
    action: '暂停交易30分钟'
  }
])

// 仓位限制设置
const positionLimits = reactive({
  maxTotalPosition: 80, // 最大总仓位
  maxSinglePosition: 10, // 单股最大仓位
  maxIndustryConcentration: 30, // 行业集中度
  stopLossPercent: 5.0 // 止损比例
})

// 监控设置
const monitorSettings = reactive({
  frequency: 'realtime',
  alertThreshold: 70,
  emailAlert: true,
  smsAlert: false
})

// 集中度数据
const concentrationData = ref([
  { name: '金融', percent: 25 },
  { name: '科技', percent: 35 },
  { name: '消费', percent: 20 },
  { name: '医药', percent: 15 },
  { name: '其他', percent: 5 }
])

// 风险警报
const riskAlerts = ref([
  {
    id: 'alert_001',
    timestamp: Date.now() - 3600000,
    level: '高风险',
    title: '仓位过度集中',
    message: '科技股持仓占比达到35%，超过设定阈值30%'
  },
  {
    id: 'alert_002',
    timestamp: Date.now() - 7200000,
    level: '中风险',
    title: '单日亏损接近限额',
    message: '当日亏损1.8%，接近2%的止损线'
  },
  {
    id: 'alert_003',
    timestamp: Date.now() - 10800000,
    level: '低风险',
    title: '波动率异常',
    message: '组合波动率较历史平均水平上升20%'
  }
])

// 压力测试结果
const stressTestResults = ref([
  {
    date: '2024-01-15',
    scenario: '市场崩盘',
    portfolioValue: 950000,
    loss: -15.2,
    maxDrawdown: -18.5,
    duration: '2.3秒'
  },
  {
    date: '2024-01-10',
    scenario: '利率冲击',
    portfolioValue: 980000,
    loss: -8.7,
    maxDrawdown: -11.2,
    duration: '1.8秒'
  },
  {
    date: '2024-01-05',
    scenario: '流动性危机',
    portfolioValue: 965000,
    loss: -12.3,
    maxDrawdown: -16.8,
    duration: '3.1秒'
  }
])

// 计算属性
const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0
  }).format(amount)
}

const formatDateTime = (timestamp: number) => {
  return new Date(timestamp).toLocaleString('zh-CN')
}

// 方法
const getRiskLevelClass = (level: string) => {
  const classes: Record<string, string> = {
    '低风险': 'risk-low',
    '中等': 'risk-medium',
    '高风险': 'risk-high'
  }
  return classes[level] || 'risk-medium'
}

const getRuleTypeTag = (type: string) => {
  const types: Record<string, string> = {
    '损失控制': 'danger',
    '仓位控制': 'warning',
    '流动性控制': 'info'
  }
  return types[type] || 'default'
}

const getAlertTypeTag = (level: string) => {
  const types: Record<string, string> = {
    '高风险': 'danger',
    '中风险': 'warning',
    '低风险': 'info'
  }
  return types[level] || 'default'
}

const toggleRule = (rule: any) => {
  const action = rule.enabled ? '启用' : '禁用'
  ElMessage.success(`风控规则 "${rule.name}" 已${action}`)
}

const editRule = (rule: any) => {
  editingRule.value = { ...rule }
  showEditRule.value = true
}

const deleteRule = async (rule: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除风控规则 "${rule.name}" 吗？`,
      '删除确认',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = riskRules.value.findIndex(r => r.id === rule.id)
    if (index !== -1) {
      riskRules.value.splice(index, 1)
      ElMessage.success('风控规则删除成功')
    }
  } catch (error) {
    // 用户取消
  }
}

const runStressTest = async (scenario: string) => {
  ElMessage.info(`正在运行${scenario}压力测试...`)
  
  // 模拟测试过程
  await new Promise(resolve => setTimeout(resolve, 2000))
  
  ElMessage.success('压力测试完成')
}

const viewTestDetail = (test: any) => {
  ElMessage.info('查看测试详情功能开发中')
}

const viewAlertDetail = (alert: any) => {
  selectedAlert.value = alert
  showAlertDetail.value = true
}

const handleRuleCreated = (rule: any) => {
  riskRules.value.push(rule)
  showAddRule.value = false
  ElMessage.success('风控规则创建成功')
}

const handleRuleUpdated = (updatedRule: any) => {
  const index = riskRules.value.findIndex(r => r.id === updatedRule.id)
  if (index !== -1) {
    riskRules.value[index] = updatedRule
  }
  showEditRule.value = false
  ElMessage.success('风控规则更新成功')
}

// 生命周期
onMounted(() => {
  console.log('风险控制面板已加载')
})
</script>

<style scoped lang="scss">
.risk-control-panel {
  .risk-overview {
    margin-bottom: 20px;
    
    .risk-metric {
      background: white;
      border-radius: 8px;
      padding: 16px;
      text-align: center;
      border: 2px solid #e4e7ed;
      
      &.risk-low {
        border-color: #67c23a;
      }
      
      &.risk-medium {
        border-color: #e6a23c;
      }
      
      &.risk-high {
        border-color: #f56c6c;
      }
      
      .metric-label {
        font-size: 14px;
        color: #606266;
        margin-bottom: 8px;
      }
      
      .metric-value {
        font-size: 24px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 4px;
        
        &.risk-good {
          color: #67c23a;
        }
        
        &.risk-warning {
          color: #e6a23c;
        }
        
        &.risk-loss {
          color: #f56c6c;
        }
      }
      
      .metric-change, .metric-score {
        font-size: 12px;
        color: #909399;
      }
    }
  }
  
  .risk-tabs {
    .rules-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 20px;
      
      h3 {
        margin: 0;
        color: #303133;
      }
    }
    
    .rule-item {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 16px;
      margin-bottom: 12px;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.disabled {
        opacity: 0.6;
        background: #f5f7fa;
      }
      
      .rule-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;
        
        .rule-info {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .rule-name {
            font-weight: 600;
            color: #303133;
          }
        }
        
        .rule-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
      
      .rule-description {
        color: #606266;
        margin-bottom: 8px;
        font-size: 14px;
      }
      
      .rule-condition {
        display: flex;
        align-items: center;
        gap: 8px;
        font-size: 13px;
        
        .condition-text {
          background: #f0f9ff;
          color: #409eff;
          padding: 4px 8px;
          border-radius: 4px;
        }
        
        .action-text {
          background: #fef0f0;
          color: #f56c6c;
          padding: 4px 8px;
          border-radius: 4px;
        }
      }
    }
    
    .position-limits {
      margin-bottom: 24px;
      
      .unit {
        margin-left: 8px;
        color: #909399;
      }
    }
    
    .analysis-charts {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 20px;
      
      .chart-item {
        .chart-title {
          font-weight: 600;
          margin-bottom: 12px;
          color: #303133;
        }
        
        .mock-pie-chart {
          width: 200px;
          height: 200px;
          border-radius: 50%;
          background: conic-gradient(
            #409eff 0deg 90deg,
            #67c23a 90deg 216deg,
            #e6a23c 216deg 288deg,
            #f56c6c 288deg 342deg,
            #909399 342deg 360deg
          );
          display: flex;
          align-items: center;
          justify-content: center;
          margin: 0 auto;
          
          .pie-center {
            width: 100px;
            height: 100px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            color: #303133;
          }
        }
        
        .concentration-bars {
          .bar-item {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 8px;
            
            .bar-label {
              width: 60px;
              font-size: 14px;
              color: #606266;
            }
            
            .bar-container {
              flex: 1;
              height: 16px;
              background: #f0f0f0;
              border-radius: 8px;
              overflow: hidden;
              
              .bar-fill {
                height: 100%;
                background: linear-gradient(90deg, #409eff, #67c23a);
                transition: width 0.3s;
              }
            }
            
            .bar-value {
              width: 40px;
              text-align: right;
              font-size: 12px;
              color: #606266;
            }
          }
        }
      }
    }
    
    .alerts-list {
      .alert-item {
        display: flex;
        align-items: center;
        gap: 16px;
        padding: 12px;
        border: 1px solid #e4e7ed;
        border-radius: 6px;
        margin-bottom: 8px;
        
        &.高风险 {
          border-left: 4px solid #f56c6c;
        }
        
        &.中风险 {
          border-left: 4px solid #e6a23c;
        }
        
        &.低风险 {
          border-left: 4px solid #909399;
        }
        
        .alert-time {
          width: 150px;
          font-size: 12px;
          color: #909399;
        }
        
        .alert-content {
          flex: 1;
          
          .alert-title {
            font-weight: 600;
            color: #303133;
            margin-bottom: 4px;
          }
          
          .alert-message {
            font-size: 14px;
            color: #606266;
          }
        }
        
        .alert-actions {
          display: flex;
          align-items: center;
          gap: 8px;
        }
      }
    }
    
    .scenario-card {
      text-align: center;
      
      .scenario-title {
        font-size: 16px;
        font-weight: 600;
        color: #303133;
        margin-bottom: 8px;
      }
      
      .scenario-description {
        font-size: 14px;
        color: #606266;
        margin-bottom: 16px;
      }
      
      .scenario-result {
        margin-bottom: 16px;
        
        .result-item {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .label {
            color: #606266;
          }
          
          .value {
            font-weight: 600;
            
            &.loss {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }
}

.loss {
  color: #f56c6c;
}
</style>