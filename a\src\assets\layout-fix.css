/* 页面布局修复样式 */
/* 解决页面滚动和高度限制问题 */

/* 基础HTML和Body设置 - 强制启用滚动 */
html {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
  overflow: visible !important;
  overflow-x: hidden !important;
  overflow-y: scroll !important;
}

body {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
  overflow: visible !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}

/* App容器修复 */
#app, #app-wrapper {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 主布局修复 */
.default-layout {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 内容区域修复 */
.layout-content {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 市场页面特定修复 */
.market-view-optimized {
  height: auto !important;
  min-height: 100vh !important;
  max-height: none !important;
  overflow: visible !important;
}

.market-content {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  overflow: visible !important;
  overflow-x: hidden !important;
  overflow-y: visible !important;
}

.sidebar-container {
  height: auto !important;
  min-height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* Element Plus组件修复 */
.el-table {
  height: auto !important;
  max-height: none !important;
}

.el-table__body-wrapper {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

.el-card {
  height: auto !important;
  max-height: none !important;
}

.el-card__body {
  height: auto !important;
  max-height: none !important;
  overflow: visible !important;
}

/* 确保内容可以正常展开 */
.loading-container,
.error-container {
  min-height: 50vh !important;
  height: auto !important;
}

/* 响应式修复 */
@media (max-width: 768px) {
  .market-view-optimized {
    min-height: 100vh !important;
  }

  .market-content {
    padding: 10px !important;
  }
}
