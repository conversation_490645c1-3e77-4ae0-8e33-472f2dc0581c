#!/usr/bin/env node

/**
 * Bundle Analyzer Script
 * 分析打包结果，确保首屏JS ≤ 250KB
 */

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const chalk = require('chalk')

// 配置
const config = {
  distDir: path.join(__dirname, '../dist'),
  maxFirstLoadSize: 250 * 1024, // 250KB
  maxChunkSize: 500 * 1024, // 500KB
  maxAssetSize: 1024 * 1024, // 1MB
}

// 工具函数
const formatBytes = (bytes) => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getFileSize = (filePath) => {
  try {
    return fs.statSync(filePath).size
  } catch (error) {
    return 0
  }
}

const getGzipSize = (filePath) => {
  try {
    const gzipPath = filePath + '.gz'
    if (fs.existsSync(gzipPath)) {
      return fs.statSync(gzipPath).size
    }
  } catch (error) {
    // 如果没有gzip文件，计算估算大小
    const originalSize = getFileSize(filePath)
    return Math.round(originalSize * 0.3) // 估算gzip压缩率30%
  }
  return 0
}

// 分析函数
const analyzeBundle = () => {
  console.log(chalk.blue('🔍 Starting bundle analysis...\n'))

  if (!fs.existsSync(config.distDir)) {
    console.error(chalk.red('❌ Dist directory not found. Please run build first.'))
    process.exit(1)
  }

  const assetsDir = path.join(config.distDir, 'assets')
  const jsDir = path.join(assetsDir, 'js')
  const cssDir = path.join(assetsDir, 'css')

  const results = {
    js: [],
    css: [],
    firstLoad: [],
    warnings: [],
    errors: []
  }

  // 分析JS文件
  if (fs.existsSync(jsDir)) {
    const jsFiles = fs.readdirSync(jsDir).filter(file => file.endsWith('.js'))
    
    jsFiles.forEach(file => {
      const filePath = path.join(jsDir, file)
      const size = getFileSize(filePath)
      const gzipSize = getGzipSize(filePath)
      
      const fileInfo = {
        name: file,
        size,
        gzipSize,
        path: filePath,
        isEntry: file.includes('main-') || file.includes('index-'),
        isVendor: file.includes('vue-') || file.includes('element-') || file.includes('vendor'),
        isChunk: !file.includes('main-') && !file.includes('index-')
      }

      results.js.push(fileInfo)

      // 检查首屏加载文件
      if (fileInfo.isEntry) {
        results.firstLoad.push(fileInfo)
      }

      // 检查文件大小警告
      if (size > config.maxChunkSize) {
        results.warnings.push({
          type: 'large-chunk',
          file: file,
          size: size,
          message: `Chunk size (${formatBytes(size)}) exceeds recommended limit (${formatBytes(config.maxChunkSize)})`
        })
      }
    })
  }

  // 分析CSS文件
  if (fs.existsSync(cssDir)) {
    const cssFiles = fs.readdirSync(cssDir).filter(file => file.endsWith('.css'))
    
    cssFiles.forEach(file => {
      const filePath = path.join(cssDir, file)
      const size = getFileSize(filePath)
      const gzipSize = getGzipSize(filePath)
      
      const fileInfo = {
        name: file,
        size,
        gzipSize,
        path: filePath,
        isEntry: file.includes('main-') || file.includes('index-')
      }

      results.css.push(fileInfo)

      if (fileInfo.isEntry) {
        results.firstLoad.push(fileInfo)
      }
    })
  }

  return results
}

// 生成报告
const generateReport = (results) => {
  console.log(chalk.green('📊 Bundle Analysis Report\n'))

  // 首屏加载分析
  const firstLoadSize = results.firstLoad.reduce((sum, file) => sum + file.size, 0)
  const firstLoadGzipSize = results.firstLoad.reduce((sum, file) => sum + file.gzipSize, 0)

  console.log(chalk.yellow('🚀 First Load Analysis:'))
  console.log(`Total first load size: ${formatBytes(firstLoadSize)} (${formatBytes(firstLoadGzipSize)} gzipped)`)
  
  if (firstLoadSize > config.maxFirstLoadSize) {
    console.log(chalk.red(`❌ First load size exceeds limit (${formatBytes(config.maxFirstLoadSize)})`))
    results.errors.push({
      type: 'first-load-too-large',
      size: firstLoadSize,
      limit: config.maxFirstLoadSize
    })
  } else {
    console.log(chalk.green(`✅ First load size within limit`))
  }

  console.log('\nFirst load files:')
  results.firstLoad.forEach(file => {
    console.log(`  ${file.name}: ${formatBytes(file.size)} (${formatBytes(file.gzipSize)} gzipped)`)
  })

  // JS文件分析
  console.log(chalk.yellow('\n📦 JavaScript Bundles:'))
  const sortedJs = results.js.sort((a, b) => b.size - a.size)
  
  sortedJs.forEach(file => {
    const sizeColor = file.size > config.maxChunkSize ? 'red' : 'green'
    const typeLabel = file.isEntry ? '[ENTRY]' : file.isVendor ? '[VENDOR]' : '[CHUNK]'
    
    console.log(`  ${typeLabel} ${file.name}: ${chalk[sizeColor](formatBytes(file.size))} (${formatBytes(file.gzipSize)} gzipped)`)
  })

  // CSS文件分析
  if (results.css.length > 0) {
    console.log(chalk.yellow('\n🎨 CSS Bundles:'))
    const sortedCss = results.css.sort((a, b) => b.size - a.size)
    
    sortedCss.forEach(file => {
      const typeLabel = file.isEntry ? '[ENTRY]' : '[CHUNK]'
      console.log(`  ${typeLabel} ${file.name}: ${formatBytes(file.size)} (${formatBytes(file.gzipSize)} gzipped)`)
    })
  }

  // 警告和错误
  if (results.warnings.length > 0) {
    console.log(chalk.yellow('\n⚠️  Warnings:'))
    results.warnings.forEach(warning => {
      console.log(`  ${warning.message}`)
    })
  }

  if (results.errors.length > 0) {
    console.log(chalk.red('\n❌ Errors:'))
    results.errors.forEach(error => {
      console.log(`  ${error.type}: ${error.message || 'Check the details above'}`)
    })
  }

  // 总结
  const totalJs = results.js.reduce((sum, file) => sum + file.size, 0)
  const totalCss = results.css.reduce((sum, file) => sum + file.size, 0)
  const totalSize = totalJs + totalCss

  console.log(chalk.blue('\n📈 Summary:'))
  console.log(`Total JavaScript: ${formatBytes(totalJs)}`)
  console.log(`Total CSS: ${formatBytes(totalCss)}`)
  console.log(`Total bundle size: ${formatBytes(totalSize)}`)
  console.log(`Number of chunks: ${results.js.length + results.css.length}`)

  return results.errors.length === 0
}

// 生成优化建议
const generateOptimizationSuggestions = (results) => {
  console.log(chalk.blue('\n💡 Optimization Suggestions:\n'))

  const largeChunks = results.js.filter(file => file.size > config.maxChunkSize)
  const vendorChunks = results.js.filter(file => file.isVendor)
  
  if (largeChunks.length > 0) {
    console.log(chalk.yellow('📦 Large Chunks:'))
    largeChunks.forEach(chunk => {
      console.log(`  - ${chunk.name} (${formatBytes(chunk.size)})`)
      
      if (chunk.name.includes('element-')) {
        console.log('    💡 Consider splitting Element Plus components further')
      }
      if (chunk.name.includes('echarts-')) {
        console.log('    💡 Consider using ECharts on-demand loading')
      }
      if (chunk.name.includes('vendor')) {
        console.log('    💡 Consider splitting vendor libraries into smaller chunks')
      }
    })
  }

  if (vendorChunks.length > 0) {
    console.log(chalk.yellow('\n📚 Vendor Analysis:'))
    const totalVendorSize = vendorChunks.reduce((sum, chunk) => sum + chunk.size, 0)
    console.log(`Total vendor size: ${formatBytes(totalVendorSize)}`)
    
    vendorChunks.forEach(chunk => {
      console.log(`  - ${chunk.name}: ${formatBytes(chunk.size)}`)
    })
  }

  console.log(chalk.green('\n🎯 Recommended Actions:'))
  console.log('  1. Enable route-based code splitting for all pages')
  console.log('  2. Use dynamic imports for heavy components')
  console.log('  3. Consider lazy loading for non-critical features')
  console.log('  4. Optimize image assets and use WebP format')
  console.log('  5. Enable Brotli compression on server')
  console.log('  6. Implement service worker for caching')
}

// 主函数
const main = () => {
  try {
    const results = analyzeBundle()
    const success = generateReport(results)
    generateOptimizationSuggestions(results)

    // 打开可视化分析页面
    const analysisFile = path.join(config.distDir, 'bundle-analysis.html')
    if (fs.existsSync(analysisFile)) {
      console.log(chalk.blue(`\n🔍 Visual analysis available at: ${analysisFile}`))
    }

    if (!success) {
      console.log(chalk.red('\n❌ Bundle analysis failed. Please fix the errors above.'))
      process.exit(1)
    } else {
      console.log(chalk.green('\n✅ Bundle analysis completed successfully!'))
    }

  } catch (error) {
    console.error(chalk.red('❌ Bundle analysis failed:'), error.message)
    process.exit(1)
  }
}

// 运行
if (require.main === module) {
  main()
}

module.exports = {
  analyzeBundle,
  generateReport,
  generateOptimizationSuggestions
} 