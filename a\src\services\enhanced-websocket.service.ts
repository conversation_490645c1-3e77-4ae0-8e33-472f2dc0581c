/**
 * 增强型WebSocket服务
 * 提供自动重连、心跳检测、消息队列、数据压缩等功能
 */

export enum MessageType {
  // 系统消息
  PING = 'ping',
  PONG = 'pong',
  CONNECT = 'connect',
  DISCONNECT = 'disconnect',
  ERROR = 'error',
  
  // 市场数据
  MARKET_DATA = 'market_data',
  TICK_DATA = 'tick_data',
  KLINE_DATA = 'kline_data',
  ORDER_BOOK = 'order_book',
  
  // 交易消息
  ORDER_UPDATE = 'order_update',
  TRADE_UPDATE = 'trade_update',
  POSITION_UPDATE = 'position_update',
  ACCOUNT_UPDATE = 'account_update',
  
  // 风险控制
  RISK_ALERT = 'risk_alert',
  POSITION_RISK = 'position_risk',
  
  // 策略消息
  STRATEGY_UPDATE = 'strategy_update',
  STRATEGY_SIGNAL = 'strategy_signal'
}

export enum Channel {
  MARKET = 'market',
  TRADING = 'trading',
  RISK = 'risk',
  STRATEGY = 'strategy',
  SYSTEM = 'system'
}

export interface WebSocketMessage {
  type: MessageType
  channel: Channel
  data: any
  timestamp?: number
  id?: string
  compressed?: boolean
}

export interface SubscriptionOptions {
  symbols?: string[]
  interval?: string
  depth?: number
  throttle?: number // 节流间隔(ms)
}

export interface ConnectionConfig {
  url: string
  reconnectInterval?: number
  maxReconnectAttempts?: number
  heartbeatInterval?: number
  enableCompression?: boolean
  enableQueueing?: boolean
  maxQueueSize?: number
}

type MessageHandler = (message: WebSocketMessage) => void
type ErrorHandler = (error: Event) => void
type ConnectionHandler = () => void

export class EnhancedWebSocketService {
  private ws: WebSocket | null = null
  private config: Required<ConnectionConfig>
  private isConnected = false
  private isReconnecting = false
  private reconnectAttempts = 0
  private heartbeatTimer: NodeJS.Timeout | null = null
  private messageQueue: WebSocketMessage[] = []
  private subscriptions = new Map<string, SubscriptionOptions>()
  private messageHandlers = new Map<string, Set<MessageHandler>>()
  private errorHandlers = new Set<ErrorHandler>()
  private connectHandlers = new Set<ConnectionHandler>()
  private disconnectHandlers = new Set<ConnectionHandler>()
  private lastPingTime = 0
  private latency = 0

  constructor(config: ConnectionConfig) {
    this.config = {
      reconnectInterval: 3000,
      maxReconnectAttempts: 10,
      heartbeatInterval: 30000,
      enableCompression: true,
      enableQueueing: true,
      maxQueueSize: 1000,
      ...config
    }
  }

  /**
   * 连接WebSocket
   */
  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnected) {
        resolve()
        return
      }

      try {
        this.ws = new WebSocket(this.config.url)
        
        this.ws.onopen = () => {
          this.isConnected = true
          this.isReconnecting = false
          this.reconnectAttempts = 0
          
          console.log('WebSocket连接成功')
          this.startHeartbeat()
          this.processMessageQueue()
          this.resubscribeAll()
          
          this.connectHandlers.forEach(handler => handler())
          resolve()
        }

        this.ws.onmessage = (event) => {
          this.handleMessage(event)
        }

        this.ws.onclose = (event) => {
          this.handleDisconnection(event)
        }

        this.ws.onerror = (error) => {
          console.error('WebSocket错误:', error)
          this.errorHandlers.forEach(handler => handler(error))
          reject(error)
        }

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 断开连接
   */
  disconnect(): void {
    this.isConnected = false
    this.stopHeartbeat()
    
    if (this.ws) {
      this.ws.close()
      this.ws = null
    }
    
    this.disconnectHandlers.forEach(handler => handler())
  }

  /**
   * 发送消息
   */
  send(message: WebSocketMessage): boolean {
    if (!this.isConnected || !this.ws) {
      if (this.config.enableQueueing) {
        this.queueMessage(message)
      }
      return false
    }

    try {
      const messageString = this.serializeMessage(message)
      this.ws.send(messageString)
      return true
    } catch (error) {
      console.error('发送消息失败:', error)
      return false
    }
  }

  /**
   * 订阅频道
   */
  subscribe(channel: Channel, type: MessageType, options: SubscriptionOptions = {}, handler?: MessageHandler): void {
    const key = `${channel}:${type}`
    this.subscriptions.set(key, options)
    
    if (handler) {
      this.on(key, handler)
    }

    // 发送订阅消息
    this.send({
      type: MessageType.CONNECT,
      channel,
      data: {
        action: 'subscribe',
        type,
        ...options
      }
    })
  }

  /**
   * 取消订阅
   */
  unsubscribe(channel: Channel, type: MessageType): void {
    const key = `${channel}:${type}`
    this.subscriptions.delete(key)
    this.messageHandlers.delete(key)

    // 发送取消订阅消息
    this.send({
      type: MessageType.DISCONNECT,
      channel,
      data: {
        action: 'unsubscribe',
        type
      }
    })
  }

  /**
   * 订阅市场数据
   */
  subscribeMarketData(symbols: string[], handler?: MessageHandler): void {
    this.subscribe(Channel.MARKET, MessageType.MARKET_DATA, { symbols }, handler)
  }

  /**
   * 订阅Tick数据
   */
  subscribeTickData(symbols: string[], throttle = 100, handler?: MessageHandler): void {
    this.subscribe(Channel.MARKET, MessageType.TICK_DATA, { symbols, throttle }, handler)
  }

  /**
   * 订阅K线数据
   */
  subscribeKlineData(symbols: string[], interval = '1m', handler?: MessageHandler): void {
    this.subscribe(Channel.MARKET, MessageType.KLINE_DATA, { symbols, interval }, handler)
  }

  /**
   * 订阅订单簿数据
   */
  subscribeOrderBook(symbols: string[], depth = 10, handler?: MessageHandler): void {
    this.subscribe(Channel.MARKET, MessageType.ORDER_BOOK, { symbols, depth }, handler)
  }

  /**
   * 订阅交易更新
   */
  subscribeTradingUpdates(handler?: MessageHandler): void {
    this.subscribe(Channel.TRADING, MessageType.ORDER_UPDATE, {}, handler)
    this.subscribe(Channel.TRADING, MessageType.TRADE_UPDATE, {}, handler)
    this.subscribe(Channel.TRADING, MessageType.POSITION_UPDATE, {}, handler)
    this.subscribe(Channel.TRADING, MessageType.ACCOUNT_UPDATE, {}, handler)
  }

  /**
   * 订阅风险警报
   */
  subscribeRiskAlerts(handler?: MessageHandler): void {
    this.subscribe(Channel.RISK, MessageType.RISK_ALERT, {}, handler)
    this.subscribe(Channel.RISK, MessageType.POSITION_RISK, {}, handler)
  }

  /**
   * 订阅策略更新
   */
  subscribeStrategyUpdates(handler?: MessageHandler): void {
    this.subscribe(Channel.STRATEGY, MessageType.STRATEGY_UPDATE, {}, handler)
    this.subscribe(Channel.STRATEGY, MessageType.STRATEGY_SIGNAL, {}, handler)
  }

  /**
   * 添加消息处理器
   */
  on(event: string, handler: MessageHandler): void {
    if (!this.messageHandlers.has(event)) {
      this.messageHandlers.set(event, new Set())
    }
    this.messageHandlers.get(event)!.add(handler)
  }

  /**
   * 移除消息处理器
   */
  off(event: string, handler: MessageHandler): void {
    const handlers = this.messageHandlers.get(event)
    if (handlers) {
      handlers.delete(handler)
      if (handlers.size === 0) {
        this.messageHandlers.delete(event)
      }
    }
  }

  /**
   * 添加错误处理器
   */
  onError(handler: ErrorHandler): void {
    this.errorHandlers.add(handler)
  }

  /**
   * 添加连接处理器
   */
  onConnect(handler: ConnectionHandler): void {
    this.connectHandlers.add(handler)
  }

  /**
   * 添加断开连接处理器
   */
  onDisconnect(handler: ConnectionHandler): void {
    this.disconnectHandlers.add(handler)
  }

  /**
   * 获取连接状态
   */
  getConnectionState(): {
    connected: boolean
    reconnecting: boolean
    attempts: number
    latency: number
  } {
    return {
      connected: this.isConnected,
      reconnecting: this.isReconnecting,
      attempts: this.reconnectAttempts,
      latency: this.latency
    }
  }

  /**
   * 获取订阅列表
   */
  getSubscriptions(): Map<string, SubscriptionOptions> {
    return new Map(this.subscriptions)
  }

  /**
   * 处理接收到的消息
   */
  private handleMessage(event: MessageEvent): void {
    try {
      const message = this.deserializeMessage(event.data)
      
      // 处理心跳响应
      if (message.type === MessageType.PONG) {
        this.latency = Date.now() - this.lastPingTime
        return
      }

      // 分发消息到处理器
      const key = `${message.channel}:${message.type}`
      const handlers = this.messageHandlers.get(key)
      
      if (handlers) {
        handlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            console.error('消息处理器执行错误:', error)
          }
        })
      }

      // 通用消息处理器
      const globalHandlers = this.messageHandlers.get('*')
      if (globalHandlers) {
        globalHandlers.forEach(handler => {
          try {
            handler(message)
          } catch (error) {
            console.error('全局消息处理器执行错误:', error)
          }
        })
      }

    } catch (error) {
      console.error('消息解析错误:', error)
    }
  }

  /**
   * 处理断开连接
   */
  private handleDisconnection(event: CloseEvent): void {
    this.isConnected = false
    this.stopHeartbeat()
    
    console.log('WebSocket连接断开:', event.code, event.reason)
    this.disconnectHandlers.forEach(handler => handler())

    // 自动重连
    if (!this.isReconnecting && this.reconnectAttempts < this.config.maxReconnectAttempts) {
      this.attemptReconnect()
    }
  }

  /**
   * 尝试重连
   */
  private attemptReconnect(): void {
    if (this.isReconnecting) return

    this.isReconnecting = true
    this.reconnectAttempts++

    console.log(`尝试重连... (${this.reconnectAttempts}/${this.config.maxReconnectAttempts})`)

    setTimeout(() => {
      this.connect().catch(error => {
        console.error('重连失败:', error)
        if (this.reconnectAttempts < this.config.maxReconnectAttempts) {
          this.attemptReconnect()
        } else {
          console.error('重连次数已达上限，停止重连')
          this.isReconnecting = false
        }
      })
    }, this.config.reconnectInterval)
  }

  /**
   * 开始心跳检测
   */
  private startHeartbeat(): void {
    this.stopHeartbeat()
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected) {
        this.lastPingTime = Date.now()
        this.send({
          type: MessageType.PING,
          channel: Channel.SYSTEM,
          data: { timestamp: this.lastPingTime }
        })
      }
    }, this.config.heartbeatInterval)
  }

  /**
   * 停止心跳检测
   */
  private stopHeartbeat(): void {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer)
      this.heartbeatTimer = null
    }
  }

  /**
   * 消息入队
   */
  private queueMessage(message: WebSocketMessage): void {
    if (this.messageQueue.length >= this.config.maxQueueSize) {
      this.messageQueue.shift() // 移除最旧的消息
    }
    this.messageQueue.push(message)
  }

  /**
   * 处理消息队列
   */
  private processMessageQueue(): void {
    while (this.messageQueue.length > 0 && this.isConnected) {
      const message = this.messageQueue.shift()!
      this.send(message)
    }
  }

  /**
   * 重新订阅所有频道
   */
  private resubscribeAll(): void {
    this.subscriptions.forEach((options, key) => {
      const [channel, type] = key.split(':')
      this.send({
        type: MessageType.CONNECT,
        channel: channel as Channel,
        data: {
          action: 'subscribe',
          type,
          ...options
        }
      })
    })
  }

  /**
   * 序列化消息
   */
  private serializeMessage(message: WebSocketMessage): string {
    const messageWithId = {
      ...message,
      id: message.id || this.generateMessageId(),
      timestamp: message.timestamp || Date.now()
    }

    let serialized = JSON.stringify(messageWithId)

    // 如果启用压缩且消息较大，则进行压缩
    if (this.config.enableCompression && serialized.length > 1024) {
      // 这里可以实现消息压缩逻辑
      messageWithId.compressed = true
    }

    return serialized
  }

  /**
   * 反序列化消息
   */
  private deserializeMessage(data: string): WebSocketMessage {
    const message = JSON.parse(data)

    // 如果消息被压缩，则解压缩
    if (message.compressed) {
      // 这里可以实现消息解压缩逻辑
    }

    return message
  }

  /**
   * 生成消息ID
   */
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }
}

// 创建全局WebSocket服务实例
const wsConfig: ConnectionConfig = {
  url: process.env.NODE_ENV === 'production' 
    ? 'wss://api.yourdomain.com/ws' 
    : 'ws://localhost:8000/ws',
  reconnectInterval: 3000,
  maxReconnectAttempts: 10,
  heartbeatInterval: 30000,
  enableCompression: true,
  enableQueueing: true,
  maxQueueSize: 1000
}

export const websocketService = new EnhancedWebSocketService(wsConfig)

// Vue组合式函数
export function useWebSocket() {
  return {
    service: websocketService,
    MessageType,
    Channel
  }
}