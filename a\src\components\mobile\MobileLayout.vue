<template>
  <div class="mobile-layout" :class="{ 'layout-fullscreen': isFullscreen }">
    <!-- 移动端顶部导航 -->
    <header class="mobile-header" v-if="!isFullscreen">
      <div class="header-content">
        <!-- 左侧：菜单按钮 -->
        <div class="header-left">
          <el-button text @click="toggleSidebar" class="menu-button">
            <el-icon size="20"><Menu /></el-icon>
          </el-button>

          <div class="page-title">
            <h1>{{ pageTitle }}</h1>
            <span v-if="pageSubtitle" class="subtitle">{{ pageSubtitle }}</span>
          </div>
        </div>

        <!-- 右侧：操作按钮 -->
        <div class="header-right">
          <el-button text @click="showSearch" class="action-button">
            <el-icon size="18"><Search /></el-icon>
          </el-button>

          <el-badge :value="notificationCount" :hidden="notificationCount === 0">
            <el-button text @click="showNotifications" class="action-button">
              <el-icon size="18"><Bell /></el-icon>
            </el-button>
          </el-badge>

          <el-button text @click="showUserMenu" class="action-button">
            <el-avatar :size="28" :src="userAvatar">
              <el-icon><User /></el-icon>
            </el-avatar>
          </el-button>
        </div>
      </div>
    </header>

    <!-- 移动端侧边栏 -->
    <aside class="mobile-sidebar" :class="{ 'sidebar-open': sidebarOpen }">
      <div class="sidebar-overlay" @click="closeSidebar"></div>
      <div class="sidebar-content">
        <!-- 用户信息 -->
        <div class="user-section">
          <el-avatar :size="60" :src="userAvatar">
            <el-icon><User /></el-icon>
          </el-avatar>
          <div class="user-info">
            <div class="username">{{ userName }}</div>
            <div class="user-status">{{ userStatus }}</div>
          </div>
        </div>

        <!-- 导航菜单 -->
        <nav class="mobile-nav">
          <div
            v-for="item in navigationItems"
            :key="item.path"
            class="nav-item"
            :class="{ active: $route.path === item.path }"
            @click="navigateTo(item.path)"
          >
            <el-icon class="nav-icon">
              <component :is="item.icon" />
            </el-icon>
            <span class="nav-label">{{ item.label }}</span>
            <el-icon v-if="item.badge" class="nav-badge">
              <Bell />
            </el-icon>
          </div>
        </nav>

        <!-- 快速操作 -->
        <div class="quick-actions">
          <h3>快速操作</h3>
          <div class="action-grid">
            <div class="action-item" @click="quickTrade">
              <el-icon><TrendCharts /></el-icon>
              <span>快速交易</span>
            </div>
            <div class="action-item" @click="viewMarket">
              <el-icon><DataAnalysis /></el-icon>
              <span>市场行情</span>
            </div>
            <div class="action-item" @click="viewPortfolio">
              <el-icon><PieChart /></el-icon>
              <span>投资组合</span>
            </div>
            <div class="action-item" @click="viewSettings">
              <el-icon><Setting /></el-icon>
              <span>设置</span>
            </div>
          </div>
        </div>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="mobile-main">
      <div class="main-content" ref="mainContentRef">
        <slot />
      </div>
    </main>

    <!-- 移动端底部导航 -->
    <footer class="mobile-footer" v-if="!isFullscreen">
      <div class="footer-nav">
        <div
          v-for="item in bottomNavItems"
          :key="item.path"
          class="footer-nav-item"
          :class="{ active: isActiveRoute(item.path) }"
          @click="navigateTo(item.path)"
        >
          <el-icon class="nav-icon">
            <component :is="item.icon" />
          </el-icon>
          <span class="nav-label">{{ item.label }}</span>
          <div v-if="item.badge" class="nav-badge">{{ item.badge }}</div>
        </div>
      </div>
    </footer>

    <!-- 搜索弹窗 -->
    <el-drawer v-model="searchVisible" direction="ttb" size="100%" class="search-drawer">
      <template #header>
        <div class="search-header">
          <el-input
            v-model="searchQuery"
            placeholder="搜索股票、策略或功能..."
            ref="searchInputRef"
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button text @click="closeSearch">取消</el-button>
        </div>
      </template>

      <div class="search-content">
        <MobileSearchResults :query="searchQuery" @item-selected="handleSearchItemSelected" />
      </div>
    </el-drawer>

    <!-- 通知弹窗 -->
    <el-drawer v-model="notificationsVisible" direction="rtl" size="80%">
      <template #header>
        <h3>通知中心</h3>
      </template>
      <MobileNotifications @notification-action="handleNotificationAction" />
    </el-drawer>

    <!-- 用户菜单弹窗 -->
    <el-drawer v-model="userMenuVisible" direction="rtl" size="70%">
      <template #header>
        <h3>用户中心</h3>
      </template>
      <MobileUserMenu @menu-action="handleUserMenuAction" />
    </el-drawer>

    <!-- 手势提示 -->
    <div v-if="showGestureHint" class="gesture-hint">
      <div class="hint-content">
        <el-icon><Setting /></el-icon>
        <span>{{ gestureHintText }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  Menu, Search, Bell, User, TrendCharts, DataAnalysis, PieChart, Setting,
  Monitor, Document, Wallet
} from '@element-plus/icons-vue'
import MobileSearchResults from './MobileSearchResults.vue'
import MobileNotifications from './MobileNotifications.vue'
import MobileUserMenu from './MobileUserMenu.vue'
import { useGestures } from '@/composables/useGestures'
import { useDeviceDetection } from '@/composables/useDeviceDetection'

const route = useRoute()
const router = useRouter()

// 响应式数据
const sidebarOpen = ref(false)
const searchVisible = ref(false)
const notificationsVisible = ref(false)
const userMenuVisible = ref(false)
const searchQuery = ref('')
const searchInputRef = ref()
const mainContentRef = ref()
const showGestureHint = ref(false)
const gestureHintText = ref('')

// 用户信息
const userName = ref('投资者')
const userStatus = ref('在线')
const userAvatar = ref('')
const notificationCount = ref(3)

// 设备检测
const { isMobile, isTablet, orientation } = useDeviceDetection()

// 手势支持
const { enableGestures, disableGestures } = useGestures(mainContentRef, {
  onSwipeLeft: () => {
    if (route.path === '/dashboard') {
      router.push('/market')
      showGestureHint.value = true
      gestureHintText.value = '左滑查看市场'
      setTimeout(() => showGestureHint.value = false, 2000)
    }
  },
  onSwipeRight: () => {
    if (route.path === '/market') {
      router.push('/dashboard')
      showGestureHint.value = true
      gestureHintText.value = '右滑返回仪表盘'
      setTimeout(() => showGestureHint.value = false, 2000)
    }
  },
  onSwipeUp: () => {
    // 上滑刷新
    window.location.reload()
  },
  onPinch: (scale) => {
    // 缩放手势处理
    if (scale > 1.2) {
      // 放大
      document.body.style.fontSize = '16px'
    } else if (scale < 0.8) {
      // 缩小
      document.body.style.fontSize = '14px'
    }
  }
})

// 导航项目
const navigationItems = ref([
  { path: '/dashboard', label: '仪表盘', icon: 'Monitor' },
  { path: '/market', label: '市场行情', icon: 'DataAnalysis' },
  { path: '/trading', label: '交易中心', icon: 'TrendCharts', badge: true },
  { path: '/portfolio', label: '投资组合', icon: 'PieChart' },
  { path: '/strategy', label: '策略中心', icon: 'Document' },
  { path: '/settings', label: '设置', icon: 'Setting' }
])

const bottomNavItems = ref([
  { path: '/dashboard', label: '首页', icon: 'Monitor' },
  { path: '/market', label: '行情', icon: 'DataAnalysis' },
  { path: '/trading', label: '交易', icon: 'TrendCharts', badge: 2 },
  { path: '/portfolio', label: '持仓', icon: 'Wallet' },
  { path: '/profile', label: '我的', icon: 'User' }
])

// 计算属性
const pageTitle = computed(() => {
  const currentItem = navigationItems.value.find(item => item.path === route.path)
  return currentItem?.label || '量化投资平台'
})

const pageSubtitle = computed(() => {
  // 根据路由返回副标题
  return ''
})

const isFullscreen = computed(() => {
  return route.meta?.fullscreen || false
})

// 方法
const toggleSidebar = () => {
  sidebarOpen.value = !sidebarOpen.value
}

const closeSidebar = () => {
  sidebarOpen.value = false
}

const showSearch = () => {
  searchVisible.value = true
  nextTick(() => {
    searchInputRef.value?.focus()
  })
}

const closeSearch = () => {
  searchVisible.value = false
  searchQuery.value = ''
}

const showNotifications = () => {
  notificationsVisible.value = true
}

const showUserMenu = () => {
  userMenuVisible.value = true
}

const navigateTo = (path: string) => {
  router.push(path)
  closeSidebar()
}

const isActiveRoute = (path: string) => {
  return route.path === path || route.path.startsWith(path + '/')
}

const handleSearch = () => {
  if (searchQuery.value.trim()) {
    ElMessage.info(`搜索: ${searchQuery.value}`)
    // 实现搜索逻辑
  }
}

const handleSearchItemSelected = (item: any) => {
  closeSearch()
  navigateTo(item.path)
}

const handleNotificationAction = (action: string, notification: any) => {
  ElMessage.info(`通知操作: ${action}`)
}

const handleUserMenuAction = (action: string) => {
  ElMessage.info(`用户操作: ${action}`)
  userMenuVisible.value = false
}

// 快速操作
const quickTrade = () => {
  router.push('/trading')
  closeSidebar()
}

const viewMarket = () => {
  router.push('/market')
  closeSidebar()
}

const viewPortfolio = () => {
  router.push('/portfolio')
  closeSidebar()
}

const viewSettings = () => {
  router.push('/settings')
  closeSidebar()
}

// 生命周期
onMounted(() => {
  if (isMobile.value) {
    enableGestures()
  }

  // 监听设备方向变化
  window.addEventListener('orientationchange', () => {
    setTimeout(() => {
      // 重新计算布局
      window.dispatchEvent(new Event('resize'))
    }, 100)
  })
})

onUnmounted(() => {
  disableGestures()
})
</script>

<style scoped>
.mobile-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
  background: #f5f5f5;
}

.layout-fullscreen {
  .mobile-header,
  .mobile-footer {
    display: none;
  }
}

/* 移动端头部 */
.mobile-header {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 8px 16px;
  height: 56px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 0;
}

.menu-button {
  padding: 8px;
  border-radius: 8px;
}

.page-title h1 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.subtitle {
  font-size: 12px;
  color: #666;
}

.header-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.action-button {
  padding: 8px;
  border-radius: 8px;
}

/* 移动端侧边栏 */
.mobile-sidebar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2000;
  pointer-events: none;
  transition: all 0.3s ease;
}

.sidebar-open {
  pointer-events: auto;
}

.sidebar-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.sidebar-open .sidebar-overlay {
  opacity: 1;
}

.sidebar-content {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 280px;
  background: white;
  transform: translateX(-100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
  padding: 20px 0;
}

.sidebar-open .sidebar-content {
  transform: translateX(0);
}

.user-section {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 20px 20px;
  border-bottom: 1px solid #e8e8e8;
}

.user-info {
  flex: 1;
}

.username {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.user-status {
  font-size: 12px;
  color: #52c41a;
}

.mobile-nav {
  padding: 20px 0;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  cursor: pointer;
  transition: background-color 0.2s;
  position: relative;
}

.nav-item:hover {
  background: #f5f5f5;
}

.nav-item.active {
  background: #e6f7ff;
  color: #1890ff;
}

.nav-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: #1890ff;
}

.nav-icon {
  font-size: 20px;
}

.nav-label {
  flex: 1;
  font-size: 14px;
}

.nav-badge {
  color: #ff4d4f;
  font-size: 12px;
}

.quick-actions {
  padding: 20px;
  border-top: 1px solid #e8e8e8;
}

.quick-actions h3 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #666;
}

.action-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px;
  background: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.action-item:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.action-item span {
  font-size: 12px;
}

/* 主内容区域 */
.mobile-main {
  flex: 1;
  overflow: hidden;
  position: relative;
}

.main-content {
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 移动端底部导航 */
.mobile-footer {
  background: white;
  border-top: 1px solid #e8e8e8;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.1);
}

.footer-nav {
  display: flex;
  height: 60px;
}

.footer-nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 4px;
  cursor: pointer;
  transition: all 0.2s;
  position: relative;
}

.footer-nav-item:hover {
  background: #f5f5f5;
}

.footer-nav-item.active {
  color: #1890ff;
}

.footer-nav-item .nav-icon {
  font-size: 20px;
}

.footer-nav-item .nav-label {
  font-size: 10px;
}

.footer-nav-item .nav-badge {
  position: absolute;
  top: 8px;
  right: 50%;
  transform: translateX(10px);
  background: #ff4d4f;
  color: white;
  font-size: 10px;
  padding: 2px 6px;
  border-radius: 10px;
  min-width: 16px;
  text-align: center;
}

/* 搜索抽屉 */
.search-drawer :deep(.el-drawer__body) {
  padding: 0;
}

.search-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
}

.search-content {
  flex: 1;
  overflow-y: auto;
}

/* 手势提示 */
.gesture-hint {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3000;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  pointer-events: none;
}

.hint-content {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .sidebar-content {
    width: 100%;
  }

  .action-grid {
    grid-template-columns: 1fr 1fr 1fr 1fr;
  }

  .action-item {
    padding: 12px 8px;
  }
}

@media (orientation: landscape) and (max-height: 500px) {
  .mobile-header {
    height: 48px;
  }

  .header-content {
    height: 48px;
    padding: 4px 16px;
  }

  .mobile-footer {
    height: 48px;
  }

  .footer-nav {
    height: 48px;
  }
}
</style>
