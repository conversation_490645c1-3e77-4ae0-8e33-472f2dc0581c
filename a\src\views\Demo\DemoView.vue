<template>
  <div class="demo-view">
    <div class="page-header">
      <h1>🎯 功能演示</h1>
      <p>量化投资平台核心功能展示</p>
    </div>

    <div class="demo-content">
      <div class="demo-card">
        <h2>🚀 平台特色</h2>
        <p>探索我们的量化投资平台核心功能...</p>
        
        <div class="feature-list">
          <div class="feature-item">
            <span class="feature-icon">📊</span>
            <span>实时数据分析</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🤖</span>
            <span>智能算法交易</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📈</span>
            <span>策略回测系统</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🛡️</span>
            <span>风险管理工具</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">📱</span>
            <span>移动端支持</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🔒</span>
            <span>安全加密传输</span>
          </div>
        </div>
      </div>

      <div class="demo-card">
        <h2>📈 实时数据</h2>
        <div class="demo-metrics">
          <div class="metric">
            <span class="metric-label">上证指数</span>
            <span class="metric-value">3,245.67</span>
            <span class="metric-change positive">+1.23%</span>
          </div>
          <div class="metric">
            <span class="metric-label">深证成指</span>
            <span class="metric-value">12,456.78</span>
            <span class="metric-change negative">-0.45%</span>
          </div>
          <div class="metric">
            <span class="metric-label">创业板指</span>
            <span class="metric-value">2,789.34</span>
            <span class="metric-change positive">+2.15%</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 演示视图组件
</script>

<style scoped>
.demo-view {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.demo-content {
  display: grid;
  gap: 2rem;
}

.demo-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.demo-card h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #8b5cf6;
}

.feature-icon {
  font-size: 1.5rem;
}

.demo-metrics {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.metric {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  text-align: center;
}

.metric-label {
  font-size: 0.9rem;
  color: #6b7280;
  margin-bottom: 0.5rem;
}

.metric-value {
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 0.25rem;
}

.metric-change {
  font-size: 0.9rem;
  font-weight: 500;
}

.metric-change.positive {
  color: #10b981;
}

.metric-change.negative {
  color: #ef4444;
}
</style>
