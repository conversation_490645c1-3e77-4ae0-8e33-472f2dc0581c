/**
 * CTP交易服务
 * 提供与后端CTP API的交互功能
 */

import { apiClient } from './api'

// 类型定义
export interface CTOrderRequest {
  symbol: string
  direction: 'BUY' | 'SELL'
  order_type: 'LIMIT' | 'MARKET'
  volume: number
  price?: number
  stop_price?: number
  time_in_force?: string
}

export interface CTOrder {
  order_id: string
  symbol: string
  direction: 'BUY' | 'SELL'
  order_type: 'LIMIT' | 'MARKET'
  volume: number
  price?: number
  status: string
  filled_volume: number
  avg_price: number
  create_time: string
  update_time: string
}

export interface CTPosition {
  symbol: string
  direction: 'BUY' | 'SELL'
  volume: number
  avg_price: number
  market_value: number
  pnl: number
  pnl_ratio: number
}

export interface CTAccount {
  account_id: string
  balance: number
  available: number
  margin: number
  commission: number
  pnl: number
  total_asset: number
}

export interface CTStatus {
  connected: boolean
  logged_in: boolean
  broker_id: string
  user_id: string
  simulation_mode: boolean
}

export interface CTOrderResponse {
  success: boolean
  message: string
  order_id?: string
  data?: any
}

class CTPTradingService {
  private baseUrl = '/ctp'
  private wsConnection: WebSocket | null = null
  private wsCallbacks: Map<string, Function[]> = new Map()

  /**
   * 获取CTP连接状态
   */
  async getStatus(): Promise<CTStatus> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/status`)
      return response.data.data
    } catch (error) {
      console.error('获取CTP状态失败:', error)
      throw error
    }
  }

  /**
   * 初始化CTP连接
   */
  async initialize(config?: any): Promise<boolean> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/initialize`, config)
      return response.data.success
    } catch (error) {
      console.error('CTP初始化失败:', error)
      throw error
    }
  }

  /**
   * 提交订单
   */
  async submitOrder(orderRequest: CTOrderRequest): Promise<CTOrderResponse> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/orders`, orderRequest)
      return response.data
    } catch (error) {
      console.error('提交订单失败:', error)
      throw error
    }
  }

  /**
   * 撤销订单
   */
  async cancelOrder(orderId: string): Promise<CTOrderResponse> {
    try {
      const response = await apiClient.delete(`${this.baseUrl}/orders/${orderId}`)
      return response.data
    } catch (error) {
      console.error('撤销订单失败:', error)
      throw error
    }
  }

  /**
   * 查询订单
   */
  async queryOrders(params?: {
    symbol?: string
    status?: string
  }): Promise<CTOrder[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/orders`, { params })
      return response.data.data
    } catch (error) {
      console.error('查询订单失败:', error)
      throw error
    }
  }

  /**
   * 查询持仓
   */
  async queryPositions(): Promise<CTPosition[]> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/positions`)
      return response.data.data
    } catch (error) {
      console.error('查询持仓失败:', error)
      throw error
    }
  }

  /**
   * 查询账户
   */
  async queryAccount(): Promise<CTAccount> {
    try {
      const response = await apiClient.get(`${this.baseUrl}/account`)
      return response.data.data
    } catch (error) {
      console.error('查询账户失败:', error)
      throw error
    }
  }

  /**
   * 批量提交订单
   */
  async submitBatchOrders(orders: CTOrderRequest[]): Promise<CTOrderResponse[]> {
    try {
      const response = await apiClient.post(`${this.baseUrl}/orders/batch`, orders)
      return response.data.data
    } catch (error) {
      console.error('批量提交订单失败:', error)
      throw error
    }
  }

  /**
   * 建立WebSocket连接
   */
  connectWebSocket(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        const wsUrl = `ws://localhost:8000${this.baseUrl}/ws${token ? `?token=${token}` : ''}`
        this.wsConnection = new WebSocket(wsUrl)

        this.wsConnection.onopen = () => {
          console.log('CTP WebSocket连接已建立')
          resolve()
        }

        this.wsConnection.onmessage = (event) => {
          try {
            const message = JSON.parse(event.data)
            this.handleWebSocketMessage(message)
          } catch (error) {
            console.error('WebSocket消息解析失败:', error)
          }
        }

        this.wsConnection.onclose = () => {
          console.log('CTP WebSocket连接已关闭')
          this.wsConnection = null
        }

        this.wsConnection.onerror = (error) => {
          console.error('CTP WebSocket连接错误:', error)
          reject(error)
        }

        // 设置连接超时
        setTimeout(() => {
          if (this.wsConnection?.readyState !== WebSocket.OPEN) {
            reject(new Error('WebSocket连接超时'))
          }
        }, 5000)

      } catch (error) {
        reject(error)
      }
    })
  }

  /**
   * 断开WebSocket连接
   */
  disconnectWebSocket(): void {
    if (this.wsConnection) {
      this.wsConnection.close()
      this.wsConnection = null
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(message: any): void {
    const { type, data } = message

    // 触发对应类型的回调
    const callbacks = this.wsCallbacks.get(type) || []
    callbacks.forEach(callback => {
      try {
        callback(data)
      } catch (error) {
        console.error(`WebSocket回调执行失败 (${type}):`, error)
      }
    })
  }

  /**
   * 注册WebSocket事件回调
   */
  onWebSocketMessage(type: string, callback: Function): void {
    if (!this.wsCallbacks.has(type)) {
      this.wsCallbacks.set(type, [])
    }
    this.wsCallbacks.get(type)!.push(callback)
  }

  /**
   * 移除WebSocket事件回调
   */
  offWebSocketMessage(type: string, callback: Function): void {
    const callbacks = this.wsCallbacks.get(type)
    if (callbacks) {
      const index = callbacks.indexOf(callback)
      if (index > -1) {
        callbacks.splice(index, 1)
      }
    }
  }

  /**
   * 发送WebSocket消息
   */
  sendWebSocketMessage(message: any): void {
    if (this.wsConnection?.readyState === WebSocket.OPEN) {
      this.wsConnection.send(JSON.stringify(message))
    } else {
      console.warn('WebSocket未连接，无法发送消息')
    }
  }

  /**
   * 发送心跳
   */
  sendHeartbeat(): void {
    this.sendWebSocketMessage({ type: 'ping' })
  }

  /**
   * 获取WebSocket连接状态
   */
  get isWebSocketConnected(): boolean {
    return this.wsConnection?.readyState === WebSocket.OPEN
  }

  /**
   * 格式化价格
   */
  formatPrice(price: number, decimals: number = 2): string {
    return price.toFixed(decimals)
  }

  /**
   * 格式化数量
   */
  formatVolume(volume: number): string {
    return volume.toLocaleString()
  }

  /**
   * 格式化金额
   */
  formatAmount(amount: number): string {
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: 'CNY'
    }).format(amount)
  }

  /**
   * 计算订单金额
   */
  calculateOrderAmount(price: number, volume: number): number {
    return price * volume
  }

  /**
   * 验证订单参数
   */
  validateOrder(order: CTOrderRequest): { valid: boolean; message?: string } {
    if (!order.symbol) {
      return { valid: false, message: '请选择交易品种' }
    }

    if (!order.direction) {
      return { valid: false, message: '请选择买卖方向' }
    }

    if (!order.volume || order.volume <= 0) {
      return { valid: false, message: '请输入有效的交易数量' }
    }

    if (order.order_type === 'LIMIT' && (!order.price || order.price <= 0)) {
      return { valid: false, message: '限价单请输入有效的价格' }
    }

    return { valid: true }
  }
}

// 创建单例实例
export const ctpTradingService = new CTPTradingService()

// 默认导出
export default ctpTradingService
