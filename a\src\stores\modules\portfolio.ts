import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type {
  Portfolio,
  PortfolioSummary,
  PortfolioPosition,
  PortfolioPerformance,
  PortfolioAllocation
} from '@/types/portfolio'
import { apiManager } from '@/services/api-manager.service'
import { portfolioService } from '@/services/portfolio.service'
import { httpClient } from '@/api/http'

// 错误处理工具函数
const handleError = (err: unknown, defaultMessage: string): string => {
  return err instanceof Error ? err.message : defaultMessage
}

export const usePortfolioStore = defineStore('portfolio', () => {
  // State
  const portfolios = ref<Portfolio[]>([])
  const currentPortfolio = ref<Portfolio | null>(null)
  const positions = ref<PortfolioPosition[]>([])
  const performance = ref<PortfolioPerformance | null>(null)
  const allocation = ref<PortfolioAllocation[]>([])
  const portfolioTrend = ref<Array<{ date: string; totalAssets: number }>>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)

  // Getters
  const totalValue = computed(() => {
    const positionsArray = Array.isArray(positions.value) ? positions.value : []
    return positionsArray.reduce((sum, pos) => sum + pos.marketValue, 0)
  })

  const totalPnL = computed(() => {
    const positionsArray = Array.isArray(positions.value) ? positions.value : []
    return positionsArray.reduce((sum, pos) => sum + pos.unrealizedPnL, 0)
  })

  const totalPnLPercent = computed(() => {
    const positionsArray = Array.isArray(positions.value) ? positions.value : []
    const totalCost = positionsArray.reduce((sum, pos) => sum + pos.costBasis, 0)
    return totalCost > 0 ? (totalPnL.value / totalCost) * 100 : 0
  })

  const summary = computed((): PortfolioSummary => ({
    totalValue: totalValue.value,
    totalPnL: totalPnL.value,
    totalPnLPercent: totalPnLPercent.value,
    positionCount: positions.value.length,
    cashBalance: currentPortfolio.value?.cashBalance || 0,
    availableCash: currentPortfolio.value?.availableCash || 0
  }))

  const topPositions = computed(() => {
    const positionsArray = Array.isArray(positions.value) ? positions.value : []
    return [...positionsArray]
      .sort((a, b) => b.marketValue - a.marketValue)
      .slice(0, 10)
  })

  const worstPerformers = computed(() => {
    return [...positions.value]
      .sort((a, b) => a.unrealizedPnLPercent - b.unrealizedPnLPercent)
      .slice(0, 5)
  })

  const bestPerformers = computed(() => {
    return [...positions.value]
      .sort((a, b) => b.unrealizedPnLPercent - a.unrealizedPnLPercent)
      .slice(0, 5)
  })

  // Actions
  const fetchPortfolios = async () => {
    try {
      isLoading.value = true
      error.value = null

      try {
        const response = await portfolioService.getPortfolios()
        portfolios.value = response.data
      } catch (err) {
        console.warn('API call failed, using mock data:', err)
        // Fallback to mock data
        portfolios.value = [
        {
          id: 'portfolio-001',
          name: '主投资组合',
          description: '主要投资组合，包含核心持仓',
          totalValue: 100000,
          cashBalance: 52540,
          availableCash: 50000,
          totalPnL: 2960,
          totalPnLPercent: 3.05,
          createdAt: '2024-01-01T00:00:00Z',
          updatedAt: new Date().toISOString()
        }
        ]
      }
    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : '获取投资组合失败'
      error.value = errorMessage
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const setCurrentPortfolio = async (portfolioId: string) => {
    try {
      isLoading.value = true
      error.value = null

      const portfolio = portfolios.value.find(p => p.id === portfolioId)
      if (!portfolio) {
        throw new Error('投资组合不存在')
      }

      currentPortfolio.value = portfolio

      // Fetch positions for this portfolio
      await fetchPositions(portfolioId)
      await fetchPerformance(portfolioId)
      await fetchAllocation(portfolioId)

    } catch (err: unknown) {
      const errorMessage = err instanceof Error ? err.message : '切换投资组合失败'
      error.value = errorMessage
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchPositions = async (portfolioId?: string) => {
    try {
      const id = portfolioId || currentPortfolio.value?.id
      if (!id) return

      // TODO: Replace with actual API call
      // const response = await portfolioService.getPositions(id)
      // positions.value = response.data

      // Mock data for now
      positions.value = [
        {
          id: 'pos-001',
          symbol: '000001',
          name: '平安银行',
          quantity: 1000,
          avgPrice: 12.50,
          currentPrice: 13.80,
          marketValue: 13800,
          costBasis: 12500,
          unrealizedPnL: 1300,
          unrealizedPnLPercent: 10.4,
          weight: 0.138,
          sector: '银行',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'pos-002',
          symbol: '600036',
          name: '招商银行',
          quantity: 500,
          avgPrice: 36.00,
          currentPrice: 38.20,
          marketValue: 19100,
          costBasis: 18000,
          unrealizedPnL: 1100,
          unrealizedPnLPercent: 6.11,
          weight: 0.191,
          sector: '银行',
          lastUpdated: new Date().toISOString()
        },
        {
          id: 'pos-003',
          symbol: '000858',
          name: '五粮液',
          quantity: 100,
          avgPrice: 140.00,
          currentPrice: 145.60,
          marketValue: 14560,
          costBasis: 14000,
          unrealizedPnL: 560,
          unrealizedPnLPercent: 4.0,
          weight: 0.146,
          sector: '食品饮料',
          lastUpdated: new Date().toISOString()
        }
      ]
    } catch (err: unknown) {
      error.value = handleError(err, '获取持仓失败')
      throw err
    }
  }

  const fetchPerformance = async (portfolioId?: string) => {
    try {
      const id = portfolioId || currentPortfolio.value?.id
      if (!id) return

      // TODO: Replace with actual API call
      // const response = await portfolioService.getPerformance(id)
      // performance.value = response.data

      // Mock data for now
      performance.value = null
    } catch (err: any) {
      error.value = err.message || '获取绩效失败'
      throw err
    }
  }

  const fetchAllocation = async (portfolioId?: string) => {
    try {
      const id = portfolioId || currentPortfolio.value?.id
      if (!id) return

      // TODO: Replace with actual API call
      // const response = await portfolioService.getAllocation(id)
      // allocation.value = response.data

      // Mock data for now
      allocation.value = []
    } catch (err: any) {
      error.value = err.message || '获取配置失败'
      throw err
    }
  }

  const fetchPortfolioTrend = async (params: { timeRange: string; symbol: string }) => {
    try {
      isLoading.value = true
      error.value = null

      // TODO: Replace with actual API call
      // const response = await portfolioService.getPortfolioTrend(params)
      // portfolioTrend.value = response.data

      // Mock data for now
      const mockData = []
      const now = new Date()
      const days = params.timeRange === 'today' ? 1 : params.timeRange === 'week' ? 7 : 30

      // 基础资产值
      let baseValue = 100000

      for (let i = days - 1; i >= 0; i--) {
        const date = new Date(now)
        date.setDate(date.getDate() - i)

        // 模拟市场波动，总体趋势向上
        const dailyChange = (Math.random() - 0.45) * 0.02 // 略微向上的趋势
        baseValue = baseValue * (1 + dailyChange)

        mockData.push({
          date: i === 0 ? '今日' : date.toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' }),
          totalAssets: Math.round(baseValue)
        })
      }

      portfolioTrend.value = mockData
    } catch (err: any) {
      error.value = err.message || '获取投资组合趋势失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const initialize = async () => {
    try {
      await fetchPortfolios()
      if (portfolios.value.length > 0) {
        await setCurrentPortfolio(portfolios.value[0].id)
      }
    } catch (err: any) {
      console.error('初始化投资组合失败:', err)
    }
  }

  const createPortfolio = async (portfolioData: Omit<Portfolio, 'id' | 'createdAt' | 'updatedAt'>) => {
    try {
      isLoading.value = true
      error.value = null

      // TODO: Replace with actual API call
      // const response = await portfolioService.createPortfolio(portfolioData)
      // const newPortfolio = response.data
      // portfolios.value.push(newPortfolio)
      // return newPortfolio

      // Mock implementation
      const newPortfolio: Portfolio = {
        ...portfolioData,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      }
      portfolios.value.push(newPortfolio)
      return newPortfolio

    } catch (err: any) {
      error.value = err.message || '创建投资组合失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updatePortfolio = async (portfolioId: string, updates: Partial<Portfolio>) => {
    try {
      isLoading.value = true
      error.value = null

      // TODO: Replace with actual API call
      // const response = await portfolioService.updatePortfolio(portfolioId, updates)
      // const updatedPortfolio = response.data

      // Mock implementation
      const index = portfolios.value.findIndex(p => p.id === portfolioId)
      if (index !== -1) {
        portfolios.value[index] = { ...portfolios.value[index], ...updates }
        if (currentPortfolio.value?.id === portfolioId) {
          currentPortfolio.value = portfolios.value[index]
        }
      }

    } catch (err: any) {
      error.value = err.message || '更新投资组合失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const deletePortfolio = async (portfolioId: string) => {
    try {
      isLoading.value = true
      error.value = null

      // TODO: Replace with actual API call
      // await portfolioService.deletePortfolio(portfolioId)

      // Mock implementation
      portfolios.value = portfolios.value.filter(p => p.id !== portfolioId)
      if (currentPortfolio.value?.id === portfolioId) {
        currentPortfolio.value = null
        positions.value = []
        performance.value = null
        allocation.value = []
      }

    } catch (err: any) {
      error.value = err.message || '删除投资组合失败'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const refreshData = async () => {
    if (currentPortfolio.value) {
      await Promise.all([
        fetchPositions(),
        fetchPerformance(),
        fetchAllocation()
      ])
    }
  }

  const clearError = () => {
    error.value = null
  }

  const reset = () => {
    portfolios.value = []
    currentPortfolio.value = null
    positions.value = []
    performance.value = null
    allocation.value = []
    portfolioTrend.value = []
    error.value = null
  }

  return {
    // State
    portfolios,
    currentPortfolio,
    positions,
    performance,
    allocation,
    portfolioTrend,
    isLoading,
    error,

    // Getters
    totalValue,
    totalPnL,
    totalPnLPercent,
    summary,
    topPositions,
    worstPerformers,
    bestPerformers,

    // Actions
    fetchPortfolios,
    setCurrentPortfolio,
    fetchPositions,
    fetchPerformance,
    fetchAllocation,
    fetchPortfolioTrend,
    createPortfolio,
    updatePortfolio,
    deletePortfolio,
    refreshData,
    initialize,
    clearError,
    reset
  }
}, {
  persist: {
    key: 'portfolio-store',
    storage: localStorage,
    paths: ['currentPortfolio']
  }
})
