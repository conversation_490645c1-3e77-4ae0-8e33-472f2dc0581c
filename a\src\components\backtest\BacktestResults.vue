<template>
  <div class="backtest-results">
    <!-- 结果概览 -->
    <el-card class="results-overview" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><TrendCharts /></el-icon>
          <span>回测结果概览</span>
          <div class="header-actions">
            <el-button size="small" @click="exportResults">
              <el-icon><Download /></el-icon>
              导出报告
            </el-button>
          </div>
        </div>
      </template>

      <div class="overview-metrics">
        <div class="metric-row">
          <div class="metric-item">
            <div class="metric-label">总收益率</div>
            <div class="metric-value" :class="getReturnClass(results.totalReturn)">
              {{ formatPercent(results.totalReturn) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">年化收益率</div>
            <div class="metric-value" :class="getReturnClass(results.annualReturn)">
              {{ formatPercent(results.annualReturn) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">最大回撤</div>
            <div class="metric-value negative">
              {{ formatPercent(results.maxDrawdown) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">夏普比率</div>
            <div class="metric-value">
              {{ results.sharpeRatio.toFixed(2) }}
            </div>
          </div>
        </div>

        <div class="metric-row">
          <div class="metric-item">
            <div class="metric-label">胜率</div>
            <div class="metric-value">
              {{ formatPercent(results.winRate) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">盈亏比</div>
            <div class="metric-value">
              {{ results.profitLossRatio.toFixed(2) }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">交易次数</div>
            <div class="metric-value">
              {{ results.totalTrades }}
            </div>
          </div>
          <div class="metric-item">
            <div class="metric-label">基准收益率</div>
            <div class="metric-value" :class="getReturnClass(results.benchmarkReturn)">
              {{ formatPercent(results.benchmarkReturn) }}
            </div>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 收益曲线图 -->
    <el-card class="results-chart" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><LineChart /></el-icon>
          <span>收益曲线</span>
        </div>
      </template>
      
      <div class="chart-container">
        <div ref="chartRef" class="chart" style="height: 400px;"></div>
      </div>
    </el-card>

    <!-- 详细统计 -->
    <el-row :gutter="20">
      <el-col :span="12">
        <el-card class="detailed-stats" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>详细统计</span>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="回测开始时间">
              {{ formatDate(results.startDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="回测结束时间">
              {{ formatDate(results.endDate) }}
            </el-descriptions-item>
            <el-descriptions-item label="回测天数">
              {{ results.totalDays }}天
            </el-descriptions-item>
            <el-descriptions-item label="初始资金">
              {{ formatCurrency(results.initialCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最终资金">
              {{ formatCurrency(results.finalCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大资金">
              {{ formatCurrency(results.maxCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="最小资金">
              {{ formatCurrency(results.minCapital) }}
            </el-descriptions-item>
            <el-descriptions-item label="波动率">
              {{ formatPercent(results.volatility) }}
            </el-descriptions-item>
            <el-descriptions-item label="信息比率">
              {{ results.informationRatio.toFixed(2) }}
            </el-descriptions-item>
            <el-descriptions-item label="卡尔马比率">
              {{ results.calmarRatio.toFixed(2) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <el-col :span="12">
        <el-card class="trade-stats" shadow="never">
          <template #header>
            <div class="card-header">
              <el-icon><Operation /></el-icon>
              <span>交易统计</span>
            </div>
          </template>

          <el-descriptions :column="1" border>
            <el-descriptions-item label="总交易次数">
              {{ results.totalTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="盈利交易次数">
              {{ results.profitTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="亏损交易次数">
              {{ results.lossTrades }}
            </el-descriptions-item>
            <el-descriptions-item label="平均持仓天数">
              {{ results.avgHoldingDays.toFixed(1) }}天
            </el-descriptions-item>
            <el-descriptions-item label="平均盈利">
              {{ formatCurrency(results.avgProfit) }}
            </el-descriptions-item>
            <el-descriptions-item label="平均亏损">
              {{ formatCurrency(results.avgLoss) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大单笔盈利">
              {{ formatCurrency(results.maxProfit) }}
            </el-descriptions-item>
            <el-descriptions-item label="最大单笔亏损">
              {{ formatCurrency(results.maxLoss) }}
            </el-descriptions-item>
            <el-descriptions-item label="手续费总计">
              {{ formatCurrency(results.totalCommission) }}
            </el-descriptions-item>
            <el-descriptions-item label="印花税总计">
              {{ formatCurrency(results.totalStampTax) }}
            </el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
    </el-row>

    <!-- 交易记录 -->
    <el-card class="trade-records" shadow="never">
      <template #header>
        <div class="card-header">
          <el-icon><List /></el-icon>
          <span>交易记录</span>
          <div class="header-actions">
            <el-button size="small" @click="exportTrades">
              <el-icon><Download /></el-icon>
              导出交易记录
            </el-button>
          </div>
        </div>
      </template>

      <el-table :data="results.trades" stripe>
        <el-table-column prop="date" label="交易日期" width="120" />
        <el-table-column prop="symbol" label="股票代码" width="100" />
        <el-table-column prop="name" label="股票名称" width="120" />
        <el-table-column prop="action" label="操作" width="80">
          <template #default="{ row }">
            <el-tag :type="row.action === 'buy' ? 'success' : 'danger'">
              {{ row.action === 'buy' ? '买入' : '卖出' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="quantity" label="数量" width="100" />
        <el-table-column prop="price" label="价格" width="100">
          <template #default="{ row }">
            ¥{{ row.price.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="amount" label="金额" width="120">
          <template #default="{ row }">
            ¥{{ row.amount.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="commission" label="手续费" width="100">
          <template #default="{ row }">
            ¥{{ row.commission.toFixed(2) }}
          </template>
        </el-table-column>
        <el-table-column prop="profit" label="盈亏" width="120">
          <template #default="{ row }">
            <span :class="row.profit >= 0 ? 'positive' : 'negative'">
              {{ row.profit >= 0 ? '+' : '' }}¥{{ row.profit.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="reason" label="交易原因" min-width="150" />
      </el-table>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  TrendCharts,
  LineChart,
  DataAnalysis,
  Operation,
  List,
  Download
} from '@element-plus/icons-vue'

interface BacktestResults {
  // 基本信息
  startDate: string
  endDate: string
  totalDays: number
  initialCapital: number
  finalCapital: number
  maxCapital: number
  minCapital: number
  
  // 收益指标
  totalReturn: number
  annualReturn: number
  maxDrawdown: number
  volatility: number
  sharpeRatio: number
  informationRatio: number
  calmarRatio: number
  benchmarkReturn: number
  
  // 交易指标
  totalTrades: number
  profitTrades: number
  lossTrades: number
  winRate: number
  profitLossRatio: number
  avgHoldingDays: number
  avgProfit: number
  avgLoss: number
  maxProfit: number
  maxLoss: number
  totalCommission: number
  totalStampTax: number
  
  // 收益曲线数据
  equityCurve: Array<{ date: string; value: number; benchmark: number }>
  
  // 交易记录
  trades: Array<{
    date: string
    symbol: string
    name: string
    action: 'buy' | 'sell'
    quantity: number
    price: number
    amount: number
    commission: number
    profit: number
    reason: string
  }>
}

interface Props {
  results: BacktestResults
}

const props = defineProps<Props>()
const chartRef = ref()

// 格式化方法
const formatPercent = (value: number) => {
  return `${(value * 100).toFixed(2)}%`
}

const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY'
  }).format(value)
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const getReturnClass = (value: number) => {
  return value >= 0 ? 'positive' : 'negative'
}

// 导出功能
const exportResults = () => {
  ElMessage.success('导出功能开发中...')
}

const exportTrades = () => {
  ElMessage.success('导出交易记录功能开发中...')
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  try {
    // 使用动态导入 ECharts
    import('echarts').then(echarts => {
      const chart = echarts.init(chartRef.value)
      
      // 生成模拟收益曲线数据
      const generateMockData = () => {
        const dates = []
        const returns = []
        const benchmark = []
        
        const startDate = new Date(2024, 0, 1) // 2024-01-01
        let currentReturn = 10000 // 初始资金
        let benchmarkReturn = 10000
        
        for (let i = 0; i < 90; i++) {
          const date = new Date(startDate)
          date.setDate(startDate.getDate() + i)
          dates.push(date.toISOString().split('T')[0])
          
          // 模拟策略收益（有一定波动性和趋势）
          const dailyReturn = (Math.random() - 0.4) * 0.03 // -1.2% 到 1.8% 的日收益
          currentReturn *= (1 + dailyReturn)
          returns.push(Math.round(currentReturn))
          
          // 模拟基准收益（市场平均水平）
          const benchmarkDaily = (Math.random() - 0.5) * 0.02 // -1% 到 1% 的日收益
          benchmarkReturn *= (1 + benchmarkDaily)
          benchmark.push(Math.round(benchmarkReturn))
        }
        
        return { dates, returns, benchmark }
      }
      
      const { dates, returns, benchmark } = generateMockData()
      
      const option = {
        title: {
          text: '策略收益曲线',
          left: 'left',
          textStyle: {
            fontSize: 16,
            fontWeight: 'normal'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params) {
            let result = params[0].axisValueLabel + '<br/>'
            params.forEach(param => {
              const value = param.value
              const percentage = ((value - 10000) / 10000 * 100).toFixed(2)
              result += `${param.seriesName}: ¥${value.toLocaleString()} (${percentage}%)<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['策略收益', '基准收益'],
          top: 30
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: dates,
          axisLabel: {
            formatter: function(value) {
              return new Date(value).toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric'
              })
            }
          }
        },
        yAxis: {
          type: 'value',
          axisLabel: {
            formatter: function(value) {
              return '¥' + (value / 1000).toFixed(0) + 'k'
            }
          }
        },
        series: [
          {
            name: '策略收益',
            type: 'line',
            data: returns,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#409EFF',
              width: 2
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
                  { offset: 1, color: 'rgba(64, 158, 255, 0.05)' }
                ]
              }
            }
          },
          {
            name: '基准收益',
            type: 'line',
            data: benchmark,
            smooth: true,
            symbol: 'none',
            lineStyle: {
              color: '#F56C6C',
              width: 2,
              type: 'dashed'
            }
          }
        ]
      }
      
      chart.setOption(option)
      
      // 响应式处理
      const handleResize = () => {
        chart.resize()
      }
      window.addEventListener('resize', handleResize)
      
      // 存储 chart 实例，便于清理
      chartRef.value._chart = chart
      chartRef.value._resizeHandler = handleResize
      
    }).catch(error => {
      console.warn('ECharts not available, showing fallback:', error)
      chartRef.value.innerHTML = `
        <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999; flex-direction: column;">
          <div style="font-size: 18px; margin-bottom: 8px;">📈</div>
          <div>收益曲线图表</div>
          <div style="font-size: 12px; margin-top: 4px;">ECharts库未加载</div>
        </div>
      `
    })
    
  } catch (error) {
    console.error('Chart initialization failed:', error)
    chartRef.value.innerHTML = `
      <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #999;">
        图表加载失败
      </div>
    `
  }
}

onMounted(() => {
  nextTick(() => {
    initChart()
  })
})

onUnmounted(() => {
  // 清理图表实例和事件监听器
  if (chartRef.value && chartRef.value._chart) {
    chartRef.value._chart.dispose()
  }
  if (chartRef.value && chartRef.value._resizeHandler) {
    window.removeEventListener('resize', chartRef.value._resizeHandler)
  }
})
</script>

<style scoped>
.backtest-results {
  padding: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.card-header span {
  margin-left: 8px;
  font-weight: 500;
}

.overview-metrics {
  padding: 20px 0;
}

.metric-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;
}

.metric-row:last-child {
  margin-bottom: 0;
}

.metric-item {
  text-align: center;
  flex: 1;
}

.metric-label {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.metric-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.metric-value.positive {
  color: #f56c6c;
}

.metric-value.negative {
  color: #67c23a;
}

.chart-container {
  padding: 20px 0;
}

.chart {
  border: 1px solid #eee;
  border-radius: 4px;
}

.results-overview,
.results-chart,
.detailed-stats,
.trade-stats,
.trade-records {
  margin-bottom: 20px;
}

.positive {
  color: #f56c6c;
}

.negative {
  color: #67c23a;
}
</style>
