/**
 * 安全工具函数
 * 包含XSS防护、CSP策略、输入验证等安全功能
 */

import DOMPurify from 'dompurify'

// CSP策略配置
export const CSP_POLICIES = {
  'default-src': ["'self'"],
  'script-src': [
    "'self'",
    "'unsafe-inline'", // 开发环境需要，生产环境应移除
    "'unsafe-eval'", // 开发环境需要，生产环境应移除
    'https://cdn.jsdelivr.net',
    'https://unpkg.com'
  ],
  'style-src': [
    "'self'",
    "'unsafe-inline'",
    'https://fonts.googleapis.com',
    'https://cdn.jsdelivr.net'
  ],
  'font-src': [
    "'self'",
    'https://fonts.gstatic.com',
    'https://cdn.jsdelivr.net',
    'data:'
  ],
  'img-src': [
    "'self'",
    'data:',
    'blob:',
    'https:',
    'http:' // 开发环境，生产环境应移除
  ],
  'connect-src': [
    "'self'",
    'wss:',
    'ws:',
    'https://api.example.com', // 替换为实际API域名
    'http://localhost:*' // 开发环境
  ],
  'media-src': ["'self'", 'data:', 'blob:'],
  'object-src': ["'none'"],
  'base-uri': ["'self'"],
  'form-action': ["'self'"],
  // 'frame-ancestors': ["'none'"], // 移除：此指令只能通过 HTTP 响应头设置，不能通过 meta 标签设置
  'upgrade-insecure-requests': true
}

/**
 * 生成CSP策略字符串
 */
export const generateCSPString = (policies = CSP_POLICIES): string => {
  const cspParts: string[] = []

  Object.entries(policies).forEach(([directive, values]) => {
    if (directive === 'upgrade-insecure-requests') {
      if (values) {
        cspParts.push('upgrade-insecure-requests')
      }
    } else if (Array.isArray(values)) {
      cspParts.push(`${directive} ${values.join(' ')}`)
    }
  })

  return cspParts.join('; ')
}

/**
 * 设置CSP头部（在HTML中使用meta标签）
 * 注意：安全头应该通过HTTP响应头设置，而不是meta标签
 */
export const setCSPMeta = (): void => {
  // 完全禁用meta标签设置，安全头已通过后端HTTP响应头设置
  console.log('🔒 安全头通过后端HTTP响应头设置，跳过meta标签设置')
  return

  // 以下代码已禁用，避免X-Frame-Options meta标签错误
  // const existingMeta = document.querySelector('meta[http-equiv="Content-Security-Policy"]')
  // if (existingMeta) {
  //   existingMeta.remove()
  // }

  // const meta = document.createElement('meta')
  // meta.httpEquiv = 'Content-Security-Policy'
  // meta.content = generateCSPString()
  // document.head.appendChild(meta)
}

/**
 * XSS防护配置
 */
const XSS_CONFIG = {
  ALLOWED_TAGS: [
    'b', 'i', 'em', 'strong', 'u', 's', 'span', 'div', 'p', 'br',
    'ul', 'ol', 'li', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'table', 'thead', 'tbody', 'tr', 'td', 'th',
    'blockquote', 'code', 'pre'
  ],
  ALLOWED_ATTRIBUTES: {
    '*': ['class', 'id'],
    'span': ['style'],
    'div': ['style'],
    'p': ['style'],
    'table': ['border', 'cellpadding', 'cellspacing'],
    'td': ['colspan', 'rowspan'],
    'th': ['colspan', 'rowspan']
  },
  ALLOWED_SCHEMES: ['http', 'https', 'mailto', 'tel'],
  FORBID_TAGS: ['script', 'object', 'embed', 'form', 'input', 'iframe'],
  FORBID_ATTR: ['onload', 'onerror', 'onclick', 'onmouseover', 'onfocus', 'onblur']
}

/**
 * 清理HTML内容，防止XSS攻击
 */
export const sanitizeHTML = (dirty: string, options?: any): string => {
  if (!dirty || typeof dirty !== 'string') {
    return ''
  }

  const config = {
    ALLOWED_TAGS: options?.allowedTags || XSS_CONFIG.ALLOWED_TAGS,
    ALLOWED_ATTR: options?.allowedAttributes || XSS_CONFIG.ALLOWED_ATTRIBUTES,
    ALLOWED_URI_REGEXP: new RegExp(`^(?:(?:(?:f|ht)tps?|mailto|tel):|[^a-z]|[a-z+.\\-]+(?:[^a-z+.\\-:]|$))`, 'i'),
    FORBID_TAGS: XSS_CONFIG.FORBID_TAGS,
    FORBID_ATTR: XSS_CONFIG.FORBID_ATTR,
    RETURN_DOM: false,
    RETURN_DOM_FRAGMENT: false,
    RETURN_DOM_IMPORT: false,
    SANITIZE_DOM: true,
    KEEP_CONTENT: true,
    ...options
  }

  return DOMPurify.sanitize(dirty, config)
}

/**
 * 清理文本内容，移除所有HTML标签
 */
export const sanitizeText = (dirty: string): string => {
  if (!dirty || typeof dirty !== 'string') {
    return ''
  }

  return DOMPurify.sanitize(dirty, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: [],
    KEEP_CONTENT: true
  })
}

/**
 * 验证和清理URL
 */
export const sanitizeURL = (url: string): string => {
  if (!url || typeof url !== 'string') {
    return ''
  }

  try {
    const parsed = new URL(url)

    // 只允许特定协议
    if (!['http:', 'https:', 'mailto:', 'tel:'].includes(parsed.protocol)) {
      return ''
    }

    // 防止javascript:和data:协议
    if (parsed.protocol === 'javascript:' || parsed.protocol === 'data:') {
      return ''
    }

    return parsed.toString()
  } catch (error) {
    console.warn('Invalid URL:', url)
    return ''
  }
}

/**
 * 输入验证函数
 */
export const validateInput = {
  /**
   * 验证邮箱格式
   */
  email: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    return emailRegex.test(email)
  },

  /**
   * 验证手机号格式（中国大陆）
   */
  phone: (phone: string): boolean => {
    const phoneRegex = /^1[3-9]\d{9}$/
    return phoneRegex.test(phone)
  },

  /**
   * 验证密码强度
   */
  password: (password: string): { isValid: boolean; errors: string[] } => {
    const errors: string[] = []

    if (password.length < 8) {
      errors.push('密码长度至少8位')
    }

    if (!/[a-z]/.test(password)) {
      errors.push('密码必须包含小写字母')
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('密码必须包含大写字母')
    }

    if (!/\d/.test(password)) {
      errors.push('密码必须包含数字')
    }

    if (!/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
      errors.push('密码必须包含特殊字符')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * 验证用户名格式
   */
  username: (username: string): boolean => {
    // 只允许字母、数字、下划线，长度3-20位
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/
    return usernameRegex.test(username)
  },

  /**
   * 验证数字格式
   */
  number: (value: string, options?: { min?: number; max?: number; decimals?: number }): boolean => {
    const num = parseFloat(value)

    if (isNaN(num)) {
      return false
    }

    if (options?.min !== undefined && num < options.min) {
      return false
    }

    if (options?.max !== undefined && num > options.max) {
      return false
    }

    if (options?.decimals !== undefined) {
      const decimals = (value.split('.')[1] || '').length
      if (decimals > options.decimals) {
        return false
      }
    }

    return true
  },

  /**
   * 验证股票代码格式
   */
  stockCode: (code: string): boolean => {
    // 支持A股、港股、美股代码格式
    const patterns = [
      /^[0-9]{6}$/, // A股：6位数字
      /^[0-9]{5}\.HK$/, // 港股：5位数字.HK
      /^[A-Z]{1,5}$/, // 美股：1-5位字母
      /^[A-Z]{1,4}\.[A-Z]{1,2}$/ // 其他市场
    ]

    return patterns.some(pattern => pattern.test(code))
  }
}

/**
 * 防止点击劫持攻击
 */
export const preventClickjacking = (): void => {
  // 设置X-Frame-Options
  if (window.self !== window.top) {
    // 如果页面被嵌入iframe中，跳转到顶层
    window.top!.location = window.self.location
  }
}

/**
 * 生成安全的随机字符串
 */
export const generateSecureToken = (length = 32): string => {
  const array = new Uint8Array(length)
  crypto.getRandomValues(array)
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('')
}

/**
 * 安全的localStorage操作
 */
export const secureStorage = {
  /**
   * 安全存储数据
   */
  setItem: (key: string, value: any): void => {
    try {
      // 验证key的安全性
      if (!key || typeof key !== 'string' || key.includes('<') || key.includes('>')) {
        throw new Error('Invalid storage key')
      }

      const sanitizedValue = typeof value === 'string' ? sanitizeText(value) : value
      localStorage.setItem(key, JSON.stringify(sanitizedValue))
    } catch (error) {
      console.error('Secure storage setItem failed:', error)
    }
  },

  /**
   * 安全获取数据
   */
  getItem: (key: string): any => {
    try {
      // 验证key的安全性
      if (!key || typeof key !== 'string' || key.includes('<') || key.includes('>')) {
        return null
      }

      const item = localStorage.getItem(key)
      return item ? JSON.parse(item) : null
    } catch (error) {
      console.error('Secure storage getItem failed:', error)
      return null
    }
  },

  /**
   * 移除数据
   */
  removeItem: (key: string): void => {
    try {
      if (!key || typeof key !== 'string') {
        return
      }
      localStorage.removeItem(key)
    } catch (error) {
      console.error('Secure storage removeItem failed:', error)
    }
  },

  /**
   * 清空所有数据
   */
  clear: (): void => {
    try {
      localStorage.clear()
    } catch (error) {
      console.error('Secure storage clear failed:', error)
    }
  }
}

/**
 * 检查页面完整性
 */
export const checkPageIntegrity = (): boolean => {
  try {
    // 检查是否有可疑的脚本注入
    const scripts = document.querySelectorAll('script')
    for (const script of scripts) {
      // 检查内联脚本是否包含可疑内容
      if (script.innerHTML.includes('eval(') ||
          script.innerHTML.includes('document.write(') ||
          script.innerHTML.includes('innerHTML') ||
          script.innerHTML.includes('outerHTML')) {
        console.warn('Suspicious script detected:', script)
        return false
      }
    }

    // 检查是否有可疑的iframe
    const iframes = document.querySelectorAll('iframe')
    for (const iframe of iframes) {
      const src = iframe.getAttribute('src')
      if (src && !src.startsWith('https://') && !src.startsWith('/')) {
        console.warn('Suspicious iframe detected:', iframe)
        return false
      }
    }

    return true
  } catch (error) {
    console.error('Page integrity check failed:', error)
    return false
  }
}

/**
 * 监听DOM变化，防止XSS注入
 */
export const startSecurityMonitoring = (): (() => void) => {
  const observer = new MutationObserver((mutations) => {
    mutations.forEach((mutation) => {
      mutation.addedNodes.forEach((node) => {
        if (node.nodeType === Node.ELEMENT_NODE) {
          const element = node as Element

          // 检查脚本标签
          if (element.tagName === 'SCRIPT') {
            console.warn('Script tag added dynamically:', element)
            element.remove()
          }

          // 检查可疑属性
          const attributes = element.attributes
          if (attributes) {
            for (let i = 0; i < attributes.length; i++) {
              const attr = attributes[i]
              if (attr.name.startsWith('on') || attr.value.includes('javascript:')) {
                console.warn('Suspicious attribute detected:', attr)
                element.removeAttribute(attr.name)
              }
            }
          }
        }
      })
    })
  })

  observer.observe(document.body, {
    childList: true,
    subtree: true,
    attributes: true,
    attributeFilter: ['onclick', 'onload', 'onerror', 'onmouseover']
  })

  return () => observer.disconnect()
}

/**
 * 初始化安全防护
 */
export const initSecurity = (): void => {
  // 设置CSP策略
  setCSPMeta()

  // 防止点击劫持
  preventClickjacking()

  // 检查页面完整性
  if (!checkPageIntegrity()) {
    console.error('Page integrity check failed')
  }

  // 启动安全监控
  startSecurityMonitoring()

  // 禁用开发者工具（生产环境）
  if (import.meta.env.PROD) {
    disableDevTools()
  }

  console.log('Security initialization completed')
}

/**
 * 禁用开发者工具（生产环境）
 */
const disableDevTools = (): void => {
  // 检测开发者工具是否打开
  const detectDevTools = () => {
    const threshold = 160
    const widthThreshold = window.outerWidth - window.innerWidth > threshold
    const heightThreshold = window.outerHeight - window.innerHeight > threshold

    if (widthThreshold || heightThreshold) {
      console.clear()
      console.warn('开发者工具已被检测到')
      // 可以选择重定向到错误页面或执行其他安全措施
    }
  }

  // 禁用右键菜单
  document.addEventListener('contextmenu', (e) => {
    e.preventDefault()
  })

  // 禁用F12和其他开发者工具快捷键
  document.addEventListener('keydown', (e) => {
    if (e.key === 'F12' ||
        (e.ctrlKey && e.shiftKey && e.key === 'I') ||
        (e.ctrlKey && e.shiftKey && e.key === 'C') ||
        (e.ctrlKey && e.shiftKey && e.key === 'J') ||
        (e.ctrlKey && e.key === 'U')) {
      e.preventDefault()
    }
  })

  // 定期检测开发者工具
  setInterval(detectDevTools, 1000)
}

/**
 * 创建安全的事件处理器
 */
export const createSecureEventHandler = (handler: Function): EventListener => {
  return (event: Event) => {
    try {
      // 验证事件来源
      if (!event.isTrusted) {
        console.warn('Untrusted event detected:', event)
        return
      }

      // 执行原始处理器
      handler(event)
    } catch (error) {
      console.error('Event handler error:', error)
    }
  }
}

/**
 * 导出安全配置
 */
export const SECURITY_CONFIG = {
  CSP_POLICIES,
  XSS_CONFIG,
  generateCSPString,
  setCSPMeta,
  sanitizeHTML,
  sanitizeText,
  sanitizeURL,
  validateInput,
  secureStorage,
  initSecurity
}

// 重新导出CSRF Token函数以供安全拦截器使用
import { getCSRFToken as getCSRFTokenFromAuth } from '@/utils/auth'
export const getCSRFToken = getCSRFTokenFromAuth
