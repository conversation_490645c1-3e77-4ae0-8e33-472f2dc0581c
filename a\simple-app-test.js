/**
 * 简单应用测试 - 检查Vue应用是否能正常启动
 */

import puppeteer from 'puppeteer';

class SimpleAppTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.errors = [];
  }

  async init() {
    console.log('🔍 启动简单应用测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听所有控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'error') {
        this.errors.push(text);
        console.log(`❌ [${type.toUpperCase()}] ${text}`);
      } else if (type === 'warn') {
        console.log(`⚠️ [${type.toUpperCase()}] ${text}`);
      } else if (text.includes('🚀') || text.includes('✅') || text.includes('❌')) {
        console.log(`📝 [${type.toUpperCase()}] ${text}`);
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
      this.errors.push(error.message);
    });

    // 监听请求失败
    this.page.on('requestfailed', request => {
      console.log(`🔴 请求失败: ${request.url()} - ${request.failure()?.errorText}`);
    });
  }

  async testVueAppStartup() {
    console.log('\n🔍 测试Vue应用启动...');
    
    try {
      // 导航到首页
      console.log('1. 加载首页...');
      await this.page.goto('http://localhost:5173', { 
        waitUntil: 'domcontentloaded',
        timeout: 30000 
      });
      
      // 等待一段时间让应用启动
      console.log('2. 等待应用启动...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // 检查应用状态
      const appStatus = await this.page.evaluate(() => {
        const app = document.querySelector('#app');
        
        // 检查Vue应用是否挂载
        const hasVueApp = app && (app.__vue_app__ || app._vnode);
        
        // 检查DOM结构
        const appHTML = app ? app.innerHTML : 'No app element';
        const hasChildren = app ? app.children.length > 0 : false;
        
        // 检查是否有加载动画
        const hasLoadingSpinner = document.querySelector('.app-loading') !== null;
        const hasLoadingSpinnerVisible = hasLoadingSpinner && 
          getComputedStyle(document.querySelector('.app-loading')).display !== 'none';
        
        // 检查是否有路由视图
        const hasRouterView = document.querySelector('router-view') !== null;
        
        // 检查是否有布局组件
        const hasProfessionalLayout = document.querySelector('.professional-layout') !== null;
        
        // 检查JavaScript错误
        const jsErrors = window.jsErrors || [];
        
        return {
          hasVueApp,
          hasChildren,
          appHTML: appHTML.substring(0, 500),
          hasLoadingSpinner,
          hasLoadingSpinnerVisible,
          hasRouterView,
          hasProfessionalLayout,
          jsErrors,
          url: window.location.href,
          title: document.title
        };
      });
      
      console.log('\n📊 应用状态检查:');
      console.log(`   Vue应用已挂载: ${appStatus.hasVueApp}`);
      console.log(`   #app有子元素: ${appStatus.hasChildren}`);
      console.log(`   加载动画存在: ${appStatus.hasLoadingSpinner}`);
      console.log(`   加载动画可见: ${appStatus.hasLoadingSpinnerVisible}`);
      console.log(`   路由视图存在: ${appStatus.hasRouterView}`);
      console.log(`   布局组件存在: ${appStatus.hasProfessionalLayout}`);
      console.log(`   页面标题: ${appStatus.title}`);
      console.log(`   当前URL: ${appStatus.url}`);
      
      console.log('\n📄 App内容预览:');
      console.log(appStatus.appHTML);
      
      // 如果应用没有正确启动，尝试手动触发
      if (!appStatus.hasVueApp || !appStatus.hasChildren) {
        console.log('\n🔧 尝试手动诊断...');
        
        // 检查main.ts是否加载
        const mainTsLoaded = await this.page.evaluate(() => {
          const scripts = Array.from(document.querySelectorAll('script'));
          return scripts.some(script => script.src.includes('main.ts'));
        });
        
        console.log(`   main.ts已加载: ${mainTsLoaded}`);
        
        // 检查是否有Vue相关的全局变量
        const vueGlobals = await this.page.evaluate(() => {
          return {
            hasVue: typeof window.Vue !== 'undefined',
            hasCreateApp: typeof window.createApp !== 'undefined',
            hasRouter: typeof window.VueRouter !== 'undefined'
          };
        });
        
        console.log(`   Vue全局变量: ${JSON.stringify(vueGlobals)}`);
        
        // 尝试在控制台中手动创建Vue应用
        try {
          await this.page.evaluate(() => {
            console.log('🔧 尝试手动创建Vue应用...');
            
            // 检查是否可以访问Vue
            if (typeof window.Vue !== 'undefined') {
              console.log('✅ Vue可用');
            } else {
              console.log('❌ Vue不可用');
            }
          });
        } catch (error) {
          console.log(`   手动测试失败: ${error.message}`);
        }
      }
      
      // 截图
      await this.page.screenshot({ 
        path: `simple-app-test-${Date.now()}.png`, 
        fullPage: true 
      });
      console.log('📸 已保存测试截图');
      
      return {
        success: appStatus.hasVueApp && appStatus.hasChildren,
        appStatus,
        errorCount: this.errors.length
      };
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      return {
        success: false,
        error: error.message,
        errorCount: this.errors.length
      };
    }
  }

  async generateReport(result) {
    console.log('\n📊 简单应用测试报告:');
    console.log('='.repeat(60));
    
    if (result.success) {
      console.log('✅ Vue应用启动: 成功');
      console.log('🎉 应用已正确挂载并渲染');
    } else {
      console.log('❌ Vue应用启动: 失败');
      
      if (result.appStatus) {
        console.log('\n🔍 问题分析:');
        if (!result.appStatus.hasVueApp) {
          console.log('   - Vue应用实例未创建或未挂载');
        }
        if (!result.appStatus.hasChildren) {
          console.log('   - #app元素没有子元素，应用可能未渲染');
        }
        if (result.appStatus.hasLoadingSpinnerVisible) {
          console.log('   - 加载动画仍然可见，应用可能卡在启动阶段');
        }
      }
      
      if (this.errors.length > 0) {
        console.log('\n🐛 JavaScript错误:');
        this.errors.forEach((error, index) => {
          console.log(`   ${index + 1}. ${error}`);
        });
      }
    }
    
    console.log(`\n📈 总错误数: ${result.errorCount}`);
    console.log('='.repeat(60));
  }

  async runTest() {
    try {
      await this.init();
      const result = await this.testVueAppStartup();
      await this.generateReport(result);
      
      console.log('\n🎯 简单应用测试完成！');
      
      return result;
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
      return { success: false, error: error.message };
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new SimpleAppTester();
tester.runTest().catch(console.error);
