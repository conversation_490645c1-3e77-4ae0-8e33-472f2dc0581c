<template>
  <div class="live-trading-terminal">
    <!-- 头部控制栏（10%） -->
    <div class="header-control-bar">
      <div class="header-left">
        <!-- 智能搜索框 -->
        <div class="smart-search">
          <el-autocomplete
            v-model="searchInput"
            :fetch-suggestions="searchStocks"
            placeholder="股票代码/拼音缩写 (如PAYH→平安银行)"
            style="width: 300px"
            :loading="searchLoading"
            @select="handleStockSelect"
            clearable
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #default="{ item }">
              <div class="search-suggestion">
                <span class="stock-code">{{ item.symbol }}</span>
                <span class="stock-name">{{ item.name }}</span>
                <span class="stock-price" :class="getPriceClass(item.changePercent)">
                  ¥{{ item.price }}
                </span>
                <span class="stock-change" :class="getPriceClass(item.changePercent)">
                  {{ item.changePercent > 0 ? '+' : '' }}{{ item.changePercent }}%
                </span>
              </div>
            </template>
          </el-autocomplete>
        </div>
      </div>

      <div class="header-center">
        <!-- 资金总览 -->
        <div class="fund-overview">
          <div class="fund-item">
            <span class="fund-label">总资产</span>
            <span class="fund-value">¥{{ formatMoney(accountData.totalAssets) }}</span>
          </div>
          <div class="fund-item">
            <span class="fund-label">可用资金</span>
            <span class="fund-value">¥{{ formatMoney(accountData.availableCash) }}</span>
          </div>
          <div class="fund-item">
            <span class="fund-label">当日盈亏</span>
            <span class="fund-value" :class="accountData.todayPnL >= 0 ? 'profit' : 'loss'">
              {{ accountData.todayPnL >= 0 ? '+' : '' }}¥{{ Math.abs(accountData.todayPnL) }}
            </span>
          </div>
          <el-button
            size="small"
            @click="refreshData"
            :loading="refreshing"
            circle
          >
            <el-icon><Refresh /></el-icon>
          </el-button>
        </div>
      </div>

      <div class="header-right">
        <!-- 模式切换 -->
        <div class="mode-switch">
          <el-radio-group v-model="tradingMode" size="small">
            <el-radio-button value="simple">极简模式</el-radio-button>
            <el-radio-button value="advanced">高级模式</el-radio-button>
          </el-radio-group>
        </div>

        <!-- 连接状态 -->
        <div class="connection-indicator">
          <el-tag
            :type="getConnectionType()"
            size="small"
            class="connection-status"
          >
            <el-icon>
              <component :is="getConnectionIcon()" />
            </el-icon>
            {{ getConnectionText() }}
          </el-tag>
        </div>

        <!-- 设置按钮 -->
        <el-button size="small" @click="showConfigDialog = true" circle>
          <el-icon><Setting /></el-icon>
        </el-button>
      </div>
    </div>

    <!-- 连接状态提示 -->
    <el-alert
      v-if="connectionStatus !== 'connected'"
      title="MiniQMT连接提示"
      type="warning"
      show-icon
      :closable="false"
      class="connection-alert"
    >
      <template #default>
        <div>
          <p>请确保MiniQMT客户端已启动并配置正确，然后点击右上角连接按钮。</p>
          <ul>
            <li>确认MiniQMT客户端正在运行</li>
            <li>检查API接口配置（默认端口：58609）</li>
            <li>验证账户信息和交易权限</li>
          </ul>
          <el-button type="primary" @click="connectToMiniQMT" :loading="connecting">
            <el-icon><Link /></el-icon>
            连接MiniQMT
          </el-button>
        </div>
      </template>
    </el-alert>

    <!-- 主要交易区域（70%） + 辅助功能区（30%） -->
    <div v-if="connectionStatus === 'connected'" class="main-trading-area">
      <!-- 核心交易区（70%） -->
      <div class="core-trading-zone">
        <!-- 行情图表（左侧35%） -->
        <div class="chart-section">
          <div class="chart-header">
            <div class="stock-info" v-if="selectedStock">
              <h3 class="stock-title">
                {{ selectedStock.name }}({{ selectedStock.symbol }})
                <span class="current-price" :class="getPriceClass(selectedStock.changePercent)">
                  ¥{{ selectedStock.price }}
                </span>
                <span class="price-change" :class="getPriceClass(selectedStock.changePercent)">
                  {{ selectedStock.changePercent > 0 ? '+' : '' }}{{ selectedStock.changePercent }}%
                </span>
              </h3>
              <div class="price-limits">
                <span class="limit-up">涨停{{ selectedStock.limitUp }}</span>
                <span class="limit-down">跌停{{ selectedStock.limitDown }}</span>
              </div>
            </div>
            <div class="chart-controls">
              <el-radio-group v-model="chartType" size="small">
                <el-radio-button value="timeline">分时</el-radio-button>
                <el-radio-button value="kline">K线</el-radio-button>
              </el-radio-group>
            </div>
          </div>

          <div class="chart-container">
            <KLineChart
              v-if="selectedStock && chartData.length > 0"
              :symbol="selectedStock.symbol"
              :symbol-name="selectedStock.name"
              :chart-data="chartData"
              :selected-period="selectedPeriod"
              height="400px"
              @period-change="handlePeriodChange"
              @data-update="handleChartDataUpdate"
              @right-click="handleChartRightClick"
            />
            <div v-else class="chart-placeholder">
              <el-empty description="请选择股票查看行情图表" />
            </div>
          </div>

          <!-- 技术指标（高级模式） -->
          <div v-if="tradingMode === 'advanced'" class="technical-indicators">
            <el-tabs v-model="activeIndicator" size="small">
              <el-tab-pane label="成交量" name="volume" />
              <el-tab-pane label="MACD" name="macd" />
              <el-tab-pane label="KDJ" name="kdj" />
            </el-tabs>
          </div>
        </div>

        <!-- 下单面板（中央30%） -->
        <div class="order-panel">
          <div class="panel-header">
            <div class="selected-stock-info" v-if="selectedStock">
              <span class="stock-display">{{ selectedStock.name }}({{ selectedStock.symbol }})</span>
              <span class="current-price" :class="getPriceClass(selectedStock.changePercent)">
                ¥{{ selectedStock.price }} {{ selectedStock.changePercent > 0 ? '↑' : '↓' }}{{ Math.abs(selectedStock.changePercent) }}%
              </span>
            </div>
            <div v-else class="no-stock-selected">
              请先选择股票
            </div>
          </div>

          <!-- 大按钮交易区 -->
          <div class="quick-trade-buttons">
            <el-button
              type="success"
              size="large"
              class="trade-button buy-button"
              @click="quickTrade('buy')"
              :disabled="!selectedStock"
            >
              市价买入
            </el-button>
            <el-button
              type="danger"
              size="large"
              class="trade-button sell-button"
              @click="quickTrade('sell')"
              :disabled="!selectedStock"
            >
              限价卖出
            </el-button>
            <el-button
              type="warning"
              size="large"
              class="trade-button cancel-button"
              @click="cancelAllOrders"
              :disabled="activeOrders.length === 0"
            >
              一键撤单
            </el-button>
          </div>

          <!-- 详细下单表单 -->
          <div class="order-form-section">
            <el-form :model="orderForm" label-width="60px" size="small">
              <!-- 交易方向 -->
              <el-form-item label="方向">
                <el-radio-group v-model="orderForm.side" @change="handleSideChange">
                  <el-radio-button value="buy" class="buy-radio">买入</el-radio-button>
                  <el-radio-button value="sell" class="sell-radio">卖出</el-radio-button>
                </el-radio-group>
              </el-form-item>

              <!-- 价格设置 -->
              <el-form-item label="价格">
                <div class="price-input-group">
                  <el-input-number
                    v-model="orderForm.price"
                    :precision="2"
                    :step="0.01"
                    :min="0"
                    style="width: 100%"
                    size="small"
                    @change="calculateAmount"
                  />
                  <div class="price-limits-info" v-if="selectedStock">
                    <span class="limit-up-price">涨停{{ selectedStock.limitUp }}</span>
                    <span class="limit-down-price">跌停{{ selectedStock.limitDown }}</span>
                  </div>
                </div>
              </el-form-item>

              <!-- 数量设置 -->
              <el-form-item label="数量">
                <div class="quantity-input-group">
                  <el-input-number
                    v-model="orderForm.quantity"
                    :min="100"
                    :step="100"
                    style="width: 100%"
                    size="small"
                    @change="calculateAmount"
                  />
                  <div class="quantity-shortcuts">
                    <el-button size="small" @click="setQuantity(100)">1手</el-button>
                    <el-button size="small" @click="setQuantity(500)">5手</el-button>
                    <el-button size="small" @click="setQuantity(1000)">10手</el-button>
                    <el-button size="small" @click="setMaxQuantity">{{ orderForm.side === 'buy' ? '全仓' : '全卖' }}</el-button>
                  </div>
                  <div class="quantity-info">
                    <span v-if="orderForm.side === 'buy'">可买：{{ maxBuyQuantity }}股</span>
                    <span v-else>可卖：{{ maxSellQuantity }}股</span>
                  </div>
                </div>
              </el-form-item>

              <!-- 预估信息 -->
              <el-form-item label="预估">
                <div class="estimate-info">
                  <div class="estimate-row">
                    <span>金额：¥{{ estimatedAmount }}</span>
                    <span>佣金：¥{{ commission }}</span>
                  </div>
                  <div class="estimate-row" v-if="orderForm.side === 'sell'">
                    <span>印花税：¥{{ stampTax }}</span>
                  </div>
                </div>
              </el-form-item>

              <!-- 闪电确认 -->
              <el-form-item>
                <el-checkbox v-model="flashConfirm" size="small">
                  闪电确认⚡（跳过弹窗）
                </el-checkbox>
              </el-form-item>

              <!-- 提交按钮 -->
              <el-form-item>
                <el-button
                  :type="orderForm.side === 'buy' ? 'success' : 'danger'"
                  size="large"
                  style="width: 100%"
                  @click="submitOrder"
                  :loading="submitting"
                  :disabled="!canSubmitOrder"
                >
                  {{ orderForm.side === 'buy' ? '买入' : '卖出' }}
                </el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 深度盘口（右侧35%） -->
        <div class="depth-panel">
          <div class="depth-header">
            <h4>五档行情</h4>
            <el-switch
              v-model="showLevel2"
              size="small"
              active-text="Level-2"
              inactive-text="Level-1"
            />
          </div>

          <div class="depth-book">
            <!-- 卖档 -->
            <div class="sell-orders">
              <div class="depth-header-row">
                <span>卖档</span>
                <span>价格</span>
                <span>数量</span>
              </div>
              <div
                v-for="(item, index) in sellDepth"
                :key="`sell-${index}`"
                class="depth-row sell-row"
                @click="setOrderPrice(item.price)"
                @contextmenu.prevent="quickFollowOrder(item.price, 'sell')"
              >
                <span class="depth-level">卖{{ 5 - index }}</span>
                <span class="depth-price sell-price">{{ item.price }}</span>
                <span class="depth-volume" :class="{ 'large-order': item.volume > 50000 }">
                  {{ formatVolume(item.volume) }}
                  <span v-if="item.volume > 50000" class="large-order-indicator">📈</span>
                </span>
              </div>
            </div>

            <!-- 最新价分隔线 -->
            <div class="latest-price-divider">
              <span class="latest-price" :class="getPriceClass(selectedStock?.changePercent || 0)">
                {{ selectedStock?.price || '--' }}
              </span>
              <span class="price-change" :class="getPriceClass(selectedStock?.changePercent || 0)">
                {{ selectedStock?.changePercent > 0 ? '+' : '' }}{{ selectedStock?.changePercent || 0 }}%
              </span>
            </div>

            <!-- 买档 -->
            <div class="buy-orders">
              <div
                v-for="(item, index) in buyDepth"
                :key="`buy-${index}`"
                class="depth-row buy-row"
                @click="setOrderPrice(item.price)"
                @contextmenu.prevent="quickFollowOrder(item.price, 'buy')"
              >
                <span class="depth-level">买{{ index + 1 }}</span>
                <span class="depth-price buy-price">{{ item.price }}</span>
                <span class="depth-volume" :class="{ 'large-order': item.volume > 50000 }">
                  {{ formatVolume(item.volume) }}
                  <span v-if="item.volume > 50000" class="large-order-indicator">📈</span>
                </span>
              </div>
            </div>
          </div>

          <!-- 逐笔成交（Level-2数据） -->
          <div v-if="showLevel2" class="tick-trades">
            <div class="tick-header">
              <span>时间</span>
              <span>价格</span>
              <span>量</span>
              <span>性质</span>
            </div>
            <div class="tick-list">
              <div
                v-for="tick in recentTicks"
                :key="tick.id"
                class="tick-row"
              >
                <span class="tick-time">{{ formatTime(tick.time) }}</span>
                <span class="tick-price" :class="getPriceClass(tick.change)">{{ tick.price }}</span>
                <span class="tick-volume">{{ formatVolume(tick.volume) }}</span>
                <span class="tick-type" :class="tick.type">{{ getTickTypeText(tick.type) }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 辅助功能区（30%） -->
      <div class="auxiliary-zone">
        <!-- 账户持仓（上50%） -->
        <div class="position-section">
          <div class="section-header">
            <h4>账户持仓</h4>
            <el-button size="small" @click="refreshPositions" :loading="loading.positions">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>

          <div class="position-table">
            <el-table
              :data="positions"
              size="small"
              stripe
              style="width: 100%"
              max-height="200"
              @row-click="handlePositionClick"
            >
              <el-table-column prop="name" label="名称" width="80" />
              <el-table-column prop="avgCost" label="成本价" width="70">
                <template #default="{ row }">
                  ¥{{ (row.avgCost || 0).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="currentPrice" label="现价" width="70">
                <template #default="{ row }">
                  <span :class="getPriceClass(row.changePercent)">
                    ¥{{ (row.currentPrice || 0).toFixed(2) }}
                  </span>
                </template>
              </el-table-column>
              <el-table-column prop="pnlPercent" label="盈亏率" width="80">
                <template #default="{ row }">
                  <span :class="(row.pnl || 0) >= 0 ? 'profit' : 'loss'">
                    {{ (row.pnl || 0) >= 0 ? '+' : '' }}{{ (row.pnlPercent || 0).toFixed(2) }}%
                  </span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="120">
                <template #default="{ row }">
                  <el-button size="small" type="success" @click.stop="quickBuy(row)">
                    补仓
                  </el-button>
                  <el-button size="small" type="danger" @click.stop="quickSell(row)">
                    止盈
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 委托队列（中30%） -->
        <div class="order-queue-section">
          <div class="section-header">
            <h4>委托队列</h4>
            <div class="queue-actions">
              <el-button
                size="small"
                type="warning"
                @click="cancelAllOrders"
                :disabled="activeOrders.length === 0"
              >
                全撤 (Ctrl+C)
              </el-button>
            </div>
          </div>

          <div class="order-queue">
            <el-table
              :data="orders"
              size="small"
              stripe
              style="width: 100%"
              max-height="150"
            >
              <el-table-column prop="symbol" label="代码" width="60" />
              <el-table-column prop="side" label="方向" width="50">
                <template #default="{ row }">
                  <el-tag
                    :type="row.side === 'buy' ? 'success' : 'danger'"
                    size="small"
                  >
                    {{ row.side === 'buy' ? '买' : '卖' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="price" label="价格" width="60">
                <template #default="{ row }">
                  ¥{{ (row.price || 0).toFixed(2) }}
                </template>
              </el-table-column>
              <el-table-column prop="quantity" label="数量" width="60" />
              <el-table-column prop="status" label="状态" width="70">
                <template #default="{ row }">
                  <el-tag :type="getOrderStatusType(row.status)" size="small">
                    {{ getOrderStatusText(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="60">
                <template #default="{ row }">
                  <el-button
                    v-if="['pending', 'partial'].includes(row.status)"
                    size="small"
                    type="warning"
                    @click="cancelOrder(row)"
                  >
                    撤
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </div>

        <!-- 智能提示（下20%） -->
        <div class="smart-alerts-section">
          <div class="section-header">
            <h4>智能提示</h4>
          </div>

          <div class="alerts-container">
            <!-- 实时预警 -->
            <div class="real-time-alerts">
              <div
                v-for="alert in smartAlerts"
                :key="alert.id"
                class="alert-item"
                :class="alert.type"
              >
                <el-icon>
                  <component :is="getAlertIcon(alert.type)" />
                </el-icon>
                <span class="alert-text">{{ alert.message }}</span>
                <span class="alert-time">{{ formatTime(alert.time) }}</span>
              </div>
            </div>

            <!-- 规则提醒 -->
            <div class="rule-reminders">
              <div v-if="!isMarketOpen" class="reminder-item warning">
                <el-icon><Warning /></el-icon>
                <span>非交易时段，委托将在下一交易日生效</span>
              </div>
              <div v-if="riskLevel > 0.8" class="reminder-item danger">
                <el-icon><Warning /></el-icon>
                <span>当前仓位较重，请注意风险控制</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部状态栏（5%） -->
    <div class="bottom-status-bar">
      <div class="status-left">
        <!-- 连接状态详情 -->
        <div class="connection-details">
          <span class="status-indicator" :class="getConnectionStatusClass()">●</span>
          <span class="status-text">{{ getDetailedConnectionStatus() }}</span>
        </div>

        <!-- 行情延迟 -->
        <div class="market-delay">
          <span>行情延迟: {{ marketDelay }}ms</span>
        </div>
      </div>

      <div class="status-center">
        <!-- 风险提示滚动 -->
        <div class="risk-notice">
          <div class="scrolling-text">市场有风险，投资需谨慎 | 理性投资，远离非法证券活动</div>
        </div>
      </div>

      <div class="status-right">
        <!-- 快捷键提示 -->
        <div class="hotkey-tips">
          <span>F1:买入 F2:卖出 Esc:清空 Ctrl+C:全撤</span>
        </div>
      </div>
    </div>

    <!-- 账户信息 -->
    <div v-if="connectionStatus === 'connected'" class="account-info">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>账户资产</span>
              <el-icon class="card-icon"><Money /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.totalAssets || 0) }}</span>
              <span class="change positive">+{{ (((accountData.todayPnL || 0) / (accountData.totalAssets || 1)) * 100).toFixed(2) }}%</span>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>可用资金</span>
              <el-icon class="card-icon"><Wallet /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.availableCash) }}</span>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>持仓市值</span>
              <el-icon class="card-icon"><TrendCharts /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.positionValue) }}</span>
            </div>
          </el-card>
        </el-col>

        <el-col :span="6">
          <el-card class="info-card" shadow="hover">
            <div class="card-header">
              <span>今日盈亏</span>
              <el-icon class="card-icon"><Trophy /></el-icon>
            </div>
            <div class="card-value">
              <span class="amount">¥{{ formatMoney(accountData.todayPnL) }}</span>
              <span :class="['change', accountData.todayPnL >= 0 ? 'positive' : 'negative']">
                {{ accountData.todayPnL >= 0 ? '+' : '' }}¥{{ Math.abs(accountData.todayPnL) }}
              </span>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>






    <!-- MiniQMT配置对话框 -->
    <el-dialog
      v-model="showConfigDialog"
      title="MiniQMT配置"
      width="600px"
    >
      <el-form :model="qmtConfig" label-width="120px">
        <el-form-item label="服务器地址">
          <el-input v-model="qmtConfig.host" placeholder="localhost" />
        </el-form-item>

        <el-form-item label="API端口">
          <el-input-number v-model="qmtConfig.port" :min="1" :max="65535" style="width: 100%" />
        </el-form-item>

        <el-form-item label="账户编号">
          <el-input v-model="qmtConfig.account" placeholder="请输入交易账户编号" />
        </el-form-item>

        <el-form-item label="启用SSL">
          <el-switch v-model="qmtConfig.ssl" />
        </el-form-item>

        <el-form-item label="连接超时">
          <el-input-number v-model="qmtConfig.timeout" :min="1" :max="60" style="width: 100%" />
          <span style="margin-left: 8px; color: #909399;">秒</span>
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showConfigDialog = false">取消</el-button>
        <el-button type="primary" @click="saveConfig">保存配置</el-button>
      </template>
    </el-dialog>

    <!-- 订单确认对话框 -->
    <el-dialog
      v-model="showOrderConfirm"
      title="订单确认"
      width="400px"
    >
      <div class="order-confirm">
        <el-alert
          title="请仔细核对订单信息"
          type="warning"
          :closable="false"
          show-icon
        />

        <div class="confirm-details">
          <div class="detail-row">
            <span>股票：</span>
            <span>{{ orderForm.symbol }}</span>
          </div>
          <div class="detail-row">
            <span>方向：</span>
            <span :class="orderSide === 'buy' ? 'buy-text' : 'sell-text'">
              {{ orderSide === 'buy' ? '买入' : '卖出' }}
            </span>
          </div>
          <div class="detail-row">
            <span>数量：</span>
            <span>{{ orderForm.quantity }}股</span>
          </div>
          <div class="detail-row">
            <span>价格：</span>
            <span>¥{{ (orderForm.price || 0).toFixed(2) }}</span>
          </div>
          <div class="detail-row">
            <span>预估金额：</span>
            <span>¥{{ estimatedAmount }}</span>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showOrderConfirm = false">取消</el-button>
        <el-button
          :type="orderSide === 'buy' ? 'success' : 'danger'"
          @click="submitOrder"
          :loading="submitting"
        >
          确认{{ orderSide === 'buy' ? '买入' : '卖出' }}
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Coin,
  Connection,
  Link,
  Setting,
  Refresh,
  Money,
  Wallet,
  TrendCharts,
  Trophy,
  Search,
  Warning,
  InfoFilled,
  SuccessFilled,
  CircleCheck
} from '@element-plus/icons-vue'
import KLineChart from '@/components/charts/KLineChart/index.vue'
import { useTradingStore } from '@/stores/modules/trading'
import { useMarketStore } from '@/stores/modules/market'
import { tradingApi } from '@/api'
import type { SubmitOrderRequest } from '@/api/trading'

// Store
const tradingStore = useTradingStore()
const marketStore = useMarketStore()

// 响应式数据
const connectionStatus = ref<'connected' | 'disconnected' | 'connecting'>('connected') // 默认连接状态用于演示
const connecting = ref(false)
const refreshing = ref(false)
const showConfigDialog = ref(false)
const showOrderConfirm = ref(false)
const submitting = ref(false)
const searchLoading = ref(false)

// 界面状态
const tradingMode = ref<'simple' | 'advanced'>('simple')
const chartType = ref<'timeline' | 'kline'>('timeline')
const activeIndicator = ref('volume')
const showLevel2 = ref(true)
const flashConfirm = ref(false)

// 选中的股票和搜索
const selectedStock = ref<any>(null)
const searchInput = ref('')
const orderSide = ref<'buy' | 'sell'>('buy')

// 图表数据
const chartData = ref<any[]>([])
const selectedPeriod = ref('1d')

// 加载状态
const loading = reactive({
  positions: false,
  orders: false,
  trades: false
})

// MiniQMT配置
const qmtConfig = reactive({
  host: 'localhost',
  port: 58609,
  account: '',
  ssl: false,
  timeout: 10
})

// 账户数据
const accountData = reactive({
  totalAssets: 0,
  availableCash: 0,
  positionValue: 0,
  todayPnL: 0
})

// 下单表单
const orderForm = reactive({
  side: 'buy' as 'buy' | 'sell',
  symbol: '',
  orderType: 'limit',
  price: 0,
  quantity: 100,
  stopLoss: 0,
  takeProfit: 0
})

// 深度数据
const buyDepth = ref([
  { price: 12.37, volume: 15600 },
  { price: 12.36, volume: 23400 },
  { price: 12.35, volume: 45600 },
  { price: 12.34, volume: 67800 },
  { price: 12.33, volume: 34500 }
])

const sellDepth = ref([
  { price: 12.38, volume: 18900 },
  { price: 12.39, volume: 34500 },
  { price: 12.40, volume: 56700 },
  { price: 12.41, volume: 78900 },
  { price: 12.42, volume: 23400 }
])

// 逐笔成交数据
const recentTicks = ref([
  { id: 1, time: new Date(), price: 12.37, volume: 500, change: 0.01, type: 'buy' },
  { id: 2, time: new Date(), price: 12.36, volume: 300, change: -0.01, type: 'sell' },
  { id: 3, time: new Date(), price: 12.37, volume: 800, change: 0.01, type: 'buy' }
])

// 持仓数据
const positions = ref([
  {
    symbol: '000001',
    name: '平安银行',
    quantity: 1000,
    availableQty: 1000,
    avgCost: 12.00,
    currentPrice: 12.36,
    pnl: 360,
    pnlPercent: 3.0,
    changePercent: 2.5
  },
  {
    symbol: '000858',
    name: '五粮液',
    quantity: 200,
    availableQty: 200,
    avgCost: 175.00,
    currentPrice: 180.00,
    pnl: 1000,
    pnlPercent: 2.86,
    changePercent: 1.8
  }
])

// 订单数据
const orders = ref([
  {
    orderNo: 'QMT001',
    symbol: '000001',
    side: 'buy',
    quantity: 500,
    price: 12.30,
    status: 'pending'
  }
])

// 智能提示数据
const smartAlerts = ref([
  {
    id: 1,
    type: 'info',
    message: '平安银行主力资金连续流入',
    time: new Date()
  },
  {
    id: 2,
    type: 'warning',
    message: '五粮液涨速>3%触发突破预警',
    time: new Date()
  }
])

// 市场状态
const isMarketOpen = ref(true)
const marketDelay = ref(156)
const riskLevel = ref(0.65)

// 计算属性
const estimatedAmount = computed(() => {
  const price = orderForm.orderType === 'market' ? (selectedStock.value?.price || 0) : (orderForm.price || 0)
  return ((price || 0) * (orderForm.quantity || 0)).toFixed(2)
})

const commission = computed(() => {
  const amount = parseFloat(estimatedAmount.value) || 0
  return Math.max(amount * 0.0003, 5).toFixed(2) // 万三手续费，最低5元
})

const stampTax = computed(() => {
  if (orderForm.side === 'sell') {
    const amount = parseFloat(estimatedAmount.value) || 0
    return (amount * 0.001).toFixed(2) // 卖出时收取千分之一印花税
  }
  return '0.00'
})

const canSubmitOrder = computed(() => {
  return selectedStock.value && orderForm.quantity > 0 &&
         (orderForm.orderType === 'market' || orderForm.price > 0)
})

const maxBuyQuantity = computed(() => {
  if (!selectedStock.value || !orderForm.price) return 0
  return Math.floor(accountData.availableCash / orderForm.price / 100) * 100
})

const maxSellQuantity = computed(() => {
  if (!selectedStock.value) return 0
  const position = positions.value.find(p => p.symbol === selectedStock.value.symbol)
  return position ? position.availableQty : 0
})

const activeOrders = computed(() => {
  return orders.value.filter(order => ['pending', 'partial'].includes(order.status))
})

// 方法
const formatMoney = (amount: number) => {
  return new Intl.NumberFormat('zh-CN').format(amount)
}

const formatVolume = (volume: number) => {
  const vol = volume || 0
  if (vol >= *********) {
    return (vol / *********).toFixed(1) + '亿'
  } else if (vol >= 10000) {
    return (vol / 10000).toFixed(1) + '万'
  }
  return vol.toString()
}

const formatDateTime = (date: Date) => {
  return date.toLocaleString('zh-CN')
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { hour12: false })
}

// 价格颜色类
const getPriceClass = (changePercent: number) => {
  if (changePercent > 0) return 'price-up'
  if (changePercent < 0) return 'price-down'
  return 'price-flat'
}

// 连接状态相关
const getConnectionType = () => {
  const statusMap = {
    'connected': 'success',
    'connecting': 'warning',
    'disconnected': 'danger'
  }
  return statusMap[connectionStatus.value] || 'info'
}

const getConnectionIcon = () => {
  const iconMap = {
    'connected': 'CircleCheck',
    'connecting': 'Loading',
    'disconnected': 'Warning'
  }
  return iconMap[connectionStatus.value] || 'Warning'
}

const getConnectionText = () => {
  const textMap = {
    'connected': 'MiniQMT已连接',
    'connecting': '连接中...',
    'disconnected': 'MiniQMT未连接'
  }
  return textMap[connectionStatus.value] || '未知状态'
}

const getConnectionStatusClass = () => {
  const classMap = {
    'connected': 'status-connected',
    'connecting': 'status-connecting',
    'disconnected': 'status-disconnected'
  }
  return classMap[connectionStatus.value] || 'status-unknown'
}

const getDetailedConnectionStatus = () => {
  if (connectionStatus.value === 'connected') {
    return `MiniQMT连接正常 | 交易通道畅通`
  } else if (connectionStatus.value === 'connecting') {
    return `正在连接MiniQMT...`
  } else {
    return `MiniQMT连接断开 | 请检查客户端`
  }
}

const connectToMiniQMT = async () => {
  connecting.value = true
  connectionStatus.value = 'connecting'

  try {
    // 模拟连接MiniQMT
    await new Promise(resolve => setTimeout(resolve, 2000))

    // 连接成功后加载数据
    connectionStatus.value = 'connected'
    await loadAccountData()
    await loadPositions()
    await loadOrders()
    await loadTrades()

    ElMessage.success('MiniQMT连接成功')
  } catch (error) {
    connectionStatus.value = 'disconnected'
    ElMessage.error('MiniQMT连接失败，请检查配置和网络')
  } finally {
    connecting.value = false
  }
}

const loadAccountData = async () => {
  // 模拟加载账户数据
  accountData.totalAssets = 1000000
  accountData.availableCash = 800000
  accountData.positionValue = 200000
  accountData.todayPnL = 5000
}

const loadPositions = async () => {
  // 模拟加载持仓数据
  positions.value = [
    {
      symbol: '000001',
      name: '平安银行',
      quantity: 1000,
      availableQty: 1000,
      avgCost: 12.00,
      currentPrice: 12.50,
      pnl: 500
    },
    {
      symbol: '000858',
      name: '五粮液',
      quantity: 200,
      availableQty: 200,
      avgCost: 175.00,
      currentPrice: 180.00,
      pnl: 1000
    }
  ]
}

const loadOrders = async () => {
  // 模拟加载订单数据
  orders.value = [
    {
      orderNo: 'QMT001',
      symbol: '000001',
      side: 'buy',
      quantity: 500,
      price: 12.30,
      status: 'pending'
    }
  ]
}

const loadTrades = async () => {
  // 模拟加载成交数据
  trades.value = [
    {
      tradeNo: 'T001',
      symbol: '000001',
      side: 'buy',
      quantity: 500,
      price: 12.25,
      amount: 6125,
      tradeTime: new Date()
    }
  ]
}

// 股票搜索
const searchStocks = async (queryString: string, callback: Function) => {
  searchLoading.value = true

  // 模拟股票搜索，支持拼音缩写
  const mockStocks = [
    { symbol: '000001', name: '平安银行', price: 12.36, changePercent: 2.5, pinyin: 'PAYH' },
    { symbol: '000002', name: '万科A', price: 20.80, changePercent: -1.2, pinyin: 'WKA' },
    { symbol: '000858', name: '五粮液', price: 180.00, changePercent: 1.8, pinyin: 'WLY' },
    { symbol: '600036', name: '招商银行', price: 42.50, changePercent: 0.8, pinyin: 'ZSYH' },
    { symbol: '600519', name: '贵州茅台', price: 1680.00, changePercent: -0.5, pinyin: 'GZMT' }
  ]

  const results = queryString
    ? mockStocks.filter(stock =>
        stock.symbol.includes(queryString.toUpperCase()) ||
        stock.name.includes(queryString) ||
        stock.pinyin.includes(queryString.toUpperCase())
      )
    : mockStocks.slice(0, 5)

  setTimeout(() => {
    searchLoading.value = false
    callback(results)
  }, 200)
}

// 股票选择处理
const handleStockSelect = (item: any) => {
  selectedStock.value = {
    ...item,
    open: item.price * 0.99,
    high: item.price * 1.02,
    low: item.price * 0.97,
    volume: Math.floor(Math.random() * 1000000) + 100000,
    change: ((item.price || 0) * (item.changePercent || 0) / 100),
    limitUp: ((item.price || 0) * 1.1).toFixed(2),
    limitDown: ((item.price || 0) * 0.9).toFixed(2)
  }
  orderForm.price = item.price
  orderForm.symbol = item.symbol

  // 加载图表数据
  loadChartData(item.symbol)
}

// 加载图表数据
const loadChartData = async (symbol: string) => {
  // 模拟加载图表数据
  chartData.value = [
    // 这里应该是真实的K线数据
  ]
}

// 图表相关处理
const handlePeriodChange = (period: string) => {
  selectedPeriod.value = period
  if (selectedStock.value) {
    loadChartData(selectedStock.value.symbol)
  }
}

const handleChartDataUpdate = (data: any) => {
  chartData.value = data
}

const handleChartRightClick = (event: any) => {
  // 右键菜单快速下单
  const price = event.price
  if (price) {
    orderForm.price = price
  }
}

// 交易相关方法
const quickTrade = (side: 'buy' | 'sell') => {
  if (!selectedStock.value) {
    ElMessage.warning('请先选择股票')
    return
  }

  orderForm.side = side
  if (side === 'buy') {
    orderForm.price = sellDepth.value[0]?.price || selectedStock.value.price
  } else {
    orderForm.price = buyDepth.value[0]?.price || selectedStock.value.price
  }

  if (flashConfirm.value) {
    submitOrder()
  } else {
    showOrderConfirm.value = true
  }
}

const handleSideChange = (side: 'buy' | 'sell') => {
  orderForm.side = side
  // 自动调整价格
  if (selectedStock.value) {
    if (side === 'buy') {
      orderForm.price = sellDepth.value[0]?.price || selectedStock.value.price
    } else {
      orderForm.price = buyDepth.value[0]?.price || selectedStock.value.price
    }
  }
}

const setOrderPrice = (price: number) => {
  orderForm.price = price
}

const quickFollowOrder = (price: number, side: 'buy' | 'sell') => {
  orderForm.price = price
  orderForm.side = side
  ElMessage.success(`已设置${side === 'buy' ? '买入' : '卖出'}价格: ¥${price}`)
}

const calculateAmount = () => {
  // 计算预估金额，触发响应式更新
}

const setQuantity = (quantity: number) => {
  orderForm.quantity = quantity
}

const setMaxQuantity = () => {
  if (orderForm.side === 'buy') {
    orderForm.quantity = maxBuyQuantity.value
  } else {
    orderForm.quantity = maxSellQuantity.value
  }
}

// 深度盘口相关
const getTickTypeText = (type: string) => {
  const typeMap = {
    'buy': '买盘',
    'sell': '卖盘',
    'neutral': '中性'
  }
  return typeMap[type] || '未知'
}

// 智能提示相关
const getAlertIcon = (type: string) => {
  const iconMap = {
    'info': 'InfoFilled',
    'warning': 'Warning',
    'success': 'SuccessFilled',
    'danger': 'Warning'
  }
  return iconMap[type] || 'InfoFilled'
}



const confirmOrder = () => {
  if (!canSubmitOrder.value) {
    ElMessage.warning('请完善订单信息')
    return
  }

  showOrderConfirm.value = true
}

const submitOrder = async () => {
  submitting.value = true

  try {
    // 模拟提交订单到MiniQMT
    await new Promise(resolve => setTimeout(resolve, 1000))

    const newOrder = {
      orderNo: `QMT${Date.now()}`,
      symbol: orderForm.symbol.split(' ')[0],
      side: orderSide.value,
      quantity: orderForm.quantity,
      price: orderForm.price,
      status: 'pending'
    }

    orders.value.unshift(newOrder)

    ElMessage.success('订单提交成功')
    showOrderConfirm.value = false

    // 重置表单
    orderForm.symbol = ''
    orderForm.quantity = 100
    orderForm.price = 0
    selectedStock.value = null

  } catch (error) {
    ElMessage.error('订单提交失败')
  } finally {
    submitting.value = false
  }
}

const quickSell = (position: any) => {
  orderSide.value = 'sell'
  orderForm.symbol = `${position.symbol} ${position.name}`
  orderForm.quantity = position.availableQty
  orderForm.price = position.currentPrice

  handleStockSelect({
    symbol: position.symbol,
    name: position.name,
    price: position.currentPrice
  })
}

const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm('确认撤销该订单？', '撤单确认', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 模拟撤单
    await new Promise(resolve => setTimeout(resolve, 500))

    const index = orders.value.findIndex(o => o.orderNo === order.orderNo)
    if (index !== -1) {
      orders.value[index].status = 'cancelled'
    }

    ElMessage.success('订单已撤销')
  } catch {
    // 用户取消
  }
}

const getOrderStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return statusMap[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return statusMap[status] || '未知'
}

const saveConfig = () => {
  ElMessage.success('配置已保存')
  showConfigDialog.value = false
}

const refreshData = async () => {
  refreshing.value = true
  try {
    if (connectionStatus.value === 'connected') {
      await Promise.all([
        loadAccountData(),
        loadPositions(),
        loadOrders(),
        loadTrades()
      ])
      ElMessage.success('数据已刷新')
    }
  } finally {
    refreshing.value = false
  }
}

const refreshPositions = async () => {
  loading.positions = true
  try {
    await loadPositions()
  } finally {
    loading.positions = false
  }
}

const handlePositionClick = (position: any) => {
  // 点击持仓行，自动选择该股票
  const stockInfo = {
    symbol: position.symbol,
    name: position.name,
    price: position.currentPrice,
    changePercent: position.changePercent || 0
  }
  handleStockSelect(stockInfo)
}

const quickBuy = (position: any) => {
  orderForm.side = 'buy'
  orderForm.symbol = position.symbol
  orderForm.price = position.currentPrice
  orderForm.quantity = 100

  handleStockSelect({
    symbol: position.symbol,
    name: position.name,
    price: position.currentPrice,
    changePercent: position.changePercent || 0
  })
}



const cancelAllOrders = async () => {
  if (activeOrders.value.length === 0) {
    ElMessage.warning('没有可撤销的订单')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确认撤销所有${activeOrders.value.length}个订单？`,
      '批量撤单确认',
      {
        confirmButtonText: '确认撤销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 模拟批量撤单
    for (const order of activeOrders.value) {
      const index = orders.value.findIndex(o => o.orderNo === order.orderNo)
      if (index !== -1) {
        orders.value[index].status = 'cancelled'
      }
    }

    ElMessage.success(`已撤销${activeOrders.value.length}个订单`)
  } catch {
    // 用户取消
  }
}



// 键盘快捷键处理
const handleKeydown = (event: KeyboardEvent) => {
  // F1: 快速买入
  if (event.key === 'F1') {
    event.preventDefault()
    if (selectedStock.value) {
      orderForm.side = 'buy'
      if (sellDepth.value[0]) {
        orderForm.price = sellDepth.value[0].price
      }
    }
  }

  // F2: 快速卖出
  if (event.key === 'F2') {
    event.preventDefault()
    if (selectedStock.value) {
      orderForm.side = 'sell'
      if (buyDepth.value[0]) {
        orderForm.price = buyDepth.value[0].price
      }
    }
  }

  // Esc: 清空输入
  if (event.key === 'Escape') {
    event.preventDefault()
    orderForm.symbol = ''
    orderForm.price = 0
    orderForm.quantity = 100
    selectedStock.value = null
  }

  // Ctrl+C: 全撤
  if (event.ctrlKey && event.key === 'c') {
    event.preventDefault()
    cancelAllOrders()
  }
}

onMounted(() => {
  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeydown)

  // 自动连接（演示用）
  connectToMiniQMT()

  // 默认选择一只股票用于演示
  setTimeout(() => {
    handleStockSelect({
      symbol: '000001',
      name: '平安银行',
      price: 12.36,
      changePercent: 2.5,
      pinyin: 'PAYH'
    })
  }, 1000)

  // 模拟实时数据更新
  const updateInterval = setInterval(() => {
    if (selectedStock.value) {
      // 模拟价格波动
      const change = (Math.random() - 0.5) * 0.02
      selectedStock.value.price = Math.max(0.01, selectedStock.value.price + change)
      selectedStock.value.changePercent = ((selectedStock.value.price - 12.00) / 12.00 * 100)

      // 更新深度数据
      buyDepth.value.forEach(item => {
        item.volume += Math.floor((Math.random() - 0.5) * 1000)
        item.volume = Math.max(100, item.volume)
      })

      sellDepth.value.forEach(item => {
        item.volume += Math.floor((Math.random() - 0.5) * 1000)
        item.volume = Math.max(100, item.volume)
      })
    }

    // 更新市场延迟
    marketDelay.value = 100 + Math.floor(Math.random() * 100)
  }, 2000)

  // 清理定时器
  onUnmounted(() => {
    clearInterval(updateInterval)
  })
})

onUnmounted(() => {
  // 清理键盘事件监听
  document.removeEventListener('keydown', handleKeydown)
})
</script>

<style scoped>
.live-trading-terminal {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background: #f5f7fa;
  font-family: 'Microsoft YaHei', sans-serif;
}

/* 头部控制栏（10%） */
.header-control-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px;
  padding: 0 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.smart-search {
  display: flex;
  align-items: center;
}

.search-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 8px 0;
}

.stock-code {
  font-weight: 600;
  color: #303133;
  min-width: 60px;
}

.stock-name {
  flex: 1;
  margin-left: 8px;
  color: #606266;
}

.stock-price, .stock-change {
  font-weight: 500;
  min-width: 60px;
  text-align: right;
}

.header-center {
  flex: 2;
  display: flex;
  justify-content: center;
}

.fund-overview {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 8px 16px;
  background: #f8f9fa;
  border-radius: 6px;
}

.fund-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.fund-label {
  font-size: 12px;
  color: #909399;
}

.fund-value {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 12px;
}

.mode-switch {
  margin-right: 12px;
}

.connection-indicator {
  display: flex;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

/* 连接提示 */
.connection-alert {
  margin: 0 20px 20px 20px;
}

/* 主要交易区域 */
.main-trading-area {
  display: flex;
  flex: 1;
  gap: 20px;
  padding: 20px;
  overflow: hidden;
}

/* 核心交易区（70%） */
.core-trading-zone {
  display: flex;
  flex: 0.7;
  gap: 20px;
  min-height: 0;
}

/* 行情图表区（35%） */
.chart-section {
  flex: 0.35;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.stock-info {
  flex: 1;
}

.stock-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 600;
}

.current-price {
  font-size: 18px;
  font-weight: bold;
}

.price-change {
  font-size: 14px;
  font-weight: 500;
}

.price-limits {
  display: flex;
  gap: 12px;
  font-size: 12px;
  color: #909399;
}

.limit-up {
  color: #f56c6c;
}

.limit-down {
  color: #67c23a;
}

.chart-container {
  flex: 1;
  padding: 16px;
  min-height: 0;
}

.chart-placeholder {
  height: 400px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.technical-indicators {
  border-top: 1px solid #e4e7ed;
  padding: 16px;
}

/* 下单面板（30%） */
.order-panel {
  flex: 0.3;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.selected-stock-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-display {
  font-weight: 600;
  color: #303133;
}

.no-stock-selected {
  color: #909399;
  text-align: center;
}

.quick-trade-buttons {
  display: flex;
  gap: 8px;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.trade-button {
  flex: 1;
  height: 40px;
  font-weight: 600;
  border-radius: 6px;
}

.buy-button {
  background: #67c23a;
  border-color: #67c23a;
}

.sell-button {
  background: #f56c6c;
  border-color: #f56c6c;
}

.cancel-button {
  background: #e6a23c;
  border-color: #e6a23c;
}

.order-form-section {
  flex: 1;
  padding: 16px;
  overflow-y: auto;
}

.buy-radio {
  color: #67c23a;
}

.sell-radio {
  color: #f56c6c;
}

.price-input-group {
  width: 100%;
}

.price-limits-info {
  display: flex;
  justify-content: space-between;
  margin-top: 4px;
  font-size: 12px;
}

.limit-up-price {
  color: #f56c6c;
}

.limit-down-price {
  color: #67c23a;
}

.quantity-input-group {
  width: 100%;
}

.quantity-shortcuts {
  display: flex;
  gap: 4px;
  margin-top: 8px;
}

.quantity-shortcuts .el-button {
  flex: 1;
  font-size: 12px;
}

.quantity-info {
  margin-top: 4px;
  font-size: 12px;
  color: #909399;
}

.estimate-info {
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.estimate-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.estimate-row:last-child {
  margin-bottom: 0;
}

/* 深度盘口（35%） */
.depth-panel {
  flex: 0.35;
  display: flex;
  flex-direction: column;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.depth-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #e4e7ed;
}

.depth-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.depth-book {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.depth-header-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 8px 16px;
  background: #fafafa;
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
}

.sell-orders {
  flex: 1;
  display: flex;
  flex-direction: column-reverse;
}

.buy-orders {
  flex: 1;
}

.depth-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  padding: 6px 16px;
  font-size: 12px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.depth-row:hover {
  background: #f5f7fa;
}

.sell-row .depth-price {
  color: #67c23a;
}

.buy-row .depth-price {
  color: #f56c6c;
}

.depth-level {
  color: #909399;
}

.depth-price {
  font-weight: 600;
  text-align: center;
}

.depth-volume {
  text-align: right;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 4px;
}

.large-order {
  font-weight: 600;
  color: #e6a23c;
}

.large-order-indicator {
  font-size: 10px;
}

.latest-price-divider {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 16px;
  background: #f8f9fa;
  border: 1px solid #e4e7ed;
  border-left: none;
  border-right: none;
  font-weight: 600;
}

.latest-price {
  font-size: 14px;
}

.tick-trades {
  border-top: 1px solid #e4e7ed;
  max-height: 200px;
  overflow-y: auto;
}

.tick-header {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  padding: 8px 16px;
  background: #fafafa;
  font-size: 12px;
  font-weight: 600;
  color: #606266;
  border-bottom: 1px solid #e4e7ed;
}

.tick-list {
  max-height: 150px;
  overflow-y: auto;
}

.tick-row {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  padding: 4px 16px;
  font-size: 11px;
  border-bottom: 1px solid #f5f7fa;
}

.tick-time {
  color: #909399;
}

.tick-price {
  font-weight: 500;
  text-align: center;
}

.tick-volume {
  text-align: center;
}

.tick-type {
  text-align: center;
  font-size: 10px;
}

.tick-type.buy {
  color: #f56c6c;
}

.tick-type.sell {
  color: #67c23a;
}

.tick-type.neutral {
  color: #909399;
}

/* 辅助功能区（30%） */
.auxiliary-zone {
  flex: 0.3;
  display: flex;
  flex-direction: column;
  gap: 20px;
  min-height: 0;
}

.position-section,
.order-queue-section,
.smart-alerts-section {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.position-section {
  flex: 0.5;
}

.order-queue-section {
  flex: 0.3;
}

.smart-alerts-section {
  flex: 0.2;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid #e4e7ed;
  background: #fafafa;
}

.section-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
}

.queue-actions {
  display: flex;
  gap: 8px;
}

.position-table,
.order-queue {
  padding: 8px;
}

.profit {
  color: #f56c6c;
}

.loss {
  color: #67c23a;
}

.alerts-container {
  padding: 12px;
  max-height: 150px;
  overflow-y: auto;
}

.alert-item,
.reminder-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  margin-bottom: 8px;
  border-radius: 4px;
  font-size: 12px;
}

.alert-item.info {
  background: #e1f3ff;
  color: #409eff;
}

.alert-item.warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.alert-item.success {
  background: #f0f9ff;
  color: #67c23a;
}

.alert-item.danger {
  background: #fef0f0;
  color: #f56c6c;
}

.reminder-item.warning {
  background: #fdf6ec;
  color: #e6a23c;
}

.reminder-item.danger {
  background: #fef0f0;
  color: #f56c6c;
}

.alert-text {
  flex: 1;
}

.alert-time {
  font-size: 10px;
  color: #909399;
}

/* 底部状态栏（5%） */
.bottom-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 40px;
  padding: 0 20px;
  background: #303133;
  color: white;
  font-size: 12px;
}

.status-left,
.status-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.status-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.connection-details {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-indicator {
  font-size: 8px;
}

.status-indicator.status-connected {
  color: #67c23a;
}

.status-indicator.status-connecting {
  color: #e6a23c;
}

.status-indicator.status-disconnected {
  color: #f56c6c;
}

.risk-notice {
  color: #909399;
  overflow: hidden;
  white-space: nowrap;
}

.scrolling-text {
  display: inline-block;
  animation: scroll-left 20s linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

.hotkey-tips {
  color: #909399;
}

/* 通用样式 */
.price-up {
  color: #f56c6c !important;
}

.price-down {
  color: #67c23a !important;
}

.price-flat {
  color: #303133 !important;
}

/* 响应式设计 */
@media (max-width: 1400px) {
  .core-trading-zone {
    flex-direction: column;
  }

  .chart-section,
  .order-panel,
  .depth-panel {
    flex: none;
    height: 400px;
  }
}

@media (max-width: 1200px) {
  .main-trading-area {
    flex-direction: column;
  }

  .auxiliary-zone {
    flex: none;
    height: 300px;
  }
}

.page-header {
  margin-bottom: 20px;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-info h1 {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0 0 8px 0;
  color: #303133;
  font-size: 24px;
}

.header-info p {
  margin: 0;
  color: #909399;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.connection-alert {
  margin-bottom: 20px;
}

.connection-alert ul {
  margin: 8px 0 0 20px;
}

.account-info {
  margin-bottom: 20px;
}

.info-card {
  height: 120px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.card-icon {
  font-size: 24px;
  color: #409eff;
}

.card-value {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.amount {
  font-size: 24px;
  font-weight: bold;
  color: #303133;
}

.change {
  font-size: 14px;
}

.change.positive { color: #67c23a; }
.change.negative { color: #f56c6c; }

.trading-content {
  gap: 20px;
}

.order-panel,
.data-panel,
.market-panel {
  height: 650px;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.order-form {
  max-height: 550px;
  overflow-y: auto;
}

.quantity-shortcuts {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

.order-summary {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
  margin-top: 16px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.summary-item:last-child {
  margin-bottom: 0;
}

.data-tabs {
  margin: -10px 0 0 0;
}

.positions-table,
.orders-table,
.trades-table {
  height: 550px;
}

.profit { color: #67c23a; }
.loss { color: #f56c6c; }

.stock-detail {
  padding: 16px 0;
}

.stock-header {
  margin-bottom: 16px;
}

.stock-header h3 {
  margin: 0 0 4px 0;
  color: #303133;
}

.stock-code {
  color: #909399;
  font-size: 14px;
}

.price-info {
  margin-bottom: 20px;
}

.current-price {
  font-size: 32px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 4px;
}

.price-change {
  font-size: 16px;
  font-weight: 500;
}

.market-data {
  background: #f5f7fa;
  padding: 12px;
  border-radius: 4px;
}

.data-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.data-row:last-child {
  margin-bottom: 0;
}

.no-selection {
  height: 300px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-confirm {
  padding: 16px 0;
}

.confirm-details {
  margin-top: 16px;
  background: #f5f7fa;
  padding: 16px;
  border-radius: 4px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.detail-row:last-child {
  margin-bottom: 0;
}

.buy-text { color: #67c23a; font-weight: 500; }
.sell-text { color: #f56c6c; font-weight: 500; }

.stock-suggestion {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stock-code {
  font-weight: 500;
  color: #303133;
}

.stock-name {
  color: #909399;
  flex: 1;
  margin-left: 8px;
}

.stock-price {
  color: #303133;
  font-weight: 500;
}
</style>
