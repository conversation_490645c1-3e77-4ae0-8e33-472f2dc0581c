<template>
  <div class="improved-dashboard">
    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ userName || '投资者' }}！</h1>
          <p>{{ currentTime }} | {{ marketStatus }}</p>
        </div>
        <div class="quick-actions">
          <el-button type="primary" @click="quickTrade">
            <el-icon><TrendCharts /></el-icon>
            快速交易
          </el-button>
          <el-button @click="viewMarket">
            <el-icon><DataAnalysis /></el-icon>
            市场行情
          </el-button>
          <el-button @click="viewPortfolio">
            <el-icon><PieChart /></el-icon>
            我的投资组合
          </el-button>
        </div>
      </div>
    </div>

    <!-- 核心指标概览 -->
    <div class="metrics-overview">
      <el-row :gutter="20">
        <el-col :span="6">
          <div class="metric-card primary">
            <div class="metric-header">
              <el-icon><Monitor /></el-icon>
              <span>总资产</span>
            </div>
            <div class="metric-value">
              <span class="value">{{ formatCurrency(totalAssets) }}</span>
              <span class="change" :class="totalChange >= 0 ? 'positive' : 'negative'">
                {{ totalChange >= 0 ? '+' : '' }}{{ formatCurrency(totalChange) }}
              </span>
            </div>
            <div class="metric-chart">
              <div class="mini-chart" ref="assetsChart"></div>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="metric-card success">
            <div class="metric-header">
              <el-icon><TrendCharts /></el-icon>
              <span>今日盈亏</span>
            </div>
            <div class="metric-value">
              <span class="value" :class="dailyPnL >= 0 ? 'positive' : 'negative'">
                {{ formatCurrency(Math.abs(dailyPnL)) }}
              </span>
              <span class="percentage" :class="dailyPnL >= 0 ? 'positive' : 'negative'">
                {{ dailyPnL >= 0 ? '+' : '' }}{{ (dailyPnL / totalAssets * 100).toFixed(2) }}%
              </span>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="metric-card info">
            <div class="metric-header">
              <el-icon><DataAnalysis /></el-icon>
              <span>持仓数量</span>
            </div>
            <div class="metric-value">
              <span class="value">{{ holdingsCount }}</span>
              <span class="unit">只股票</span>
            </div>
          </div>
        </el-col>

        <el-col :span="6">
          <div class="metric-card warning">
            <div class="metric-header">
              <el-icon><Bell /></el-icon>
              <span>待处理</span>
            </div>
            <div class="metric-value">
              <span class="value">{{ pendingOrders }}</span>
              <span class="unit">笔订单</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="20">
        <!-- 左侧：市场热点 -->
        <el-col :span="8">
          <div class="content-card">
            <div class="card-header">
              <h3>市场热点</h3>
              <el-button text @click="refreshMarket">刷新</el-button>
            </div>
            <div class="market-hotspots">
              <div v-for="stock in hotStocks" :key="stock.symbol" class="stock-item">
                <div class="stock-info">
                  <span class="symbol">{{ stock.symbol }}</span>
                  <span class="name">{{ stock.name }}</span>
                </div>
                <div class="stock-price">
                  <span class="price">{{ stock.price }}</span>
                  <span class="change" :class="stock.change >= 0 ? 'positive' : 'negative'">
                    {{ stock.change >= 0 ? '+' : '' }}{{ stock.change_percent.toFixed(2) }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 中间：投资组合 -->
        <el-col :span="8">
          <div class="content-card">
            <div class="card-header">
              <h3>投资组合</h3>
              <el-button text @click="viewFullPortfolio">查看全部</el-button>
            </div>
            <div class="portfolio-summary">
              <div class="portfolio-chart" ref="portfolioChart"></div>
              <div class="portfolio-list">
                <div v-for="holding in topHoldings" :key="holding.symbol" class="holding-item">
                  <span class="symbol">{{ holding.symbol }}</span>
                  <span class="value">{{ formatCurrency(holding.value) }}</span>
                  <span class="weight">{{ holding.weight }}%</span>
                </div>
              </div>
            </div>
          </div>
        </el-col>

        <!-- 右侧：最新动态 -->
        <el-col :span="8">
          <div class="content-card">
            <div class="card-header">
              <h3>最新动态</h3>
              <el-button text @click="viewAllNews">查看全部</el-button>
            </div>
            <div class="news-list">
              <div v-for="news in latestNews" :key="news.id" class="news-item">
                <div class="news-time">{{ formatTime(news.time) }}</div>
                <div class="news-content">{{ news.content }}</div>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
    </div>

    <!-- 快速操作面板 -->
    <div class="quick-panel">
      <div class="panel-header">
        <h3>快速操作</h3>
      </div>
      <div class="quick-buttons">
        <el-button-group>
          <el-button @click="quickBuy">快速买入</el-button>
          <el-button @click="quickSell">快速卖出</el-button>
          <el-button @click="viewOrders">查看订单</el-button>
          <el-button @click="viewAnalysis">分析报告</el-button>
        </el-button-group>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { TrendCharts, DataAnalysis, PieChart, Monitor, Bell } from '@element-plus/icons-vue'

const router = useRouter()

// 响应式数据
const userName = ref('投资者')
const totalAssets = ref(1250000)
const totalChange = ref(15600)
const dailyPnL = ref(8500)
const holdingsCount = ref(12)
const pendingOrders = ref(3)

const hotStocks = ref([
  { symbol: '000001', name: '平安银行', price: 12.50, change: 0.15, change_percent: 1.22 },
  { symbol: '600036', name: '招商银行', price: 35.20, change: 0.45, change_percent: 1.30 },
  { symbol: '000858', name: '五粮液', price: 168.50, change: -2.30, change_percent: -1.35 },
])

const topHoldings = ref([
  { symbol: '000001', value: 125000, weight: 10 },
  { symbol: '600036', value: 187500, weight: 15 },
  { symbol: '000858', value: 93750, weight: 7.5 },
])

const latestNews = ref([
  { id: 1, time: new Date(), content: '您的订单 ORD001234 已成功执行' },
  { id: 2, time: new Date(Date.now() - 300000), content: '市场提醒：银行板块异动' },
  { id: 3, time: new Date(Date.now() - 600000), content: '投资组合收益率达到新高' },
])

// 计算属性
const currentTime = computed(() => {
  return new Date().toLocaleString('zh-CN')
})

const marketStatus = computed(() => {
  const hour = new Date().getHours()
  if (hour >= 9 && hour < 15) {
    return '市场开盘中'
  } else {
    return '市场已收盘'
  }
})

// 方法
const formatCurrency = (value: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(value)
}

const formatTime = (time: Date) => {
  return time.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit' 
  })
}

// 导航方法
const quickTrade = () => router.push('/trading')
const viewMarket = () => router.push('/market')
const viewPortfolio = () => router.push('/portfolio')
const viewFullPortfolio = () => router.push('/portfolio')
const viewAllNews = () => router.push('/notifications')
const viewOrders = () => router.push('/trading/orders')
const viewAnalysis = () => router.push('/analysis')

// 操作方法
const refreshMarket = () => {
  ElMessage.success('市场数据已刷新')
}

const quickBuy = () => {
  ElMessage.info('跳转到买入页面')
  router.push('/trading?action=buy')
}

const quickSell = () => {
  ElMessage.info('跳转到卖出页面')
  router.push('/trading?action=sell')
}

onMounted(() => {
  // 初始化图表等
})
</script>

<style scoped>
.improved-dashboard {
  padding: 20px;
  background: #f5f7fa;
  min-height: 100vh;
}

.welcome-banner {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 30px;
  margin-bottom: 20px;
  color: white;
}

.banner-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
}

.quick-actions {
  display: flex;
  gap: 12px;
}

.metrics-overview {
  margin-bottom: 20px;
}

.metric-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s;
}

.metric-card:hover {
  transform: translateY(-2px);
}

.metric-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #666;
  font-size: 14px;
}

.metric-value {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.value {
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.change, .percentage {
  font-size: 14px;
}

.positive {
  color: #67c23a;
}

.negative {
  color: #f56c6c;
}

.content-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #eee;
}

.card-header h3 {
  margin: 0;
  color: #333;
}

.stock-item, .holding-item, .news-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.quick-panel {
  background: white;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.panel-header h3 {
  margin: 0 0 16px 0;
  color: #333;
}
</style>
