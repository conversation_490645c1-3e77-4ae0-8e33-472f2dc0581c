<template>
  <div class="trading-center">
    <!-- 顶部导航栏 -->
    <div class="trading-nav">
      <div class="nav-left">
        <h1 class="page-title">交易中心</h1>
        <div class="account-info">
          <el-tag :type="currentAccount.type === 'real' ? 'danger' : 'success'">
            {{ currentAccount.type === 'real' ? '实盘账户' : '模拟账户' }}
          </el-tag>
          <span class="account-name">{{ currentAccount.name }}</span>
          <span class="available-funds">可用资金: ¥{{ formatMoney(currentAccount.availableFunds) }}</span>
        </div>
      </div>

      <div class="nav-right">
        <el-button-group>
          <el-button
            :type="activeModule === 'terminal' ? 'primary' : 'default'"
            @click="switchModule('terminal')"
          >
            <el-icon><Monitor /></el-icon>
            交易终端
          </el-button>
          <el-button
            :type="activeModule === 'highfreq' ? 'primary' : 'default'"
            @click="switchModule('highfreq')"
          >
            <el-icon><Lightning /></el-icon>
            高频交易
          </el-button>
          <el-button
            :type="activeModule === 'lowfreq' ? 'primary' : 'default'"
            @click="switchModule('lowfreq')"
          >
            <el-icon><TrendCharts /></el-icon>
            低频交易
          </el-button>
          <el-button
            :type="activeModule === 'account' ? 'primary' : 'default'"
            @click="switchModule('account')"
          >
            <el-icon><User /></el-icon>
            账户管理
          </el-button>
          <el-button
            :type="activeModule === 'data' ? 'primary' : 'default'"
            @click="switchModule('data')"
          >
            <el-icon><DataBoard /></el-icon>
            数据中心
          </el-button>
        </el-button-group>

        <!-- 帮助按钮 -->
        <el-button @click="startTour" type="info" plain>
          <el-icon><QuestionFilled /></el-icon>
          使用帮助
        </el-button>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="trading-content">
      <!-- 加载状态 -->
      <div v-if="loading" class="loading-container" v-loading="true" element-loading-text="加载中...">
        <div style="height: 400px;"></div>
      </div>

      <!-- 交易终端模块 -->
      <div v-show="activeModule === 'terminal' && !loading" class="module-container">
        <TradingTerminalModule
          :account="currentAccount"
          @order-placed="handleOrderPlaced"
        />
      </div>

      <!-- 高频交易模块 -->
      <div v-show="activeModule === 'highfreq' && !loading" class="module-container">
        <HighFreqTradingModule
          :account="currentAccount"
          @strategy-start="handleStrategyStart"
          @strategy-stop="handleStrategyStop"
          @order-placed="handleOrderPlaced"
        />
      </div>

      <!-- 低频交易模块 -->
      <div v-show="activeModule === 'lowfreq' && !loading" class="module-container">
        <LowFreqTradingModule
          :account="currentAccount"
          @strategy-update="handleStrategyUpdate"
          @order-placed="handleOrderPlaced"
        />
      </div>

      <!-- 账户管理模块 -->
      <div v-show="activeModule === 'account' && !loading" class="module-container">
        <AccountManagementModule
          :accounts="accounts"
          :current-account="currentAccount"
          @account-changed="handleAccountChanged"
          @funds-transfer="handleFundsTransfer"
        />
      </div>

      <!-- 数据中心模块 -->
      <div v-show="activeModule === 'data' && !loading" class="module-container">
        <DataCenterModule
          :active-tab="dataActiveTab"
          :account="currentAccount"
          @tab-change="handleDataTabChange"
        />
      </div>

      <!-- 新用户引导提示 -->
      <div v-if="showTour" class="tour-overlay">
        <div class="tour-content">
          <h3>欢迎使用交易中心！</h3>
          <p>点击上方按钮切换不同功能模块</p>
          <el-button @click="handleTourFinish" type="primary">我知道了</el-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Monitor, User, DataBoard, QuestionFilled, Lightning, TrendCharts,
  Cpu, Money, Plus, MoreFilled, Refresh, Download
} from '@element-plus/icons-vue'
import TradingTerminalModule from './modules/TradingTerminalModule.vue'
import HighFreqTradingModule from './modules/HighFreqTradingModule.vue'
import LowFreqTradingModule from './modules/LowFreqTradingModule.vue'
import AccountManagementModule from './modules/AccountManagementModule.vue'
import DataCenterModule from './modules/DataCenterModule.vue'

// 响应式数据
const activeModule = ref<'terminal' | 'highfreq' | 'lowfreq' | 'account' | 'data'>('terminal')
const dataActiveTab = ref<'orders' | 'positions' | 'trades'>('orders')
const loading = ref(false)
const showTour = ref(false)

// 账户数据
const accounts = reactive([
  {
    id: 'sim_001',
    type: 'simulation',
    name: '模拟账户001',
    availableFunds: 1000000,
    totalAssets: 1000000,
    status: 'active'
  },
  {
    id: 'real_001',
    type: 'real',
    name: '实盘账户001',
    availableFunds: 50000,
    totalAssets: 55000,
    status: 'active'
  }
])

const currentAccount = ref(accounts[0]) // 默认使用模拟账户

// 计算属性
const formatMoney = computed(() => {
  return (amount: number) => {
    return new Intl.NumberFormat('zh-CN', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(amount)
  }
})

// 用户引导状态
const tourMessage = ref('欢迎使用交易中心！点击上方按钮切换不同功能模块。')

// 方法
const switchModule = async (module: 'terminal' | 'highfreq' | 'lowfreq' | 'account' | 'data') => {
  loading.value = true

  // 模拟加载延迟
  await new Promise(resolve => setTimeout(resolve, 300))

  activeModule.value = module
  loading.value = false

  // 记录用户行为
  console.log(`用户切换到${module}模块`)
}

const handleOrderPlaced = (orderData: any) => {
  ElMessage.success('订单提交成功')

  // 如果当前在数据中心，自动切换到订单管理
  if (activeModule.value === 'data') {
    dataActiveTab.value = 'orders'
  }
}

const handleAccountSwitch = (accountType: 'simulation' | 'real') => {
  const targetAccount = accounts.find(acc => acc.type === accountType)
  if (targetAccount) {
    currentAccount.value = targetAccount
    ElMessage.info(`已切换到${accountType === 'simulation' ? '模拟' : '实盘'}账户`)
  }
}

const handleAccountChanged = (account: any) => {
  currentAccount.value = account
  ElMessage.success('账户切换成功')
}

const handleFundsTransfer = (transferData: any) => {
  ElMessage.success('资金划转成功')

  // 更新账户资金
  if (transferData.type === 'bank-to-stock') {
    currentAccount.value.availableFunds += transferData.amount
  } else {
    currentAccount.value.availableFunds -= transferData.amount
  }
}

const handleDataTabChange = (tab: 'orders' | 'positions' | 'trades') => {
  dataActiveTab.value = tab
}

const handleStrategyStart = (strategyData: any) => {
  ElMessage.success(`高频策略 ${strategyData.name} 启动成功`)
  console.log('高频策略启动:', strategyData)
}

const handleStrategyStop = (strategyData: any) => {
  ElMessage.info(`高频策略 ${strategyData.name} 已停止`)
  console.log('高频策略停止:', strategyData)
}

const handleStrategyUpdate = (strategyData: any) => {
  ElMessage.info(`低频策略 ${strategyData.name} 参数已更新`)
  console.log('低频策略更新:', strategyData)
}

const handleTourFinish = () => {
  localStorage.setItem('trading-center-tour-completed', 'true')
  ElMessage.success('欢迎使用交易中心！')
}

// 快捷键支持
const handleKeyboardShortcuts = (event: KeyboardEvent) => {
  if (event.ctrlKey || event.metaKey) {
    switch (event.key) {
      case '1':
        event.preventDefault()
        switchModule('terminal')
        break
      case '2':
        event.preventDefault()
        switchModule('account')
        break
      case '3':
        event.preventDefault()
        switchModule('data')
        break
    }
  }
}

// 自动保存用户偏好
const saveUserPreferences = () => {
  const preferences = {
    activeModule: activeModule.value,
    currentAccountId: currentAccount.value.id,
    dataActiveTab: dataActiveTab.value
  }
  localStorage.setItem('trading-center-preferences', JSON.stringify(preferences))
}

// 加载用户偏好
const loadUserPreferences = () => {
  try {
    const saved = localStorage.getItem('trading-center-preferences')
    if (saved) {
      const preferences = JSON.parse(saved)
      activeModule.value = preferences.activeModule || 'terminal'
      dataActiveTab.value = preferences.dataActiveTab || 'orders'

      // 恢复账户选择
      if (preferences.currentAccountId) {
        const account = accounts.find(acc => acc.id === preferences.currentAccountId)
        if (account) {
          currentAccount.value = account
        }
      }
    }
  } catch (error) {
    console.warn('加载用户偏好失败:', error)
  }
}

// 实时更新账户数据
const updateAccountData = async () => {
  try {
    // 模拟API调用获取最新账户数据
    const response = await fetch(`/api/v1/accounts/${currentAccount.value.id}`)
    if (response.ok) {
      const accountData = await response.json()
      Object.assign(currentAccount.value, accountData)
    }
  } catch (error) {
    console.warn('更新账户数据失败:', error)
  }
}

// 页面可见性变化处理
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // 页面重新可见时更新数据
    updateAccountData()
  }
}

// 生命周期钩子
onMounted(() => {
  // 加载用户偏好
  loadUserPreferences()

  // 检查是否需要显示用户引导
  const tourCompleted = localStorage.getItem('trading-center-tour-completed')
  if (!tourCompleted) {
    showTour.value = true
  }

  // 添加键盘事件监听
  document.addEventListener('keydown', handleKeyboardShortcuts)

  // 添加页面可见性监听
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 定期更新账户数据
  const updateInterval = setInterval(updateAccountData, 30000) // 每30秒更新一次

  // 保存定时器引用以便清理
  onUnmounted(() => {
    clearInterval(updateInterval)
    document.removeEventListener('keydown', handleKeyboardShortcuts)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  })
})

// 监听数据变化，自动保存偏好
watch([activeModule, currentAccount, dataActiveTab], () => {
  saveUserPreferences()
}, { deep: true })

const startTour = () => {
  showTour.value = true
}



// 生命周期
onMounted(() => {
  console.log('交易中心初始化完成')

  // 检查是否需要显示用户引导
  const tourCompleted = localStorage.getItem('trading-center-tour-completed')
  if (!tourCompleted) {
    setTimeout(() => {
      showTour.value = true
    }, 1000) // 延迟1秒显示引导
  }
})
</script>

<style scoped lang="scss">
.trading-center {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.trading-nav {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

  .nav-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .page-title {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
      color: #303133;
    }

    .account-info {
      display: flex;
      align-items: center;
      gap: 12px;

      .account-name {
        font-weight: 500;
        color: #606266;
      }

      .available-funds {
        font-weight: 600;
        color: #67c23a;
        font-size: 16px;
      }
    }
  }

  .nav-right {
    .el-button-group {
      .el-button {
        padding: 12px 20px;

        .el-icon {
          margin-right: 6px;
        }
      }
    }
  }
}

.trading-content {
  flex: 1;
  overflow: hidden;

  .module-container {
    height: 100%;
    padding: 16px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .trading-nav {
    flex-direction: column;
    gap: 16px;
    padding: 12px 16px;

    .nav-left {
      flex-direction: column;
      align-items: flex-start;
      gap: 12px;

      .page-title {
        font-size: 20px;
      }

      .account-info {
        flex-wrap: wrap;
        gap: 8px;

        .available-funds {
          font-size: 14px;
        }
      }
    }

    .nav-right {
      width: 100%;

      .el-button-group {
        width: 100%;
        display: flex;

        .el-button {
          flex: 1;
          padding: 10px 12px;
          font-size: 14px;
        }
      }
    }
  }

  .trading-content {
    .module-container {
      padding: 12px;
    }
  }
}

// 用户引导样式
.tour-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;

  .tour-content {
    background: white;
    padding: 30px;
    border-radius: 12px;
    text-align: center;
    max-width: 400px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);

    h3 {
      margin: 0 0 16px 0;
      color: #303133;
    }

    p {
      margin: 0 0 24px 0;
      color: #606266;
      line-height: 1.6;
    }
  }
}

.simple-module {
  padding: 40px;
  text-align: center;

  h2 {
    margin: 0 0 16px 0;
    color: #303133;
  }

  p {
    margin: 0;
    color: #606266;
    font-size: 16px;
  }
}
</style>
