import { ref, onMounted, onUnmounted, reactive } from 'vue'
import type { TickData, DepthData, OrderData, TradeData, PositionData, StrategyData } from '@/types/api'

// 延迟导入WebSocket服务，避免循环依赖和初始化错误
let websocketService: any = null
let MessageType: any = null
let Channel: any = null

async function initWebSocketService() {
  if (!websocketService) {
    try {
      const module = await import('@/services/websocket.service')
      websocketService = module.websocketService
      MessageType = module.MessageType
      Channel = module.Channel
    } catch (error) {
      console.warn('WebSocket服务初始化失败:', error)
    }
  }
  return { websocketService, MessageType, Channel }
}

// 全局订阅状态管理
const globalSubscriptions = reactive({
  marketTicks: new Map<string, Set<Function>>(),
  marketDepth: new Map<string, Set<Function>>(),
  marketKline: new Map<string, Set<Function>>(),
  tradingOrders: new Set<Function>(),
  tradingTrades: new Set<Function>(),
  tradingPositions: new Set<Function>(),
  strategyStatus: new Map<string, Set<Function>>()
})

export function useWebSocket(options?: { url?: string; autoConnect?: boolean }) {
  const isConnected = ref(false)

  const onConnect = () => {
    isConnected.value = true
  }

  const onDisconnect = () => {
    isConnected.value = false
  }

  onMounted(async () => {
    try {
      // 异步初始化WebSocket服务
      const { websocketService: service } = await initWebSocketService()

      if (service) {
        // 更新连接状态
        isConnected.value = service.isConnectionActive()

        if (options?.autoConnect !== false) {
          service.connect().catch(error => {
            console.warn('WebSocket自动连接失败:', error)
          })
        }

        // 安全地添加事件监听器
        if (typeof service.on === 'function') {
          service.on('connect', onConnect)
          service.on('disconnect', onDisconnect)
        }
      } else {
        console.warn('WebSocket服务初始化失败')
      }
    } catch (error) {
      console.error('WebSocket初始化失败:', error)
    }
  })

  onUnmounted(() => {
    try {
      if (websocketService && typeof websocketService.off === 'function') {
        websocketService.off('connect', onConnect)
        websocketService.off('disconnect', onDisconnect)
      }
    } catch (error) {
      console.error('WebSocket清理失败:', error)
    }
  })

  // 提供兼容的事件监听接口
  const on = (event: string, callback: Function) => {
    try {
      if (websocketService && typeof websocketService.on === 'function') {
        websocketService.on(event, callback)
      } else {
        console.warn(`WebSocket service not available or 'on' method not found for event: ${event}`)
      }
    } catch (error) {
      console.error(`Error adding WebSocket event listener for ${event}:`, error)
    }
  }

  const off = (event: string, callback: Function) => {
    try {
      if (websocketService && typeof websocketService.off === 'function') {
        websocketService.off(event, callback)
      } else {
        console.warn(`WebSocket service not available or 'off' method not found for event: ${event}`)
      }
    } catch (error) {
      console.error(`Error removing WebSocket event listener for ${event}:`, error)
    }
  }

  const send = (data: any) => {
    try {
      if (websocketService && typeof websocketService.send === 'function') {
        websocketService.send(data)
      } else {
        console.warn('WebSocket service not available or send method not found')
      }
    } catch (error) {
      console.error('Error sending WebSocket message:', error)
    }
  }

  const connect = () => {
    if (websocketService && typeof websocketService.connect === 'function') {
      websocketService.connect()
    }
  }

  const disconnect = () => {
    if (websocketService && typeof websocketService.disconnect === 'function') {
      websocketService.disconnect()
    }
  }

  // 统一的订阅管理 - 避免重复订阅
  const subscribeMarketTick = (symbols: string[], callback: (data: TickData) => void) => {
    const symbolsArray = Array.isArray(symbols) ? symbols : [symbols]

    symbolsArray.forEach(symbol => {
      if (!globalSubscriptions.marketTicks.has(symbol)) {
        globalSubscriptions.marketTicks.set(symbol, new Set())
        // 只有第一次订阅时才向服务器发送订阅请求
        websocketService.subscribeMarketTick([symbol], callback)
      }

      globalSubscriptions.marketTicks.get(symbol)?.add(callback)
    })

    // 返回取消订阅函数
    return () => {
      symbolsArray.forEach(symbol => {
        const callbacks = globalSubscriptions.marketTicks.get(symbol)
        if (callbacks) {
          callbacks.delete(callback)
          if (callbacks.size === 0) {
            globalSubscriptions.marketTicks.delete(symbol)
            // 最后一个取消订阅时，向服务器发送取消订阅请求
            websocketService.unsubscribe(Channel.MARKET, MessageType.TICK)
          }
        }
      })
    }
  }

  const subscribeMarketDepth = (symbol: string, callback: (data: DepthData) => void) => {
    if (!globalSubscriptions.marketDepth.has(symbol)) {
      globalSubscriptions.marketDepth.set(symbol, new Set())
      websocketService.subscribeMarketDepth(symbol, callback)
    }

    globalSubscriptions.marketDepth.get(symbol)?.add(callback)

    return () => {
      const callbacks = globalSubscriptions.marketDepth.get(symbol)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          globalSubscriptions.marketDepth.delete(symbol)
          websocketService.unsubscribe(Channel.MARKET, MessageType.DEPTH)
        }
      }
    }
  }

  const subscribeMarketKline = (symbol: string, interval: string, callback: (data: any) => void) => {
    const key = `${symbol}:${interval}`

    if (!globalSubscriptions.marketKline.has(key)) {
      globalSubscriptions.marketKline.set(key, new Set())
      websocketService.subscribeMarketKline(symbol, interval, callback)
    }

    globalSubscriptions.marketKline.get(key)?.add(callback)

    return () => {
      const callbacks = globalSubscriptions.marketKline.get(key)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          globalSubscriptions.marketKline.delete(key)
          websocketService.unsubscribe(Channel.MARKET, MessageType.KLINE)
        }
      }
    }
  }

  const subscribeTradingOrders = (callback: (data: OrderData) => void) => {
    if (globalSubscriptions.tradingOrders.size === 0) {
      websocketService.subscribeTradingOrders(callback)
    }

    globalSubscriptions.tradingOrders.add(callback)

    return () => {
      globalSubscriptions.tradingOrders.delete(callback)
      if (globalSubscriptions.tradingOrders.size === 0) {
        websocketService.unsubscribe(Channel.TRADING, MessageType.ORDER)
      }
    }
  }

  const subscribeTradingTrades = (callback: (data: TradeData) => void) => {
    if (globalSubscriptions.tradingTrades.size === 0) {
      websocketService.subscribeTradingTrades(callback)
    }

    globalSubscriptions.tradingTrades.add(callback)

    return () => {
      globalSubscriptions.tradingTrades.delete(callback)
      if (globalSubscriptions.tradingTrades.size === 0) {
        websocketService.unsubscribe(Channel.TRADING, MessageType.TRADE)
      }
    }
  }

  const subscribeTradingPositions = (callback: (data: PositionData) => void) => {
    if (globalSubscriptions.tradingPositions.size === 0) {
      websocketService.subscribeTradingPositions(callback)
    }

    globalSubscriptions.tradingPositions.add(callback)

    return () => {
      globalSubscriptions.tradingPositions.delete(callback)
      if (globalSubscriptions.tradingPositions.size === 0) {
        websocketService.unsubscribe(Channel.TRADING, MessageType.POSITION)
      }
    }
  }

  const subscribeStrategyStatus = (strategyId: string, callback: (data: StrategyData) => void) => {
    if (!globalSubscriptions.strategyStatus.has(strategyId)) {
      globalSubscriptions.strategyStatus.set(strategyId, new Set())
      websocketService.subscribeStrategyStatus(strategyId, callback)
    }

    globalSubscriptions.strategyStatus.get(strategyId)?.add(callback)

    return () => {
      const callbacks = globalSubscriptions.strategyStatus.get(strategyId)
      if (callbacks) {
        callbacks.delete(callback)
        if (callbacks.size === 0) {
          globalSubscriptions.strategyStatus.delete(strategyId)
          websocketService.unsubscribe(Channel.STRATEGY, MessageType.STRATEGY_STATUS)
        }
      }
    }
  }

  // 批量订阅市场数据
  const subscribeMarketData = (symbols: string[], callback: (data: TickData) => void) => {
    const unsubscribers: Function[] = []

    symbols.forEach(symbol => {
      const unsubscribe = subscribeMarketTick([symbol], callback)
      unsubscribers.push(unsubscribe)
    })

    return () => {
      unsubscribers.forEach(unsubscribe => unsubscribe())
    }
  }

  // 获取订阅统计信息
  const getSubscriptionStats = () => {
    return {
      marketTicks: globalSubscriptions.marketTicks.size,
      marketDepth: globalSubscriptions.marketDepth.size,
      marketKline: globalSubscriptions.marketKline.size,
      tradingOrders: globalSubscriptions.tradingOrders.size,
      tradingTrades: globalSubscriptions.tradingTrades.size,
      tradingPositions: globalSubscriptions.tradingPositions.size,
      strategyStatus: globalSubscriptions.strategyStatus.size
    }
  }

  // 清理所有订阅
  const clearAllSubscriptions = () => {
    globalSubscriptions.marketTicks.clear()
    globalSubscriptions.marketDepth.clear()
    globalSubscriptions.marketKline.clear()
    globalSubscriptions.tradingOrders.clear()
    globalSubscriptions.tradingTrades.clear()
    globalSubscriptions.tradingPositions.clear()
    globalSubscriptions.strategyStatus.clear()
  }

  return {
    isConnected,
    websocketService,
    // 基础WebSocket接口
    connect,
    disconnect,
    send,
    on,
    off,
    // 优化后的订阅函数
    subscribeMarketTick,
    subscribeMarketDepth,
    subscribeMarketKline,
    subscribeTradingOrders,
    subscribeTradingTrades,
    subscribeTradingPositions,
    subscribeStrategyStatus,
    subscribeMarketData,
    // 管理函数
    getSubscriptionStats,
    clearAllSubscriptions,
    // 兼容性别名
    subscribe: subscribeMarketTick,
    unsubscribe: clearAllSubscriptions
  }
}
