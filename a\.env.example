# 🚀 量化投资平台前端环境配置模板
# 复制此文件为 .env.development 或 .env.production 并根据实际情况修改
# 详细配置说明请参考: docs/frontend-setup.md

# ========== 应用基础配置 ==========
VITE_APP_TITLE=量化投资平台
VITE_APP_DESCRIPTION=专业的量化投资可视化平台
VITE_APP_VERSION=1.0.0
VITE_APP_ENVIRONMENT=development

# ========== API 配置 ==========
# 后端API地址 - 请确保与后端服务地址匹配
VITE_API_BASE_URL=http://localhost:8000/api/v1
VITE_API_TIMEOUT=10000
VITE_API_RETRY_ATTEMPTS=3

# ========== WebSocket 配置 ==========
# WebSocket连接地址
VITE_WS_URL=ws://localhost:8000/api/v1/ws
VITE_WS_MARKET_URL=ws://localhost:8000/api/v1/ws/market
VITE_WS_TRADING_URL=ws://localhost:8000/api/v1/ws/trading
VITE_WS_STRATEGY_URL=ws://localhost:8000/api/v1/ws/strategy

# WebSocket连接选项
VITE_WS_RECONNECT_ATTEMPTS=5
VITE_WS_RECONNECT_INTERVAL=3000
VITE_WS_HEARTBEAT_INTERVAL=30000
VITE_WS_MAX_CONNECTIONS=10

# ========== 图表配置 ==========
# ECharts图表配置
VITE_CHART_THEME=dark
VITE_CHART_ANIMATION=true
VITE_CHART_RENDERER=canvas
VITE_CHART_DATA_CACHE_TTL=60000
VITE_CHART_MAX_DATA_POINTS=10000

# ========== 交易配置 ==========
# 交易功能开关
VITE_ENABLE_PAPER_TRADING=true
VITE_ENABLE_REAL_TRADING=false

# 交易参数
VITE_DEFAULT_LEVERAGE=1
VITE_MAX_POSITION_SIZE=1000000
VITE_TRADING_FEE_RATE=0.0003
VITE_MIN_ORDER_AMOUNT=100

# ========== 认证配置 ==========
# 本地存储键名
VITE_AUTH_TOKEN_KEY=quant_auth_token
VITE_AUTH_REFRESH_TOKEN_KEY=quant_refresh_token
VITE_AUTH_USER_KEY=quant_user_info

# Token过期时间 (毫秒)
VITE_AUTH_TOKEN_EXPIRES=7200000
VITE_AUTH_REFRESH_THRESHOLD=300000

# ========== 功能开关 ==========
# 开发功能
VITE_ENABLE_MOCK=false
VITE_ENABLE_DEVTOOLS=true
VITE_ENABLE_CONSOLE_LOG=true

# 应用功能
VITE_ENABLE_PWA=true
VITE_ENABLE_I18N=true
VITE_ENABLE_THEME_SWITCH=true

# 高级功能
VITE_ENABLE_ADVANCED_CHARTS=true
VITE_ENABLE_STRATEGY_EDITOR=true
VITE_ENABLE_BACKTEST=true

# ========== 性能配置 ==========
# 数据刷新间隔 (毫秒)
VITE_DATA_UPDATE_INTERVAL=1000
VITE_MARKET_DATA_INTERVAL=500
VITE_POSITION_UPDATE_INTERVAL=2000

# 缓存配置
VITE_CACHE_ENABLED=true
VITE_CACHE_TTL=300000
VITE_MAX_CACHE_SIZE=50

# ========== 监控配置 (可选) ==========
# 错误监控
# VITE_SENTRY_DSN=your_sentry_dsn_here
VITE_SENTRY_ENVIRONMENT=development

# 用户行为分析 (可选)
# VITE_GA_TRACKING_ID=your_ga_id_here
# VITE_BAIDU_ANALYTICS_ID=your_baidu_id_here

# ========== 第三方服务 (可选) ==========
# CDN配置
# VITE_CDN_URL=https://your-cdn.com
# VITE_UPLOAD_URL=https://your-upload-service.com

# 外部数据源
# VITE_TRADING_VIEW_SYMBOL_URL=https://symbol-search.tradingview.com
# VITE_NEWS_API_URL=https://newsapi.org/v2
# VITE_MARKET_DATA_URL=https://api.marketdata.com

# ========== 开发环境特定配置 ==========
# 开发者选项
VITE_SHOW_DEV_TOOLS=true
VITE_ENABLE_HOT_RELOAD=true
VITE_SOURCE_MAP=true

# 调试选项
VITE_DEBUG_API=false
VITE_DEBUG_WEBSOCKET=false
VITE_DEBUG_CHART=false