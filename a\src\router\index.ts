import { createRouter, createWebHistory } from 'vue-router'
import { safeRoutes } from './safe-routes'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: safeRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 全局导航守卫 - 添加错误处理
router.beforeEach(async (to, from) => {
  try {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - 量化投资平台`
    } else {
      document.title = '量化投资平台'
    }
    
    // 记录导航日志（仅在开发环境）
    if (import.meta.env.DEV) {
      console.log(`🧭 导航: ${from.path} -> ${to.path}`)
    }
    
    return true
  } catch (error) {
    console.error('导航守卫错误:', error)
    // 在导航失败时重定向到安全页面
    if (to.path !== '/safe-test') {
      return '/safe-test'
    }
    return true
  }
})

// 导航错误处理
router.onError((error) => {
  console.error('路由错误:', error)
  
  // 在生产环境中，可以上报错误
  if (import.meta.env.PROD) {
    // TODO: 上报错误到监控系统
  }
})

export default router