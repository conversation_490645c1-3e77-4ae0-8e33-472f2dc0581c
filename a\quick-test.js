/**
 * 快速测试修复效果
 */

import puppeteer from 'puppeteer';

class QuickTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.errors = [];
  }

  async init() {
    console.log('🚀 启动快速测试...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: { width: 1920, height: 1080 },
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });

    this.page = await this.browser.newPage();
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const type = msg.type();
      const text = msg.text();
      console.log(`[${type.toUpperCase()}] ${text}`);
      
      if (type === 'error') {
        this.errors.push(text);
      }
    });

    // 监听页面错误
    this.page.on('pageerror', error => {
      console.error('❌ 页面错误:', error.message);
      this.errors.push(error.message);
    });
  }

  async testBasicFunctionality() {
    console.log('\n🔍 测试基础功能...');
    
    try {
      // 1. 导航到首页
      console.log('1. 加载首页...');
      const response = await this.page.goto('http://localhost:5173', { 
        waitUntil: 'networkidle0',
        timeout: 30000 
      });
      
      console.log(`   HTTP状态: ${response.status()}`);
      
      // 2. 等待应用加载
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // 3. 检查页面标题
      const title = await this.page.title();
      console.log(`   页面标题: ${title}`);
      
      // 4. 检查是否有导航栏
      const hasNavbar = await this.page.evaluate(() => {
        return document.querySelector('.top-navbar, .navbar-nav, nav') !== null;
      });
      console.log(`   导航栏存在: ${hasNavbar}`);
      
      // 5. 检查导航链接
      const navLinks = await this.page.evaluate(() => {
        const links = Array.from(document.querySelectorAll('.navbar-nav a, nav a'));
        return links.map(link => ({
          text: link.textContent.trim(),
          href: link.getAttribute('href')
        })).filter(link => link.text);
      });
      
      console.log(`   找到 ${navLinks.length} 个导航链接:`);
      navLinks.forEach(link => {
        console.log(`     - ${link.text} -> ${link.href}`);
      });
      
      // 6. 测试导航功能
      if (navLinks.length > 0) {
        console.log('\n2. 测试导航功能...');
        
        for (let i = 0; i < Math.min(navLinks.length, 3); i++) {
          const link = navLinks[i];
          console.log(`   测试导航: ${link.text}`);
          
          try {
            // 点击导航链接
            await this.page.click(`a[href="${link.href}"]`);
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            const currentUrl = this.page.url();
            console.log(`     导航成功: ${currentUrl}`);
            
            // 检查页面内容
            const hasContent = await this.page.evaluate(() => {
              return document.body.textContent.trim().length > 100;
            });
            console.log(`     页面有内容: ${hasContent}`);
            
          } catch (error) {
            console.log(`     导航失败: ${error.message}`);
          }
        }
      }
      
      // 7. 检查是否有严重错误
      const criticalErrors = this.errors.filter(error => 
        error.includes('Cannot read properties of undefined') ||
        error.includes('setupPerformanceMonitor') ||
        error.includes('TypeError')
      );
      
      console.log(`\n3. 错误检查:`);
      console.log(`   总错误数: ${this.errors.length}`);
      console.log(`   严重错误数: ${criticalErrors.length}`);
      
      if (criticalErrors.length > 0) {
        console.log('   严重错误列表:');
        criticalErrors.forEach(error => {
          console.log(`     - ${error}`);
        });
      }
      
      // 8. 截图
      await this.page.screenshot({ 
        path: `quick-test-result-${Date.now()}.png`, 
        fullPage: true 
      });
      console.log('   📸 已保存截图');
      
      return {
        success: true,
        title,
        hasNavbar,
        navLinksCount: navLinks.length,
        totalErrors: this.errors.length,
        criticalErrors: criticalErrors.length
      };
      
    } catch (error) {
      console.error('❌ 测试失败:', error.message);
      return {
        success: false,
        error: error.message
      };
    }
  }

  async generateReport(result) {
    console.log('\n📊 测试报告:');
    console.log('='.repeat(50));
    
    if (result.success) {
      console.log('✅ 基础功能测试: 通过');
      console.log(`📄 页面标题: ${result.title}`);
      console.log(`🧭 导航栏: ${result.hasNavbar ? '存在' : '缺失'}`);
      console.log(`🔗 导航链接: ${result.navLinksCount} 个`);
      console.log(`🐛 总错误数: ${result.totalErrors}`);
      console.log(`⚠️ 严重错误: ${result.criticalErrors}`);
      
      if (result.criticalErrors === 0) {
        console.log('\n🎉 所有关键问题已修复！');
      } else {
        console.log('\n⚠️ 仍有严重错误需要处理');
      }
    } else {
      console.log('❌ 基础功能测试: 失败');
      console.log(`错误: ${result.error}`);
    }
    
    console.log('='.repeat(50));
  }

  async runTest() {
    try {
      await this.init();
      const result = await this.testBasicFunctionality();
      await this.generateReport(result);
      
      console.log('\n🎯 快速测试完成！');
      
    } catch (error) {
      console.error('❌ 测试运行失败:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }
}

// 运行测试
const tester = new QuickTester();
tester.runTest().catch(console.error);
