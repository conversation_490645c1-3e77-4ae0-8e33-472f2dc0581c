<template>
  <div class="strategy-editor-page">
    <div class="page-header">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item :to="{ path: '/dashboard' }">首页</el-breadcrumb-item>
        <el-breadcrumb-item>策略开发</el-breadcrumb-item>
        <el-breadcrumb-item>策略编辑器</el-breadcrumb-item>
      </el-breadcrumb>
    </div>

    <div class="page-content">
      <el-tabs v-model="activeTab" @tab-click="handleTabClick">
        <el-tab-pane label="策略编辑器" name="editor">
          <StrategyEditor 
            @strategy-saved="handleStrategySaved"
            @strategy-tested="handleStrategyTested"
          />
        </el-tab-pane>
        
        <el-tab-pane label="参数优化" name="optimizer">
          <ParameterOptimizer 
            @optimization-completed="handleOptimizationCompleted"
          />
        </el-tab-pane>
        
        <el-tab-pane label="策略库" name="library">
          <StrategyLibrary 
            @strategy-selected="handleStrategySelected"
            @strategy-imported="handleStrategyImported"
          />
        </el-tab-pane>
        
        <el-tab-pane label="回测报告" name="reports">
          <BacktestReports 
            :reports="backtestReports"
            @report-selected="handleReportSelected"
          />
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import StrategyEditor from '@/components/strategy/StrategyEditor.vue'
import ParameterOptimizer from '@/components/strategy/ParameterOptimizer.vue'
import StrategyLibrary from '@/components/strategy/StrategyLibrary.vue'
import BacktestReports from '@/components/strategy/BacktestReports.vue'

const activeTab = ref('editor')
const backtestReports = ref([])

const handleTabClick = (tab: any) => {
  console.log('切换到标签:', tab.name)
}

const handleStrategySaved = (strategy: any) => {
  ElMessage.success(`策略 "${strategy.name}" 保存成功`)
  // 可以在这里添加保存后的逻辑，比如刷新策略库
}

const handleStrategyTested = (result: any) => {
  ElMessage.success('策略回测完成')
  // 添加新的回测报告
  backtestReports.value.unshift({
    id: Date.now(),
    strategyName: result.strategyName || '未命名策略',
    date: new Date().toISOString().split('T')[0],
    ...result
  })
  
  // 切换到回测报告标签
  activeTab.value = 'reports'
}

const handleOptimizationCompleted = (result: any) => {
  ElMessage.success('参数优化完成')
  console.log('优化结果:', result)
}

const handleStrategySelected = (strategy: any) => {
  ElMessage.info(`选择了策略: ${strategy.name}`)
  // 可以将策略信息传递给编辑器
}

const handleStrategyImported = (strategy: any) => {
  ElMessage.success(`策略 "${strategy.name}" 导入成功`)
  // 切换到编辑器标签
  activeTab.value = 'editor'
}

const handleReportSelected = (report: any) => {
  ElMessage.info(`查看报告: ${report.strategyName}`)
  // 显示详细的回测报告
}

onMounted(() => {
  // 页面加载时的初始化逻辑
  console.log('策略编辑器页面已加载')
})
</script>

<style scoped>
.strategy-editor-page {
  height: calc(100vh - 80px);
  display: flex;
  flex-direction: column;
}

.page-header {
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #ebeef5;
}

.page-content {
  flex: 1;
  padding: 20px;
  background: #f5f7fa;
  overflow: hidden;
}

:deep(.el-tabs) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

:deep(.el-tabs__content) {
  flex: 1;
  padding: 0;
  overflow: hidden;
}

:deep(.el-tab-pane) {
  height: 100%;
  overflow: auto;
}

:deep(.el-tabs__header) {
  margin: 0 0 16px 0;
  background: white;
  padding: 0 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

:deep(.el-tabs__nav-wrap) {
  padding: 16px 0;
}
</style>