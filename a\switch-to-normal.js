/**
 * 切换回普通版本的脚本
 */

const fs = require('fs');

console.log('🔄 正在切换回普通版本...');

// 检查是否有备份文件
if (fs.existsSync('index-backup.html')) {
    const backupContent = fs.readFileSync('index-backup.html', 'utf8');
    fs.writeFileSync('index.html', backupContent);
    console.log('✅ 已恢复普通版本');
    
    // 删除备份文件
    fs.unlinkSync('index-backup.html');
    console.log('✅ 已清理备份文件');
} else {
    // 如果没有备份，创建标准的index.html
    const standardIndex = `<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8">
    <link rel="icon" href="/favicon.ico">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>量化投资平台</title>
    <!-- 防止缓存 -->
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
  </head>
  <body>
    <div id="app"></div>
    <script type="module" src="/src/main.ts"></script>
  </body>
</html>`;
    
    fs.writeFileSync('index.html', standardIndex);
    console.log('✅ 已创建标准版本');
}

console.log('\n🚀 普通版本已激活！');
console.log('📍 访问地址: http://localhost:5173/');
console.log('🔧 要切换到专业版本，请运行: node switch-to-professional.js');
