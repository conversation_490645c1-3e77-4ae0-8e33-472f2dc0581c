<template>
  <div id="app">
    <!-- 根据环境变量决定显示哪个组件 -->
    <SimpleDashboard v-if="useDirectDashboard" />
    <RouterView v-else />
  </div>
</template>

<script setup lang="ts">
import { RouterView } from 'vue-router'
import { computed } from 'vue'
import SimpleDashboard from '@/views/Dashboard/SimpleDashboard.vue'

// 检查是否使用直接仪表盘模式
const useDirectDashboard = computed(() => {
  return import.meta.env.VITE_USE_DIRECT_DASHBOARD === 'true'
})
</script>

<style>
#app {
  width: 100%;
  min-height: 100vh;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  background-color: #f5f7fa;
}
</style>
