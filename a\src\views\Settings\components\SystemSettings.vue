<template>
  <div class="system-settings">
    <div class="settings-section">
      <h3 class="section-title">
        <el-icon><Tools /></el-icon>
        系统设置
      </h3>
      <p class="section-description">管理系统级别的配置和偏好</p>

      <el-form :model="form" label-width="150px">
        <!-- 语言和地区 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>语言和地区</span>
          </template>
          
          <el-form-item label="界面语言">
            <el-select v-model="form.language" placeholder="选择语言">
              <el-option label="简体中文" value="zh-CN" />
              <el-option label="繁体中文" value="zh-TW" />
              <el-option label="English" value="en-US" />
            </el-select>
          </el-form-item>

          <el-form-item label="时区">
            <el-select v-model="form.timezone" placeholder="选择时区">
              <el-option label="北京时间 (UTC+8)" value="Asia/Shanghai" />
              <el-option label="香港时间 (UTC+8)" value="Asia/Hong_Kong" />
              <el-option label="纽约时间 (UTC-5)" value="America/New_York" />
            </el-select>
          </el-form-item>

          <el-form-item label="货币单位">
            <el-select v-model="form.currency" placeholder="选择货币">
              <el-option label="人民币 (CNY)" value="CNY" />
              <el-option label="港币 (HKD)" value="HKD" />
              <el-option label="美元 (USD)" value="USD" />
            </el-select>
          </el-form-item>
        </el-card>

        <!-- 数据和缓存 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>数据和缓存</span>
          </template>
          
          <el-form-item label="自动刷新">
            <el-switch v-model="form.autoRefresh" />
            <span class="switch-label">自动刷新市场数据</span>
          </el-form-item>

          <el-form-item label="刷新间隔">
            <el-input-number
              v-model="form.refreshInterval"
              :min="1"
              :max="60"
              :disabled="!form.autoRefresh"
              controls-position="right"
            />
            <span class="input-suffix">秒</span>
          </el-form-item>

          <el-form-item label="缓存管理">
            <div class="cache-actions">
              <el-button @click="clearCache" :loading="clearing">
                清除缓存
              </el-button>
              <span class="cache-info">当前缓存大小: 15.2 MB</span>
            </div>
          </el-form-item>
        </el-card>

        <!-- 性能设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>性能设置</span>
          </template>
          
          <el-form-item label="动画效果">
            <el-switch v-model="form.enableAnimation" />
            <span class="switch-label">启用界面动画效果</span>
          </el-form-item>

          <el-form-item label="图表渲染">
            <el-radio-group v-model="form.chartRenderer">
              <el-radio label="canvas">Canvas (推荐)</el-radio>
              <el-radio label="svg">SVG</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="数据点数限制">
            <el-input-number
              v-model="form.maxDataPoints"
              :min="1000"
              :max="10000"
              :step="1000"
              controls-position="right"
            />
            <span class="input-suffix">个</span>
          </el-form-item>
        </el-card>

        <!-- 安全设置 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>安全设置</span>
          </template>
          
          <el-form-item label="自动登出">
            <el-switch v-model="form.autoLogout" />
            <span class="switch-label">长时间无操作自动登出</span>
          </el-form-item>

          <el-form-item label="登出时间">
            <el-input-number
              v-model="form.logoutTimeout"
              :min="5"
              :max="120"
              :disabled="!form.autoLogout"
              controls-position="right"
            />
            <span class="input-suffix">分钟</span>
          </el-form-item>

          <el-form-item label="记住登录">
            <el-switch v-model="form.rememberLogin" />
            <span class="switch-label">记住登录状态</span>
          </el-form-item>
        </el-card>

        <!-- 开发者选项 -->
        <el-card class="form-section" shadow="never">
          <template #header>
            <span>开发者选项</span>
          </template>
          
          <el-form-item label="调试模式">
            <el-switch v-model="form.debugMode" />
            <span class="switch-label">启用调试信息</span>
          </el-form-item>

          <el-form-item label="API日志">
            <el-switch v-model="form.apiLogging" />
            <span class="switch-label">记录API请求日志</span>
          </el-form-item>

          <el-form-item label="性能监控">
            <el-switch v-model="form.performanceMonitor" />
            <span class="switch-label">启用性能监控</span>
          </el-form-item>
        </el-card>

        <el-form-item>
          <el-button type="primary" @click="saveSettings" :loading="saving">
            保存设置
          </el-button>
          <el-button @click="resetSettings">重置</el-button>
          <el-button @click="exportSettings">导出配置</el-button>
          <el-button @click="importSettings">导入配置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { ElMessage } from 'element-plus'
import { Tools } from '@element-plus/icons-vue'

const saving = ref(false)
const clearing = ref(false)

const form = reactive({
  language: 'zh-CN',
  timezone: 'Asia/Shanghai',
  currency: 'CNY',
  autoRefresh: true,
  refreshInterval: 5,
  enableAnimation: true,
  chartRenderer: 'canvas',
  maxDataPoints: 5000,
  autoLogout: true,
  logoutTimeout: 30,
  rememberLogin: true,
  debugMode: false,
  apiLogging: false,
  performanceMonitor: false
})

const saveSettings = async () => {
  saving.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('系统设置保存成功')
  } finally {
    saving.value = false
  }
}

const resetSettings = () => {
  Object.assign(form, {
    language: 'zh-CN',
    timezone: 'Asia/Shanghai',
    currency: 'CNY',
    autoRefresh: true,
    refreshInterval: 5,
    enableAnimation: true,
    chartRenderer: 'canvas',
    maxDataPoints: 5000,
    autoLogout: true,
    logoutTimeout: 30,
    rememberLogin: true,
    debugMode: false,
    apiLogging: false,
    performanceMonitor: false
  })
}

const clearCache = async () => {
  clearing.value = true
  try {
    await new Promise(resolve => setTimeout(resolve, 1000))
    ElMessage.success('缓存清除成功')
  } finally {
    clearing.value = false
  }
}

const exportSettings = () => {
  const settings = JSON.stringify(form, null, 2)
  const blob = new Blob([settings], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = 'system-settings.json'
  a.click()
  URL.revokeObjectURL(url)
  ElMessage.success('配置导出成功')
}

const importSettings = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e: any) => {
    const file = e.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e: any) => {
        try {
          const settings = JSON.parse(e.target.result)
          Object.assign(form, settings)
          ElMessage.success('配置导入成功')
        } catch (error) {
          ElMessage.error('配置文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}
</script>

<style scoped>
.system-settings {
  max-width: 800px;
}

.settings-section {
  background: white;
  border-radius: 8px;
  padding: 24px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.section-title {
  display: flex;
  align-items: center;
  font-size: 18px;
  font-weight: 600;
  color: #333;
  margin: 0 0 8px 0;
}

.section-title .el-icon {
  margin-right: 8px;
  color: #409eff;
}

.section-description {
  color: #666;
  font-size: 14px;
  margin: 0 0 24px 0;
}

.form-section {
  margin-bottom: 24px;
  border: 1px solid #e6e6e6;
}

.input-suffix {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.switch-label {
  margin-left: 8px;
  color: #666;
  font-size: 14px;
}

.cache-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.cache-info {
  color: #666;
  font-size: 14px;
}
</style>
