/**
 * API客户端
 * 提供与后端API的统一交互接口
 */

export interface BacktestConfig {
  initial_capital: number;
  start_date: string;
  end_date: string;
  benchmark: string;
  commission_rate: number;
  stamp_tax_rate: number;
  transfer_fee_rate: number;
  min_commission: number;
  slippage_type: 'percentage' | 'fixed' | 'volume_based' | 'market_impact';
  slippage_value: number;
  cash_interest_rate: number;
}

export interface StrategyConfig {
  name: string;
  description: string;
  strategy_type: 'momentum' | 'mean_reversion' | 'pairs_trading' | 'arbitrage' | 'factor' | 'machine_learning';
  version: string;
  parameters: Record<string, any>;
  universe: string[];
  max_positions: number;
  position_size: number;
  stop_loss?: number;
  take_profit?: number;
  max_drawdown: number;
  start_date?: string;
  end_date?: string;
  benchmark: string;
}

export interface RiskLimits {
  max_position_size: number;
  max_total_position: number;
  max_sector_exposure: number;
  max_positions_count: number;
  max_daily_loss: number;
  max_drawdown: number;
  stop_loss_threshold: number;
  max_portfolio_volatility: number;
  max_tracking_error: number;
  min_avg_volume: number;
  max_volume_participation: number;
  var_confidence_level: number;
  max_var: number;
}

export interface BacktestRequest {
  strategy_config: StrategyConfig;
  backtest_config: BacktestConfig;
  risk_limits?: RiskLimits;
  data_source: string;
}

export interface BacktestResult {
  task_id: string;
  status: 'created' | 'running' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  summary?: {
    total_return: number;
    annual_return: number;
    volatility: number;
    sharpe_ratio: number;
    max_drawdown: number;
    win_rate: number;
    profit_factor: number;
  };
  portfolio_value?: number[];
  benchmark_value?: number[];
  dates?: string[];
  trades?: any[];
  positions?: any;
  error?: string;
}

export interface APIResponse<T> {
  success: boolean;
  data?: T;
  error?: {
    error: string;
    message: string;
    details?: any;
    timestamp: string;
  };
  timestamp: string;
  request_id?: string;
}

class APIClient {
  private baseURL: string;
  private token?: string;

  constructor(baseURL: string = 'http://localhost:8000') {
    this.baseURL = baseURL;
  }

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<APIResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        return {
          success: false,
          error: {
            error: 'HTTP_ERROR',
            message: data.message || `HTTP ${response.status}`,
            details: data,
            timestamp: new Date().toISOString(),
          },
          timestamp: new Date().toISOString(),
        };
      }

      return {
        success: true,
        data,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      return {
        success: false,
        error: {
          error: 'NETWORK_ERROR',
          message: error instanceof Error ? error.message : 'Network error',
          timestamp: new Date().toISOString(),
        },
        timestamp: new Date().toISOString(),
      };
    }
  }

  // 回测相关API
  async createBacktest(request: BacktestRequest) {
    return this.request<{ task_id: string; status: string; message: string }>(
      '/api/v1/backtest/create',
      {
        method: 'POST',
        body: JSON.stringify(request),
      }
    );
  }

  async getBacktestStatus(taskId: string) {
    return this.request<BacktestResult>(`/api/v1/backtest/status/${taskId}`);
  }

  async listBacktests() {
    return this.request<{ tasks: any[] }>('/api/v1/backtest/list');
  }

  async cancelBacktest(taskId: string) {
    return this.request<{ message: string }>(
      `/api/v1/backtest/cancel/${taskId}`,
      { method: 'DELETE' }
    );
  }

  async deleteBacktest(taskId: string) {
    return this.request<{ message: string }>(
      `/api/v1/backtest/delete/${taskId}`,
      { method: 'DELETE' }
    );
  }

  async getBacktestResults(taskId: string) {
    return this.request<any>(`/api/v1/backtest/results/${taskId}`);
  }

  async getBacktestReport(taskId: string, format: 'json' | 'html' = 'json') {
    return this.request<any>(`/api/v1/backtest/report/${taskId}?format=${format}`);
  }

  async compareBacktests(taskIds: string[]) {
    return this.request<any>('/api/v1/backtest/compare', {
      method: 'POST',
      body: JSON.stringify(taskIds),
    });
  }

  async getAvailableStrategies() {
    return this.request<{ strategies: any[] }>('/api/v1/backtest/strategies');
  }

  async getConfigTemplate(strategyType: string) {
    return this.request<any>(`/api/v1/backtest/config/template?strategy_type=${strategyType}`);
  }

  // WebSocket连接
  createWebSocket(clientId: string): WebSocketManager {
    return new WebSocketManager(`${this.baseURL.replace('http', 'ws')}/ws/${clientId}`);
  }
}

export class WebSocketManager {
  private ws?: WebSocket;
  private url: string;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private isConnecting = false;

  constructor(url: string) {
    this.url = url;
  }

  connect(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (this.isConnecting || (this.ws && this.ws.readyState === WebSocket.OPEN)) {
        resolve();
        return;
      }

      this.isConnecting = true;

      this.ws = new WebSocket(this.url);

      this.ws.onopen = () => {
        console.log('WebSocket connected');
        this.isConnecting = false;
        this.reconnectAttempts = 0;
        resolve();
      };

      this.ws.onmessage = (event) => {
        try {
          const message = JSON.parse(event.data);
          const handler = this.messageHandlers.get(message.type);
          if (handler) {
            handler(message.data);
          }
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.isConnecting = false;
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.isConnecting = false;
        reject(error);
      };
    });
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect().catch(console.error);
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  onMessage(type: string, handler: (data: any) => void) {
    this.messageHandlers.set(type, handler);
  }

  offMessage(type: string) {
    this.messageHandlers.delete(type);
  }

  send(type: string, data: any) {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(JSON.stringify({ type, data }));
    } else {
      console.warn('WebSocket is not connected');
    }
  }

  subscribe(topics: string[]) {
    this.send('subscribe', { topics });
  }

  unsubscribe(topics: string[]) {
    this.send('unsubscribe', { topics });
  }

  ping() {
    this.send('ping', {});
  }

  getBacktestStatus(taskId: string) {
    this.send('backtest_control', { action: 'status', task_id: taskId });
  }

  cancelBacktest(taskId: string) {
    this.send('backtest_control', { action: 'cancel', task_id: taskId });
  }

  getServerStatus() {
    this.send('get_status', { status_type: 'server' });
  }

  getBacktestSummary() {
    this.send('get_status', { status_type: 'backtest_summary' });
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = undefined;
    }
  }
}

// 单例API客户端
export const apiClient = new APIClient();

export default APIClient;