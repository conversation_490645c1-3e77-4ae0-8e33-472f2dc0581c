<!--
  性能监控组件
  用于监控和显示应用性能指标
-->

<template>
  <div v-if="showMonitor" class="performance-monitor">
    <div class="monitor-header">
      <h4>性能监控</h4>
      <button @click="toggleMonitor" class="toggle-btn">
        {{ isExpanded ? '收起' : '展开' }}
      </button>
    </div>
    
    <div v-if="isExpanded" class="monitor-content">
      <!-- 基础性能指标 -->
      <div class="metrics-section">
        <h5>基础指标</h5>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">FPS</span>
            <span class="metric-value" :class="getFpsClass(metrics.fps)">
              {{ metrics.fps }}
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">内存使用</span>
            <span class="metric-value">
              {{ formatBytes(metrics.memoryUsage) }}
            </span>
          </div>
          <div class="metric-item">
            <span class="metric-label">DOM节点</span>
            <span class="metric-value">{{ metrics.domNodes }}</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">网络请求</span>
            <span class="metric-value">{{ metrics.networkRequests }}</span>
          </div>
        </div>
      </div>

      <!-- 页面加载性能 -->
      <div class="metrics-section">
        <h5>页面加载</h5>
        <div class="metrics-grid">
          <div class="metric-item">
            <span class="metric-label">首次内容绘制</span>
            <span class="metric-value">{{ metrics.fcp }}ms</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">最大内容绘制</span>
            <span class="metric-value">{{ metrics.lcp }}ms</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">首次输入延迟</span>
            <span class="metric-value">{{ metrics.fid }}ms</span>
          </div>
          <div class="metric-item">
            <span class="metric-label">累积布局偏移</span>
            <span class="metric-value">{{ metrics.cls }}</span>
          </div>
        </div>
      </div>

      <!-- 资源加载 -->
      <div class="metrics-section">
        <h5>资源加载</h5>
        <div class="resource-list">
          <div 
            v-for="resource in slowResources" 
            :key="resource.name"
            class="resource-item"
          >
            <span class="resource-name">{{ getResourceName(resource.name) }}</span>
            <span class="resource-time">{{ resource.duration.toFixed(2) }}ms</span>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="monitor-actions">
        <button @click="clearMetrics" class="action-btn">清除数据</button>
        <button @click="exportMetrics" class="action-btn">导出报告</button>
        <button @click="toggleAutoRefresh" class="action-btn">
          {{ autoRefresh ? '停止刷新' : '自动刷新' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, computed } from 'vue'

interface PerformanceMetrics {
  fps: number
  memoryUsage: number
  domNodes: number
  networkRequests: number
  fcp: number
  lcp: number
  fid: number
  cls: number
}

interface ResourceTiming {
  name: string
  duration: number
  size?: number
}

// 响应式数据
const showMonitor = ref(import.meta.env.DEV)
const isExpanded = ref(false)
const autoRefresh = ref(true)

const metrics = reactive<PerformanceMetrics>({
  fps: 0,
  memoryUsage: 0,
  domNodes: 0,
  networkRequests: 0,
  fcp: 0,
  lcp: 0,
  fid: 0,
  cls: 0
})

const slowResources = ref<ResourceTiming[]>([])

// 性能监控相关变量
let frameCount = 0
let lastTime = performance.now()
let animationId: number
let refreshInterval: number
let performanceObserver: PerformanceObserver | null = null

// 计算属性
const getFpsClass = (fps: number) => {
  if (fps >= 55) return 'fps-good'
  if (fps >= 30) return 'fps-medium'
  return 'fps-poor'
}

// 工具函数
const formatBytes = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const getResourceName = (url: string): string => {
  try {
    const urlObj = new URL(url)
    return urlObj.pathname.split('/').pop() || urlObj.hostname
  } catch {
    return url.substring(url.lastIndexOf('/') + 1) || url
  }
}

// FPS 监控
const measureFPS = () => {
  frameCount++
  const currentTime = performance.now()
  
  if (currentTime >= lastTime + 1000) {
    metrics.fps = Math.round((frameCount * 1000) / (currentTime - lastTime))
    frameCount = 0
    lastTime = currentTime
  }
  
  animationId = requestAnimationFrame(measureFPS)
}

// 内存使用监控
const measureMemory = () => {
  if ('memory' in performance) {
    const memory = (performance as any).memory
    metrics.memoryUsage = memory.usedJSHeapSize
  }
}

// DOM 节点计数
const countDOMNodes = () => {
  metrics.domNodes = document.querySelectorAll('*').length
}

// 网络请求监控
const monitorNetworkRequests = () => {
  if (!performanceObserver) {
    performanceObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      
      entries.forEach((entry) => {
        if (entry.entryType === 'resource') {
          metrics.networkRequests++
          
          // 记录慢资源
          if (entry.duration > 1000) {
            slowResources.value.push({
              name: entry.name,
              duration: entry.duration,
              size: (entry as any).transferSize
            })
            
            // 只保留最近的10个慢资源
            if (slowResources.value.length > 10) {
              slowResources.value = slowResources.value.slice(-10)
            }
          }
        }
      })
    })
    
    performanceObserver.observe({ entryTypes: ['resource', 'navigation'] })
  }
}

// Web Vitals 监控
const measureWebVitals = () => {
  // FCP (First Contentful Paint)
  const fcpEntry = performance.getEntriesByName('first-contentful-paint')[0]
  if (fcpEntry) {
    metrics.fcp = Math.round(fcpEntry.startTime)
  }

  // LCP (Largest Contentful Paint)
  if ('PerformanceObserver' in window) {
    const lcpObserver = new PerformanceObserver((list) => {
      const entries = list.getEntries()
      const lastEntry = entries[entries.length - 1]
      metrics.lcp = Math.round(lastEntry.startTime)
    })
    
    try {
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] })
    } catch (e) {
      console.warn('LCP monitoring not supported')
    }
  }
}

// 更新所有指标
const updateMetrics = () => {
  measureMemory()
  countDOMNodes()
  measureWebVitals()
}

// 控制函数
const toggleMonitor = () => {
  isExpanded.value = !isExpanded.value
}

const clearMetrics = () => {
  metrics.networkRequests = 0
  slowResources.value = []
  frameCount = 0
  lastTime = performance.now()
}

const exportMetrics = () => {
  const report = {
    timestamp: new Date().toISOString(),
    metrics: { ...metrics },
    slowResources: [...slowResources.value],
    userAgent: navigator.userAgent,
    url: window.location.href
  }
  
  const blob = new Blob([JSON.stringify(report, null, 2)], { 
    type: 'application/json' 
  })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `performance-report-${Date.now()}.json`
  a.click()
  URL.revokeObjectURL(url)
}

const toggleAutoRefresh = () => {
  autoRefresh.value = !autoRefresh.value
  
  if (autoRefresh.value) {
    refreshInterval = setInterval(updateMetrics, 2000)
  } else {
    clearInterval(refreshInterval)
  }
}

// 生命周期
onMounted(() => {
  if (showMonitor.value) {
    // 开始 FPS 监控
    measureFPS()
    
    // 开始网络监控
    monitorNetworkRequests()
    
    // 定期更新指标
    updateMetrics()
    if (autoRefresh.value) {
      refreshInterval = setInterval(updateMetrics, 2000)
    }
  }
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  
  if (refreshInterval) {
    clearInterval(refreshInterval)
  }
  
  if (performanceObserver) {
    performanceObserver.disconnect()
  }
})
</script>

<style scoped>
.performance-monitor {
  position: fixed;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.9);
  color: white;
  border-radius: 8px;
  padding: 12px;
  font-family: 'Monaco', 'Menlo', monospace;
  font-size: 12px;
  z-index: 9999;
  min-width: 280px;
  max-width: 400px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.monitor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.monitor-header h4 {
  margin: 0;
  font-size: 14px;
  color: #00ff88;
}

.toggle-btn, .action-btn {
  background: #333;
  color: white;
  border: 1px solid #555;
  border-radius: 4px;
  padding: 4px 8px;
  cursor: pointer;
  font-size: 11px;
}

.toggle-btn:hover, .action-btn:hover {
  background: #555;
}

.metrics-section {
  margin-bottom: 12px;
}

.metrics-section h5 {
  margin: 0 0 6px 0;
  font-size: 12px;
  color: #ffaa00;
  border-bottom: 1px solid #333;
  padding-bottom: 2px;
}

.metrics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 6px;
}

.metric-item {
  display: flex;
  justify-content: space-between;
  padding: 2px 0;
}

.metric-label {
  color: #ccc;
}

.metric-value {
  color: white;
  font-weight: bold;
}

.fps-good { color: #00ff88; }
.fps-medium { color: #ffaa00; }
.fps-poor { color: #ff4444; }

.resource-list {
  max-height: 120px;
  overflow-y: auto;
}

.resource-item {
  display: flex;
  justify-content: space-between;
  padding: 2px 0;
  border-bottom: 1px solid #333;
}

.resource-name {
  color: #ccc;
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 8px;
}

.resource-time {
  color: #ff4444;
  font-weight: bold;
}

.monitor-actions {
  display: flex;
  gap: 6px;
  margin-top: 8px;
}

.action-btn {
  flex: 1;
}
</style>
