/**
 * 投资组合服务
 * 处理投资组合相关的API调用
 */

import { httpClient } from '@/api/http'
import type { 
  Portfolio, 
  PortfolioPosition, 
  PortfolioPerformance, 
  PortfolioAllocation 
} from '@/types/portfolio'
import type { ApiResponse, ListResponse } from '@/types/api'

export class PortfolioService {
  private static instance: PortfolioService

  static getInstance(): PortfolioService {
    if (!PortfolioService.instance) {
      PortfolioService.instance = new PortfolioService()
    }
    return PortfolioService.instance
  }

  /**
   * 获取投资组合列表
   */
  async getPortfolios(): Promise<ListResponse<Portfolio>> {
    const response = await httpClient.get<Portfolio[]>('/portfolio/list')
    return {
      success: true,
      data: response.data,
      message: '获取投资组合列表成功',
      total: response.data.length
    }
  }

  /**
   * 获取投资组合详情
   */
  async getPortfolio(portfolioId: string): Promise<ApiResponse<Portfolio>> {
    const response = await httpClient.get<Portfolio>(`/portfolio/${portfolioId}`)
    return {
      success: true,
      data: response.data,
      message: '获取投资组合详情成功'
    }
  }

  /**
   * 创建投资组合
   */
  async createPortfolio(portfolioData: Omit<Portfolio, 'id' | 'createdAt' | 'updatedAt'>): Promise<ApiResponse<Portfolio>> {
    const response = await httpClient.post<Portfolio>('/portfolio/create', portfolioData)
    return {
      success: true,
      data: response.data,
      message: '创建投资组合成功'
    }
  }

  /**
   * 更新投资组合
   */
  async updatePortfolio(portfolioId: string, updates: Partial<Portfolio>): Promise<ApiResponse<Portfolio>> {
    const response = await httpClient.put<Portfolio>(`/portfolio/${portfolioId}`, updates)
    return {
      success: true,
      data: response.data,
      message: '更新投资组合成功'
    }
  }

  /**
   * 删除投资组合
   */
  async deletePortfolio(portfolioId: string): Promise<ApiResponse<void>> {
    await httpClient.delete(`/portfolio/${portfolioId}`)
    return {
      success: true,
      data: undefined,
      message: '删除投资组合成功'
    }
  }

  /**
   * 获取投资组合持仓
   */
  async getPositions(portfolioId: string): Promise<ListResponse<PortfolioPosition>> {
    const response = await httpClient.get<PortfolioPosition[]>(`/portfolio/${portfolioId}/positions`)
    return {
      success: true,
      data: response.data,
      message: '获取持仓数据成功',
      total: response.data.length
    }
  }

  /**
   * 获取投资组合绩效
   */
  async getPerformance(portfolioId: string): Promise<ApiResponse<PortfolioPerformance>> {
    const response = await httpClient.get<PortfolioPerformance>(`/portfolio/${portfolioId}/performance`)
    return {
      success: true,
      data: response.data,
      message: '获取绩效数据成功'
    }
  }

  /**
   * 获取投资组合资产配置
   */
  async getAllocation(portfolioId: string): Promise<ListResponse<PortfolioAllocation>> {
    const response = await httpClient.get<PortfolioAllocation[]>(`/portfolio/${portfolioId}/allocation`)
    return {
      success: true,
      data: response.data,
      message: '获取资产配置成功',
      total: response.data.length
    }
  }

  /**
   * 获取投资组合趋势数据
   */
  async getPortfolioTrend(params: { 
    portfolioId: string
    timeRange: string 
    symbol?: string 
  }): Promise<ApiResponse<any[]>> {
    const response = await httpClient.get<any[]>(`/portfolio/${params.portfolioId}/trend`, {
      params: {
        timeRange: params.timeRange,
        symbol: params.symbol
      }
    })
    return {
      success: true,
      data: response.data,
      message: '获取趋势数据成功'
    }
  }

  /**
   * 添加持仓
   */
  async addPosition(portfolioId: string, positionData: {
    symbol: string
    quantity: number
    price: number
  }): Promise<ApiResponse<PortfolioPosition>> {
    const response = await httpClient.post<PortfolioPosition>(
      `/portfolio/${portfolioId}/positions`, 
      positionData
    )
    return {
      success: true,
      data: response.data,
      message: '添加持仓成功'
    }
  }

  /**
   * 更新持仓
   */
  async updatePosition(
    portfolioId: string, 
    positionId: string, 
    updates: Partial<PortfolioPosition>
  ): Promise<ApiResponse<PortfolioPosition>> {
    const response = await httpClient.put<PortfolioPosition>(
      `/portfolio/${portfolioId}/positions/${positionId}`, 
      updates
    )
    return {
      success: true,
      data: response.data,
      message: '更新持仓成功'
    }
  }

  /**
   * 删除持仓
   */
  async removePosition(portfolioId: string, positionId: string): Promise<ApiResponse<void>> {
    await httpClient.delete(`/portfolio/${portfolioId}/positions/${positionId}`)
    return {
      success: true,
      data: undefined,
      message: '删除持仓成功'
    }
  }

  /**
   * 获取投资组合历史记录
   */
  async getHistory(portfolioId: string, params?: {
    startDate?: string
    endDate?: string
    limit?: number
  }): Promise<ListResponse<any>> {
    const response = await httpClient.get<any[]>(`/portfolio/${portfolioId}/history`, { params })
    return {
      success: true,
      data: response.data,
      message: '获取历史记录成功',
      total: response.data.length
    }
  }

  /**
   * 重新平衡投资组合
   */
  async rebalance(portfolioId: string, targetAllocation: PortfolioAllocation[]): Promise<ApiResponse<any>> {
    const response = await httpClient.post<any>(`/portfolio/${portfolioId}/rebalance`, {
      targetAllocation
    })
    return {
      success: true,
      data: response.data,
      message: '重新平衡成功'
    }
  }

  /**
   * 获取投资组合风险指标
   */
  async getRiskMetrics(portfolioId: string): Promise<ApiResponse<any>> {
    const response = await httpClient.get<any>(`/portfolio/${portfolioId}/risk`)
    return {
      success: true,
      data: response.data,
      message: '获取风险指标成功'
    }
  }
}

// 创建单例实例
export const portfolioService = PortfolioService.getInstance()
