/**
 * API客户端服务
 * 提供统一的HTTP请求接口
 */

import axios from 'axios'
import type { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

// API配置
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
}

// 创建axios实例
const apiClient: AxiosInstance = axios.create(API_CONFIG)

// 请求拦截器
apiClient.interceptors.request.use(
  (config) => {
    // 添加认证token
    const token = localStorage.getItem('access_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }

    // 添加请求时间戳
    config.metadata = { startTime: new Date() }

    return config
  },
  (error) => {
    console.error('Request error:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    // 计算请求耗时
    const endTime = new Date()
    const startTime = response.config.metadata?.startTime
    if (startTime) {
      const duration = endTime.getTime() - startTime.getTime()
      console.log(`API Request: ${response.config.method?.toUpperCase()} ${response.config.url} - ${duration}ms`)
    }

    return response
  },
  (error) => {
    // 统一错误处理
    if (error.response) {
      // 服务器响应错误
      const { status, data } = error.response
      console.error(`API Error ${status}:`, data)

      // 处理特定错误状态
      switch (status) {
        case 401:
          // 未授权，清除token并跳转登录
          localStorage.removeItem('access_token')
          window.location.href = '/login'
          break
        case 403:
          console.error('Access forbidden')
          break
        case 404:
          console.error('Resource not found')
          break
        case 500:
          console.error('Internal server error')
          break
        default:
          console.error('Unknown error')
      }
    } else if (error.request) {
      // 网络错误
      console.error('Network error:', error.request)
    } else {
      // 其他错误
      console.error('Error:', error.message)
    }

    return Promise.reject(error)
  }
)

// API方法封装
export const api = {
  // GET请求
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.get(url, config)
  },

  // POST请求
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.post(url, data, config)
  },

  // PUT请求
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.put(url, data, config)
  },

  // DELETE请求
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.delete(url, config)
  },

  // PATCH请求
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> => {
    return apiClient.patch(url, data, config)
  }
}

// 导出axios实例（兼容现有代码）
export { apiClient }

// 导出默认实例
export default apiClient

// 类型定义
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  message?: string
  error?: string
  code?: number
}

// 通用API响应处理
export const handleApiResponse = <T>(response: AxiosResponse<ApiResponse<T>>): T => {
  const { data } = response

  if (data.success) {
    return data.data as T
  } else {
    throw new Error(data.message || data.error || 'API request failed')
  }
}

// 创建带有基础URL的API客户端
export const createApiClient = (baseURL: string): AxiosInstance => {
  return axios.create({
    ...API_CONFIG,
    baseURL
  })
}

// WebSocket URL构建
export const getWebSocketUrl = (path: string = ''): string => {
  const baseUrl = API_CONFIG.baseURL || 'http://localhost:8000'
  const wsUrl = baseUrl.replace(/^http/, 'ws')
  return `${wsUrl}${path}`
}

// 文件上传
export const uploadFile = async (
  url: string,
  file: File,
  onProgress?: (progress: number) => void
): Promise<AxiosResponse> => {
  const formData = new FormData()
  formData.append('file', file)

  return apiClient.post(url, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    },
    onUploadProgress: (progressEvent) => {
      if (onProgress && progressEvent.total) {
        const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total)
        onProgress(progress)
      }
    }
  })
}

// 下载文件
export const downloadFile = async (url: string, filename?: string): Promise<void> => {
  const response = await apiClient.get(url, {
    responseType: 'blob'
  })

  const blob = new Blob([response.data])
  const downloadUrl = window.URL.createObjectURL(blob)
  const link = document.createElement('a')
  link.href = downloadUrl
  link.download = filename || 'download'
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
  window.URL.revokeObjectURL(downloadUrl)
}

// 健康检查
export const healthCheck = async (): Promise<boolean> => {
  try {
    const response = await apiClient.get('/health')
    return response.status === 200
  } catch (error) {
    console.error('Health check failed:', error)
    return false
  }
}

// 获取API状态
export const getApiStatus = async (): Promise<any> => {
  try {
    const response = await apiClient.get('/')
    return response.data
  } catch (error) {
    console.error('Failed to get API status:', error)
    throw error
  }
}
