<template>
  <div class="strategy-detail">
    <!-- 策略头部信息 -->
    <div class="strategy-header">
      <div class="header-content">
        <div class="strategy-info">
          <h1 class="strategy-title">{{ strategy.name }}</h1>
          <div class="strategy-meta">
            <el-tag :type="getStrategyStatusType(strategy.status)">
              {{ getStrategyStatusText(strategy.status) }}
            </el-tag>
            <span class="strategy-author">作者: {{ strategy.author }}</span>
            <span class="strategy-date">创建时间: {{ formatDate(strategy.createTime) }}</span>
          </div>
          <p class="strategy-description">{{ strategy.description }}</p>
        </div>
        
        <div class="strategy-actions">
          <el-button type="primary" @click="subscribeStrategy">
            <el-icon><Star /></el-icon>
            订阅策略
          </el-button>
          <el-button @click="backTestStrategy">
            <el-icon><TrendCharts /></el-icon>
            回测
          </el-button>
          <el-button @click="copyStrategy">
            <el-icon><CopyDocument /></el-icon>
            复制
          </el-button>
        </div>
      </div>
    </div>

    <!-- 策略统计卡片 -->
    <div class="strategy-stats">
      <div class="stats-grid">
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><TrendCharts /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatPercent(strategy.totalReturn) }}</div>
            <div class="stat-label">总收益率</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><DataAnalysis /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatPercent(strategy.annualReturn) }}</div>
            <div class="stat-label">年化收益</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Warning /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ formatPercent(strategy.maxDrawdown) }}</div>
            <div class="stat-label">最大回撤</div>
          </div>
        </div>
        
        <div class="stat-card">
          <div class="stat-icon">
            <el-icon><Odometer /></el-icon>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ strategy.sharpeRatio.toFixed(2) }}</div>
            <div class="stat-label">夏普比率</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="strategy-content">
      <el-tabs v-model="activeTab" type="card">
        <!-- 策略概览 -->
        <el-tab-pane label="策略概览" name="overview">
          <div class="overview-content">
            <div class="overview-grid">
              <div class="overview-section">
                <h3>策略逻辑</h3>
                <div class="logic-content">
                  <p>{{ strategy.logic || '暂无策略逻辑说明' }}</p>
                </div>
              </div>
              
              <div class="overview-section">
                <h3>适用市场</h3>
                <div class="market-tags">
                  <el-tag v-for="market in strategy.markets" :key="market" class="market-tag">
                    {{ market }}
                  </el-tag>
                </div>
              </div>
              
              <div class="overview-section">
                <h3>风险等级</h3>
                <div class="risk-level">
                  <el-rate v-model="strategy.riskLevel" disabled show-score />
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>

        <!-- 回测结果 -->
        <el-tab-pane label="回测结果" name="backtest">
          <div class="backtest-content">
            <div class="backtest-chart">
              <h3>收益曲线</h3>
              <div class="chart-placeholder">
                <el-icon class="chart-icon"><TrendCharts /></el-icon>
                <p>收益曲线图表</p>
                <p class="chart-note">这里将显示策略的历史收益曲线</p>
              </div>
            </div>
            
            <div class="backtest-metrics">
              <h3>详细指标</h3>
              <el-table :data="backtestMetrics" stripe>
                <el-table-column prop="metric" label="指标" width="200" />
                <el-table-column prop="value" label="数值" />
                <el-table-column prop="description" label="说明" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 持仓分析 -->
        <el-tab-pane label="持仓分析" name="positions">
          <div class="positions-content">
            <div class="positions-summary">
              <h3>当前持仓</h3>
              <el-table :data="currentPositions" stripe>
                <el-table-column prop="symbol" label="股票代码" width="120" />
                <el-table-column prop="name" label="股票名称" width="150" />
                <el-table-column prop="weight" label="权重" width="100">
                  <template #default="{ row }">
                    {{ formatPercent(row.weight) }}
                  </template>
                </el-table-column>
                <el-table-column prop="return" label="收益率" width="120">
                  <template #default="{ row }">
                    <span :class="row.return >= 0 ? 'profit' : 'loss'">
                      {{ formatPercent(row.return) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column prop="lastUpdate" label="最后更新" />
              </el-table>
            </div>
          </div>
        </el-tab-pane>

        <!-- 评论讨论 -->
        <el-tab-pane label="评论讨论" name="comments">
          <div class="comments-content">
            <div class="comment-form">
              <h3>发表评论</h3>
              <el-input
                v-model="newComment"
                type="textarea"
                :rows="4"
                placeholder="请输入您的评论..."
              />
              <div class="comment-actions">
                <el-button type="primary" @click="submitComment">发表评论</el-button>
              </div>
            </div>
            
            <div class="comments-list">
              <h3>用户评论 ({{ comments.length }})</h3>
              <div v-for="comment in comments" :key="comment.id" class="comment-item">
                <div class="comment-header">
                  <span class="comment-author">{{ comment.author }}</span>
                  <span class="comment-date">{{ formatDate(comment.date) }}</span>
                </div>
                <div class="comment-content">{{ comment.content }}</div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { 
  Star, TrendCharts, CopyDocument, DataAnalysis, 
  Warning, Odometer 
} from '@element-plus/icons-vue'

// 路由参数
const route = useRoute()
const strategyId = computed(() => route.params.id as string)

// 响应式数据
const activeTab = ref('overview')
const newComment = ref('')

// 策略数据
const strategy = reactive({
  id: '',
  name: '智能量化策略',
  author: '量化专家',
  status: 'active',
  description: '基于机器学习的智能量化交易策略，通过多因子模型筛选优质股票，实现稳健收益。',
  createTime: '2024-01-15',
  totalReturn: 0.2845,
  annualReturn: 0.1892,
  maxDrawdown: -0.0756,
  sharpeRatio: 1.67,
  logic: '该策略采用多因子选股模型，结合技术指标和基本面分析，通过机器学习算法优化投资组合配置。主要包括：1. 基本面筛选 2. 技术指标确认 3. 风险控制 4. 动态调仓',
  markets: ['A股', '创业板', '科创板'],
  riskLevel: 3
})

// 回测指标
const backtestMetrics = ref([
  { metric: '总收益率', value: '28.45%', description: '策略总体收益表现' },
  { metric: '年化收益率', value: '18.92%', description: '年化收益水平' },
  { metric: '最大回撤', value: '-7.56%', description: '最大亏损幅度' },
  { metric: '夏普比率', value: '1.67', description: '风险调整后收益' },
  { metric: '胜率', value: '68.5%', description: '盈利交易占比' },
  { metric: '盈亏比', value: '1.85', description: '平均盈利/平均亏损' }
])

// 当前持仓
const currentPositions = ref([
  { symbol: '000001', name: '平安银行', weight: 0.15, return: 0.0823, lastUpdate: '2024-01-20' },
  { symbol: '600036', name: '招商银行', weight: 0.12, return: 0.0654, lastUpdate: '2024-01-20' },
  { symbol: '000002', name: '万科A', weight: 0.10, return: -0.0234, lastUpdate: '2024-01-20' },
  { symbol: '600519', name: '贵州茅台', weight: 0.08, return: 0.1245, lastUpdate: '2024-01-20' }
])

// 评论数据
const comments = ref([
  {
    id: 1,
    author: '投资者A',
    content: '这个策略的回测效果不错，夏普比率很高，值得关注。',
    date: '2024-01-18'
  },
  {
    id: 2,
    author: '量化爱好者',
    content: '多因子模型确实是个好思路，不过需要注意市场环境变化的影响。',
    date: '2024-01-17'
  }
])

// 方法
const formatPercent = (value: number) => {
  return (value * 100).toFixed(2) + '%'
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString('zh-CN')
}

const getStrategyStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    active: 'success',
    inactive: 'info',
    testing: 'warning',
    error: 'danger'
  }
  return statusMap[status] || 'default'
}

const getStrategyStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    active: '运行中',
    inactive: '已停止',
    testing: '测试中',
    error: '异常'
  }
  return statusMap[status] || status
}

const subscribeStrategy = () => {
  ElMessage.success('策略订阅成功！')
}

const backTestStrategy = () => {
  ElMessage.info('正在启动回测...')
}

const copyStrategy = () => {
  ElMessage.success('策略已复制到您的策略库！')
}

const submitComment = () => {
  if (!newComment.value.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }
  
  comments.value.unshift({
    id: Date.now(),
    author: '当前用户',
    content: newComment.value,
    date: new Date().toISOString().split('T')[0]
  })
  
  newComment.value = ''
  ElMessage.success('评论发表成功！')
}

// 生命周期
onMounted(() => {
  // 根据路由参数加载策略数据
  strategy.id = strategyId.value
  
  // 模拟加载不同的策略数据
  if (strategyId.value === 'mock-1') {
    strategy.name = '智能量化策略 Alpha'
    strategy.description = '基于机器学习的多因子选股策略，专注于A股市场的价值投资。'
  } else if (strategyId.value === 'mock-2') {
    strategy.name = '动量交易策略 Beta'
    strategy.description = '利用技术指标捕捉市场动量，适合短期交易的量化策略。'
    strategy.totalReturn = 0.3567
    strategy.annualReturn = 0.2234
  }
  
  console.log(`加载策略详情: ${strategyId.value}`)
})
</script>

<style scoped lang="scss">
.strategy-detail {
  padding: 24px;
  background: #f5f7fa;
  min-height: 100vh;
}

.strategy-header {
  background: white;
  border-radius: 12px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  
  .header-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    
    .strategy-info {
      flex: 1;
      
      .strategy-title {
        font-size: 32px;
        font-weight: 700;
        color: #303133;
        margin: 0 0 16px 0;
      }
      
      .strategy-meta {
        display: flex;
        gap: 16px;
        align-items: center;
        margin-bottom: 16px;
        
        .strategy-author,
        .strategy-date {
          color: #606266;
          font-size: 14px;
        }
      }
      
      .strategy-description {
        color: #606266;
        line-height: 1.6;
        margin: 0;
        max-width: 600px;
      }
    }
    
    .strategy-actions {
      display: flex;
      gap: 12px;
      flex-shrink: 0;
    }
  }
}

.strategy-stats {
  margin-bottom: 24px;
  
  .stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    
    .stat-card {
      background: white;
      border-radius: 12px;
      padding: 24px;
      display: flex;
      align-items: center;
      gap: 16px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      .stat-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 24px;
      }
      
      .stat-content {
        .stat-value {
          font-size: 24px;
          font-weight: 700;
          color: #303133;
          margin-bottom: 4px;
        }
        
        .stat-label {
          color: #909399;
          font-size: 14px;
        }
      }
    }
  }
}

.strategy-content {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  
  .overview-content {
    padding: 24px;
    
    .overview-grid {
      display: grid;
      gap: 32px;
      
      .overview-section {
        h3 {
          color: #303133;
          margin: 0 0 16px 0;
          font-size: 18px;
          font-weight: 600;
        }
        
        .logic-content {
          color: #606266;
          line-height: 1.6;
        }
        
        .market-tags {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
          
          .market-tag {
            margin: 0;
          }
        }
        
        .risk-level {
          display: flex;
          align-items: center;
        }
      }
    }
  }
  
  .backtest-content {
    padding: 24px;
    
    .backtest-chart {
      margin-bottom: 32px;
      
      h3 {
        color: #303133;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .chart-placeholder {
        height: 300px;
        border: 2px dashed #dcdfe6;
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        color: #909399;
        
        .chart-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }
        
        .chart-note {
          font-size: 14px;
          margin: 8px 0 0 0;
        }
      }
    }
    
    .backtest-metrics {
      h3 {
        color: #303133;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
  
  .positions-content {
    padding: 24px;
    
    .positions-summary {
      h3 {
        color: #303133;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .profit {
        color: #f56c6c;
      }
      
      .loss {
        color: #67c23a;
      }
    }
  }
  
  .comments-content {
    padding: 24px;
    
    .comment-form {
      margin-bottom: 32px;
      
      h3 {
        color: #303133;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .comment-actions {
        margin-top: 12px;
        text-align: right;
      }
    }
    
    .comments-list {
      h3 {
        color: #303133;
        margin: 0 0 16px 0;
        font-size: 18px;
        font-weight: 600;
      }
      
      .comment-item {
        border-bottom: 1px solid #f0f0f0;
        padding: 16px 0;
        
        &:last-child {
          border-bottom: none;
        }
        
        .comment-header {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
          
          .comment-author {
            font-weight: 600;
            color: #303133;
          }
          
          .comment-date {
            color: #909399;
            font-size: 14px;
          }
        }
        
        .comment-content {
          color: #606266;
          line-height: 1.6;
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .strategy-detail {
    padding: 16px;
  }
  
  .strategy-header {
    padding: 20px;
    
    .header-content {
      flex-direction: column;
      gap: 20px;
      
      .strategy-actions {
        align-self: stretch;
        
        .el-button {
          flex: 1;
        }
      }
    }
  }
  
  .stats-grid {
    grid-template-columns: 1fr !important;
  }
}
</style>
