# WebSocket统一配置示例
# 
# 本文件展示了所有WebSocket相关的环境变量配置
# 在生产环境中，请根据实际情况修改这些值

# ========================================
# WebSocket环境变量说明
# ========================================

# 1. 通用WebSocket连接
#    用于：系统通知、用户消息、全局事件等
VITE_WS_URL=ws://localhost:8000/api/v1/ws

# 2. 市场数据WebSocket（可选）
#    用于：实时行情、K线数据、深度数据等
#    如不设置，将使用VITE_WS_URL的值
# VITE_WS_MARKET_URL=ws://localhost:8000/api/v1/ws/market

# 3. 交易数据WebSocket（可选）
#    用于：订单状态、成交回报、持仓变化等
#    如不设置，将使用VITE_WS_URL的值
# VITE_WS_TRADING_URL=ws://localhost:8000/api/v1/ws/trading

# 4. 策略监控WebSocket（可选）
#    用于：策略运行状态、信号推送、风控预警等
#    如不设置，将使用VITE_WS_URL的值
# VITE_WS_STRATEGY_URL=ws://localhost:8000/api/v1/ws/strategy

# ========================================
# 生产环境配置示例
# ========================================

# 生产环境 - 使用wss协议和域名
# VITE_WS_URL=wss://api.your-domain.com/api/v1/ws
# VITE_WS_MARKET_URL=wss://api.your-domain.com/api/v1/ws/market
# VITE_WS_TRADING_URL=wss://api.your-domain.com/api/v1/ws/trading
# VITE_WS_STRATEGY_URL=wss://api.your-domain.com/api/v1/ws/strategy

# ========================================
# 开发环境配置示例
# ========================================

# 开发环境 - 本地服务
# VITE_WS_URL=ws://localhost:8000/api/v1/ws
# VITE_WS_MARKET_URL=ws://localhost:8000/api/v1/ws/market
# VITE_WS_TRADING_URL=ws://localhost:8000/api/v1/ws/trading
# VITE_WS_STRATEGY_URL=ws://localhost:8000/api/v1/ws/strategy

# ========================================
# 注意事项
# ========================================
# 1. 生产环境必须使用wss://协议确保安全
# 2. 开发环境可以使用ws://协议
# 3. 如果所有WebSocket服务在同一服务器，只需配置VITE_WS_URL
# 4. 如果不同类型的WebSocket服务分布在不同服务器，请分别配置