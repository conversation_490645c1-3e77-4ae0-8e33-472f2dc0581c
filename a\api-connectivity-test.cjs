/**
 * API连接测试脚本
 * 测试前端是否能正常连接到后端API
 */

const http = require('http');
const https = require('https');

class ApiTester {
  constructor() {
    this.baseUrl = 'http://127.0.0.1:8000';
    this.frontendUrl = 'http://127.0.0.1:5173';
    this.testResults = {
      timestamp: new Date().toISOString(),
      frontend: { accessible: false, error: null },
      backend: { accessible: false, error: null },
      apiEndpoints: []
    };
  }

  async testUrl(url) {
    return new Promise((resolve) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;

      const req = client.get(url, (res) => {
        let data = '';
        res.on('data', chunk => data += chunk);
        res.on('end', () => {
          resolve({
            success: true,
            statusCode: res.statusCode,
            headers: res.headers,
            data: data.substring(0, 500) // 只保留前500字符
          });
        });
      });

      req.on('error', (error) => {
        resolve({
          success: false,
          error: error.message
        });
      });

      req.setTimeout(5000, () => {
        req.destroy();
        resolve({
          success: false,
          error: 'Request timeout'
        });
      });
    });
  }

  async testFrontend() {
    console.log('🌐 测试前端服务...');
    const result = await this.testUrl(this.frontendUrl);

    this.testResults.frontend = {
      accessible: result.success && result.statusCode === 200,
      statusCode: result.statusCode,
      error: result.error
    };

    if (result.success) {
      console.log(`✅ 前端服务正常 (状态码: ${result.statusCode})`);
    } else {
      console.log(`❌ 前端服务异常: ${result.error}`);
    }

    return result;
  }

  async testBackend() {
    console.log('🔧 测试后端服务...');
    const result = await this.testUrl(this.baseUrl);

    this.testResults.backend = {
      accessible: result.success && result.statusCode === 200,
      statusCode: result.statusCode,
      error: result.error
    };

    if (result.success) {
      console.log(`✅ 后端服务正常 (状态码: ${result.statusCode})`);
    } else {
      console.log(`❌ 后端服务异常: ${result.error}`);
    }

    return result;
  }

  async testApiEndpoints() {
    console.log('📡 测试API端点...');

    const endpoints = [
      '/api/v1/health',
      '/api/v1/market/quotes',
      '/api/v1/auth/login',
      '/api/v1/backtest',
      '/api/v1/trading/positions',
      '/api/v1/strategy/list'
    ];

    for (const endpoint of endpoints) {
      const url = this.baseUrl + endpoint;
      console.log(`测试: ${endpoint}`);

      const result = await this.testUrl(url);

      this.testResults.apiEndpoints.push({
        endpoint,
        success: result.success,
        statusCode: result.statusCode,
        error: result.error
      });

      if (result.success) {
        console.log(`  ✅ ${endpoint} (${result.statusCode})`);
      } else {
        console.log(`  ❌ ${endpoint} - ${result.error}`);
      }
    }
  }

  async runTests() {
    console.log('🧪 开始API连接测试...');
    console.log('='.repeat(50));

    await this.testFrontend();
    await this.testBackend();
    await this.testApiEndpoints();

    this.generateReport();
  }

  generateReport() {
    console.log('\n📋 测试报告');
    console.log('='.repeat(50));

    console.log(`🌐 前端服务: ${this.testResults.frontend.accessible ? '✅ 正常' : '❌ 异常'}`);
    console.log(`🔧 后端服务: ${this.testResults.backend.accessible ? '✅ 正常' : '❌ 异常'}`);

    const successfulEndpoints = this.testResults.apiEndpoints.filter(ep => ep.success).length;
    const totalEndpoints = this.testResults.apiEndpoints.length;

    console.log(`📡 API端点: ${successfulEndpoints}/${totalEndpoints} 可用`);

    if (!this.testResults.frontend.accessible) {
      console.log(`\n❌ 前端问题: ${this.testResults.frontend.error}`);
    }

    if (!this.testResults.backend.accessible) {
      console.log(`\n❌ 后端问题: ${this.testResults.backend.error}`);
      console.log('💡 建议: 请确保后端服务已启动在端口8000');
    }

    // 保存详细报告
    const reportPath = `api-test-report-${Date.now()}.json`;
    require('fs').writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);

    // 分析问题
    this.analyzeIssues();
  }

  analyzeIssues() {
    console.log('\n🔍 问题分析');
    console.log('='.repeat(50));

    if (!this.testResults.frontend.accessible) {
      console.log('🔴 前端服务问题:');
      console.log('  - 检查npm run dev是否正常运行');
      console.log('  - 检查端口5173是否被占用');
      console.log('  - 检查防火墙设置');
    }

    if (!this.testResults.backend.accessible) {
      console.log('🔴 后端服务问题:');
      console.log('  - 后端API服务未启动');
      console.log('  - 这解释了为什么前端页面显示"组件正在维护中"');
      console.log('  - 前端依赖后端数据，无后端则功能不可用');
    }

    const failedEndpoints = this.testResults.apiEndpoints.filter(ep => !ep.success);
    if (failedEndpoints.length > 0) {
      console.log('🔴 API端点问题:');
      failedEndpoints.forEach(ep => {
        console.log(`  - ${ep.endpoint}: ${ep.error}`);
      });
    }

    console.log('\n💡 解决方案建议:');
    console.log('1. 启动后端API服务 (通常是FastAPI或类似框架)');
    console.log('2. 确保后端运行在端口8000');
    console.log('3. 检查前端的API配置是否正确');
    console.log('4. 验证CORS设置是否允许前端访问');
  }
}

// 运行测试
const tester = new ApiTester();
tester.runTests().catch(console.error);
