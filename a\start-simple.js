import { spawn } from 'child_process';
import path from 'path';
import { fileURLToPath } from 'url';

const __dirname = path.dirname(fileURLToPath(import.meta.url));

console.log('🚀 启动简化版开发服务器...');

// 启动vite开发服务器
const vite = spawn('npx', ['vite', '--port', '5173', '--host', '0.0.0.0'], {
  cwd: path.resolve(__dirname),
  stdio: 'inherit',
  shell: true
});

vite.on('error', (error) => {
  console.error('❌ 启动失败:', error);
});

vite.on('close', (code) => {
  console.log(`🔄 服务器退出，代码: ${code}`);
});

// 处理进程退出
process.on('SIGINT', () => {
  console.log('\n🛑 正在关闭服务器...');
  vite.kill();
  process.exit(0);
});
