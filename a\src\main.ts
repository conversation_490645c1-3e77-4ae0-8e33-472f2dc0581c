/**
 * 量化投资平台主入口文件
 *
 * @description 应用程序的主入口点，负责初始化Vue应用和相关插件
 * <AUTHOR>
 * @version 1.0.0
 */

import { createApp } from 'vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

// 主应用组件
import App from './App.vue'

// 路由
import router from './router'
import { setupRouterGuards } from './router/guards'

// 样式
import 'element-plus/dist/index.css'
import 'element-plus/theme-chalk/dark/css-vars.css'
import './assets/main.css'

// 插件
import { setupElementPlus } from './plugins/element-plus'
import { setupI18n } from './plugins/i18n'

// 错误处理
import { setupErrorHandler } from './utils/error-handler'

// 性能监控
import { setupPerformanceMonitor } from './utils/performance'

/**
 * 创建Vue应用实例
 */
async function createQuantApp() {
  const app = createApp(App)

  // 设置Pinia状态管理
  const pinia = createPinia()
  pinia.use(piniaPluginPersistedstate)
  app.use(pinia)

  // 设置路由
  app.use(router)

  // 设置路由守卫
  setupRouterGuards(router)

  // 设置Element Plus
  setupElementPlus(app)

  // 设置国际化
  const i18n = await setupI18n()
  app.use(i18n)

  // 设置全局错误处理
  setupErrorHandler(app)

  // 设置性能监控
  if (import.meta.env.PROD) {
    setupPerformanceMonitor()
  }

  // 设置开发环境的额外错误处理
  if (import.meta.env.DEV) {
    // 捕获Vue警告
    app.config.warnHandler = (msg, instance, trace) => {
      console.warn(`[Vue warn]: ${msg}`, { instance, trace })
    }
  }

  return app
}

/**
 * 启动应用
 */
async function bootstrap() {
  try {
    console.log('🚀 正在启动量化投资平台...')

    const app = await createQuantApp()

    // 挂载应用
    app.mount('#app')

    console.log('✅ 量化投资平台启动成功')

    // 移除加载动画
    const loading = document.querySelector('.app-loading')
    if (loading) {
      loading.style.opacity = '0'
      setTimeout(() => loading.remove(), 300)
    }

    // 开发环境下的调试信息
    if (import.meta.env.DEV) {
      console.log('📊 开发模式已启用')
      console.log('🔧 API地址:', import.meta.env.VITE_API_BASE_URL)
      console.log('🌐 WebSocket地址:', import.meta.env.VITE_WS_BASE_URL)
    }

  } catch (error) {
    console.error('❌ 应用启动失败:', error)

    // 显示友好的错误页面
    const errorDetails = import.meta.env.DEV ? `<details style="margin-top: 1rem; text-align: left;">
      <summary style="cursor: pointer; color: #6b7280;">错误详情 (开发模式)</summary>
      <pre style="background: #f3f4f6; padding: 1rem; border-radius: 4px; overflow: auto; font-size: 0.8rem; margin-top: 0.5rem;">${error}</pre>
    </details>` : ''

    document.getElementById('app')!.innerHTML = `
      <div style="
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        padding: 2rem;
        text-align: center;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      ">
        <div style="
          background: white;
          padding: 3rem;
          border-radius: 16px;
          box-shadow: 0 20px 40px rgba(0,0,0,0.1);
          max-width: 600px;
        ">
          <div style="font-size: 4rem; margin-bottom: 1rem;">⚠️</div>
          <h1 style="color: #dc2626; margin-bottom: 1rem;">应用启动失败</h1>
          <p style="color: #6b7280; margin-bottom: 2rem;">
            抱歉，量化投资平台无法正常启动。这可能是由于网络连接问题或配置错误导致的。
          </p>
          <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <button
              onclick="window.location.reload()"
              style="
                padding: 12px 24px;
                background: #3b82f6;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 1rem;
              "
            >
              🔄 重新加载
            </button>
            <button
              onclick="localStorage.clear(); sessionStorage.clear(); window.location.reload()"
              style="
                padding: 12px 24px;
                background: #6b7280;
                color: white;
                border: none;
                border-radius: 8px;
                cursor: pointer;
                font-size: 1rem;
              "
            >
              🗑️ 清除缓存并重载
            </button>
          </div>
          ${errorDetails}
        </div>
      </div>
    `
  }
}

// 启动应用
bootstrap()
