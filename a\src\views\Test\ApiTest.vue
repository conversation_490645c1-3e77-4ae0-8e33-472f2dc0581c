<template>
  <div class="api-test">
    <h1>API 测试页面</h1>
    
    <div class="test-section">
      <h2>市场概览测试</h2>
      <el-button @click="testMarketOverview" :loading="loading">测试市场概览API</el-button>
      
      <div v-if="result" class="result">
        <h3>结果:</h3>
        <pre>{{ JSON.stringify(result, null, 2) }}</pre>
      </div>
      
      <div v-if="error" class="error">
        <h3>错误:</h3>
        <pre>{{ error }}</pre>
      </div>
    </div>
    
    <div class="test-section">
      <h2>直接API调用测试</h2>
      <el-button @click="testDirectApi" :loading="loading2">直接调用后端API</el-button>
      
      <div v-if="result2" class="result">
        <h3>结果:</h3>
        <pre>{{ JSON.stringify(result2, null, 2) }}</pre>
      </div>
      
      <div v-if="error2" class="error">
        <h3>错误:</h3>
        <pre>{{ error2 }}</pre>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import axios from 'axios'

const loading = ref(false)
const loading2 = ref(false)
const result = ref(null)
const result2 = ref(null)
const error = ref('')
const error2 = ref('')

const testMarketOverview = async () => {
  loading.value = true
  error.value = ''
  result.value = null
  
  try {
    // 使用前端API客户端
    const response = await axios.get('/market/overview')
    result.value = response.data
  } catch (err: any) {
    error.value = err.message || '请求失败'
    console.error('API测试失败:', err)
  } finally {
    loading.value = false
  }
}

const testDirectApi = async () => {
  loading2.value = true
  error2.value = ''
  result2.value = null
  
  try {
    // 直接调用后端API
    const response = await axios.get('http://localhost:8000/api/v1/market/overview')
    result2.value = response.data
  } catch (err: any) {
    error2.value = err.message || '请求失败'
    console.error('直接API调用失败:', err)
  } finally {
    loading2.value = false
  }
}
</script>

<style scoped>
.api-test {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

.test-section {
  margin-bottom: 30px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
}

.result {
  margin-top: 20px;
  padding: 10px;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 4px;
}

.error {
  margin-top: 20px;
  padding: 10px;
  background: #fef2f2;
  border: 1px solid #ef4444;
  border-radius: 4px;
  color: #dc2626;
}

pre {
  white-space: pre-wrap;
  word-wrap: break-word;
}
</style> 