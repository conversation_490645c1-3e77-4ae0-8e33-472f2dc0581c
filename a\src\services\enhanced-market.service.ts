/**
 * 增强版市场数据服务
 * 集成真实数据源，提供缓存和实时更新功能
 */

import { ref, reactive } from 'vue'
import { httpClient } from '@/api/http'
import type { StockInfo, KLineData } from '@/types/market'
import { CacheService } from '@/services/cache.service'
import { marketWebSocket } from '@/services/websocket-manager'

// 创建专用缓存实例
const marketCache = new CacheService({
  maxSize: 500,
  ttl: 30 * 1000, // 30秒缓存
  storage: 'memory',
  enableStats: true
})

const klineCache = new CacheService({
  maxSize: 200,
  ttl: 300 * 1000, // 5分钟缓存
  storage: 'memory',
  enableStats: true
})

// 配置
const USE_REAL_DATA = import.meta.env.VITE_USE_REAL_DATA === 'true'

export class EnhancedMarketService {
  private subscribers = new Set<string>()
  private wsManager = marketWebSocket

  /**
   * 获取股票实时信息（带缓存）
   */
  async getStockInfo(symbol: string): Promise<StockInfo> {
    const cacheKey = `stock_info_${symbol}`

    // 尝试从缓存获取
    const cached = marketCache.get<StockInfo>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      let stockInfo: StockInfo

      if (USE_REAL_DATA) {
        // 使用真实数据源（AKShare/Tushare）
        stockInfo = await this.fetchRealStockInfo(symbol)
      } else {
        // 使用模拟数据
        stockInfo = this.generateMockStockInfo(symbol)
      }

      // 更新缓存
      marketCache.set(cacheKey, stockInfo)

      return stockInfo
    } catch (error) {
      console.error('获取股票信息失败:', error)
      // 降级到模拟数据
      const mockData = this.generateMockStockInfo(symbol)
      marketCache.set(cacheKey, mockData, 10 * 1000) // 错误数据缓存时间更短
      return mockData
    }
  }

  /**
   * 获取K线数据（带缓存）
   */
  async getKLineData(symbol: string, period: string = '1d', limit: number = 100): Promise<KLineData[]> {
    const cacheKey = `kline_${symbol}_${period}_${limit}`

    // 尝试从缓存获取
    const cached = klineCache.get<KLineData[]>(cacheKey)
    if (cached) {
      return cached
    }

    try {
      let klineData: KLineData[]

      if (USE_REAL_DATA) {
        // 使用真实数据源
        klineData = await this.fetchRealKLineData(symbol, period, limit)
      } else {
        // 使用模拟数据
        klineData = this.generateMockKLineData(symbol, period, limit)
      }

      // 更新缓存
      klineCache.set(cacheKey, klineData)

      return klineData
    } catch (error) {
      console.error('获取K线数据失败:', error)
      // 降级到模拟数据
      const mockData = this.generateMockKLineData(symbol, period, limit)
      klineCache.set(cacheKey, mockData, 60 * 1000) // 错误数据缓存时间更短
      return mockData
    }
  }

  /**
   * 获取真实股票信息
   */
  private async fetchRealStockInfo(symbol: string): Promise<StockInfo> {
    // 这里集成真实的数据源API
    // 例如：AKShare、Tushare、新浪财经等

    try {
      // 示例：调用后端API获取真实数据
      const response = await httpClient.get(`/api/v1/market/real/stock/${symbol}`)
      return response.data
    } catch (error) {
      // 如果真实API失败，可以尝试其他数据源
      console.warn('主要数据源失败，尝试备用数据源')
      return await this.fetchBackupStockInfo(symbol)
    }
  }

  /**
   * 备用数据源
   */
  private async fetchBackupStockInfo(symbol: string): Promise<StockInfo> {
    // 可以集成多个数据源作为备用
    try {
      const response = await httpClient.get(`/api/v1/market/backup/stock/${symbol}`)
      return response.data
    } catch (error) {
      throw new Error('所有数据源都不可用')
    }
  }

  /**
   * 获取真实K线数据
   */
  private async fetchRealKLineData(symbol: string, period: string, limit: number): Promise<KLineData[]> {
    try {
      const response = await httpClient.get(`/api/v1/market/real/kline/${symbol}`, {
        params: { period, limit }
      })
      return response.data
    } catch (error) {
      console.warn('真实K线数据获取失败，使用备用数据源')
      return await this.fetchBackupKLineData(symbol, period, limit)
    }
  }

  /**
   * 备用K线数据
   */
  private async fetchBackupKLineData(symbol: string, period: string, limit: number): Promise<KLineData[]> {
    try {
      const response = await httpClient.get(`/api/v1/market/backup/kline/${symbol}`, {
        params: { period, limit }
      })
      return response.data
    } catch (error) {
      throw new Error('K线数据获取失败')
    }
  }

  /**
   * 生成模拟股票信息
   */
  private generateMockStockInfo(symbol: string): StockInfo {
    const basePrice = 10 + Math.random() * 50
    const change = (Math.random() - 0.5) * 4
    const changePercent = (change / basePrice) * 100

    return {
      symbol,
      name: `股票${symbol}`,
      currentPrice: Number((basePrice + change).toFixed(2)),
      change: Number(change.toFixed(2)),
      changePercent: Number(changePercent.toFixed(2)),
      openPrice: Number(basePrice.toFixed(2)),
      highPrice: Number((basePrice + Math.abs(change) + Math.random() * 2).toFixed(2)),
      lowPrice: Number((basePrice - Math.abs(change) - Math.random() * 2).toFixed(2)),
      prevClose: Number(basePrice.toFixed(2)),
      volume: Math.floor(Math.random() * 10000000),
      amount: Math.floor(Math.random() * 1000000000),
      turnoverRate: Number((Math.random() * 10).toFixed(2)),
      pe: Number((15 + Math.random() * 30).toFixed(2)),
      updateTime: new Date().toISOString()
    }
  }

  /**
   * 生成模拟K线数据
   */
  private generateMockKLineData(symbol: string, period: string, limit: number): KLineData[] {
    const data: KLineData[] = []
    const basePrice = 10 + Math.random() * 50
    let currentPrice = basePrice

    for (let i = 0; i < limit; i++) {
      const date = new Date()

      // 根据周期调整时间
      switch (period) {
        case '1m':
          date.setMinutes(date.getMinutes() - (limit - i))
          break
        case '5m':
          date.setMinutes(date.getMinutes() - (limit - i) * 5)
          break
        case '15m':
          date.setMinutes(date.getMinutes() - (limit - i) * 15)
          break
        case '30m':
          date.setMinutes(date.getMinutes() - (limit - i) * 30)
          break
        case '1h':
          date.setHours(date.getHours() - (limit - i))
          break
        case '1d':
          date.setDate(date.getDate() - (limit - i))
          break
        case '1w':
          date.setDate(date.getDate() - (limit - i) * 7)
          break
        case '1M':
          date.setMonth(date.getMonth() - (limit - i))
          break
      }

      const open = currentPrice
      const change = (Math.random() - 0.5) * 2
      const close = open + change
      const high = Math.max(open, close) + Math.random() * 1
      const low = Math.min(open, close) - Math.random() * 1
      const volume = Math.floor(Math.random() * 1000000)

      data.push({
        timestamp: date.getTime(),
        open: Number(open.toFixed(2)),
        high: Number(high.toFixed(2)),
        low: Number(low.toFixed(2)),
        close: Number(close.toFixed(2)),
        volume
      })

      currentPrice = close
    }

    return data.sort((a, b) => a.timestamp - b.timestamp)
  }

  /**
   * 清除缓存
   */
  clearCache(pattern?: string): void {
    if (pattern) {
      // 清除匹配模式的缓存
      const marketKeys = marketCache.getKeys().filter(key => key.includes(pattern))
      const klineKeys = klineCache.getKeys().filter(key => key.includes(pattern))

      marketKeys.forEach(key => marketCache.delete(key))
      klineKeys.forEach(key => klineCache.delete(key))
    } else {
      // 清除所有缓存
      marketCache.clear()
      klineCache.clear()
    }
  }

  /**
   * 获取缓存统计信息
   */
  getCacheStats() {
    return {
      market: marketCache.getStats(),
      kline: klineCache.getStats()
    }
  }

  /**
   * 初始化WebSocket连接
   */
  initWebSocket(): void {
    // 订阅WebSocket事件
    this.wsManager.subscribe('connected', () => {
      console.log('市场数据WebSocket连接已建立')
      this.resubscribeAll()
    })

    this.wsManager.subscribe('message', (data: any) => {
      this.handleWebSocketMessage(data)
    })

    this.wsManager.subscribe('disconnected', () => {
      console.log('市场数据WebSocket连接已断开')
    })

    this.wsManager.subscribe('error', (error: any) => {
      console.error('市场数据WebSocket错误:', error)
    })

    // 如果未连接，尝试连接
    if (this.wsManager.getStatus() !== 'connected') {
      this.wsManager.connect().catch(error => {
        console.warn('WebSocket连接失败:', error)
      })
    }
  }

  /**
   * 订阅股票实时数据
   */
  subscribe(symbol: string): void {
    this.subscribers.add(symbol)

    if (this.wsManager.getStatus() === 'connected') {
      this.wsManager.send({
        type: 'subscribe',
        symbol
      })
    }
  }

  /**
   * 取消订阅
   */
  unsubscribe(symbol: string): void {
    this.subscribers.delete(symbol)

    if (this.wsManager.getStatus() === 'connected') {
      this.wsManager.send({
        type: 'unsubscribe',
        symbol
      })
    }
  }

  /**
   * 处理WebSocket消息
   */
  private handleWebSocketMessage(data: any): void {
    if (data.type === 'quote' && data.symbol) {
      // 更新实时行情缓存
      const cacheKey = `stock_info_${data.symbol}`
      marketCache.set(cacheKey, data.data)

      // 触发事件通知组件更新
      window.dispatchEvent(new CustomEvent('market-data-update', {
        detail: { symbol: data.symbol, data: data.data }
      }))
    }
  }

  /**
   * 重新订阅所有股票
   */
  private resubscribeAll(): void {
    for (const symbol of this.subscribers) {
      this.wsManager.send({
        type: 'subscribe',
        symbol
      })
    }
  }

  /**
   * 关闭连接
   */
  disconnect(): void {
    this.wsManager.disconnect()
    this.subscribers.clear()
  }

  /**
   * 获取WebSocket连接状态
   */
  getConnectionStatus() {
    return this.wsManager.getStats()
  }
}

// 创建单例实例
export const enhancedMarketService = new EnhancedMarketService()

// 自动初始化WebSocket连接
if (typeof window !== 'undefined') {
  enhancedMarketService.initWebSocket()
}
