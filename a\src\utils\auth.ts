/**
 * 认证相关工具函数 - 安全增强版
 */
import { STORAGE_KEYS } from './constants'
import { jwtDecode } from 'jwt-decode'

interface TokenPayload {
  userId: string
  username: string
  email: string
  exp: number
  iat: number
}

// 安全策略：内存存储访问令牌，HttpOnly Cookie存储刷新令牌
let memoryAccessToken: string | null = null
let memoryRefreshToken: string | null = null
let tokenRefreshPromise: Promise<void> | null = null

/**
 * 获取存储的Token - 优先从内存获取
 */
export const getToken = (): string | null => {
  // 优先从内存获取
  if (memoryAccessToken) {
    return memoryAccessToken
  }
  
  // 降级到localStorage（向后兼容）
  return localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
}

/**
 * 设置Token - 安全存储策略
 */
export const setToken = (token: string): void => {
  // 存储到内存中
  memoryAccessToken = token
  
  // 可选：存储到localStorage作为备份（加密）
  const encryptedToken = encryptSensitiveData(token)
  localStorage.setItem(STORAGE_KEYS.AUTH_TOKEN, encryptedToken)
}

/**
 * 移除Token - 清理所有存储
 */
export const removeToken = (): void => {
  // 清理内存
  memoryAccessToken = null
  memoryRefreshToken = null
  
  // 清理localStorage
  localStorage.removeItem(STORAGE_KEYS.AUTH_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.REFRESH_TOKEN)
  localStorage.removeItem(STORAGE_KEYS.USER_INFO)
  
  // 清理HttpOnly Cookie（通过API调用）
  clearHttpOnlyCookie()
}

/**
 * 获取刷新Token - 优先从内存获取
 */
export const getRefreshToken = (): string | null => {
  // 优先从内存获取
  if (memoryRefreshToken) {
    return memoryRefreshToken
  }
  
  // 尝试从localStorage获取（向后兼容）
  const storedToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  if (storedToken) {
    return decryptSensitiveData(storedToken)
  }
  
  return null
}

/**
 * 设置刷新Token - 安全存储策略
 */
export const setRefreshToken = (token: string): void => {
  // 存储到内存中
  memoryRefreshToken = token
  
  // 发送到后端设置HttpOnly Cookie
  setHttpOnlyCookie(token)
  
  // 可选：加密存储到localStorage作为备份
  const encryptedToken = encryptSensitiveData(token)
  localStorage.setItem(STORAGE_KEYS.REFRESH_TOKEN, encryptedToken)
}

/**
 * 设置HttpOnly Cookie（通过API调用）
 */
const setHttpOnlyCookie = async (refreshToken: string): Promise<void> => {
  try {
    await fetch('/api/auth/set-cookie', {
      method: 'POST',
      credentials: 'include',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ refreshToken })
    })
  } catch (error) {
    console.error('设置HttpOnly Cookie失败:', error)
  }
}

/**
 * 清理HttpOnly Cookie
 */
const clearHttpOnlyCookie = async (): Promise<void> => {
  try {
    await fetch('/api/auth/clear-cookie', {
      method: 'POST',
      credentials: 'include'
    })
  } catch (error) {
    console.error('清理HttpOnly Cookie失败:', error)
  }
}

/**
 * 安全的Token刷新 - 防止并发刷新
 */
export const secureRefreshToken = async (): Promise<string | null> => {
  // 如果已经有刷新请求在进行，等待它完成
  if (tokenRefreshPromise) {
    await tokenRefreshPromise
    return getToken()
  }
  
  tokenRefreshPromise = performTokenRefresh()
  
  try {
    await tokenRefreshPromise
    return getToken()
  } finally {
    tokenRefreshPromise = null
  }
}

/**
 * 执行Token刷新
 */
const performTokenRefresh = async (): Promise<void> => {
  const refreshToken = getRefreshToken()
  if (!refreshToken) {
    throw new Error('No refresh token available')
  }
  
  try {
    const response = await fetch('/api/auth/refresh', {
      method: 'POST',
      credentials: 'include', // 包含HttpOnly Cookie
      headers: {
        'Content-Type': 'application/json',
        'X-Refresh-Token': refreshToken // 额外的安全层
      }
    })
    
    if (!response.ok) {
      throw new Error('Token refresh failed')
    }
    
    const data = await response.json()
    
    // 更新Token
    setToken(data.accessToken)
    if (data.refreshToken) {
      setRefreshToken(data.refreshToken)
    }
    
    // 更新用户信息
    if (data.user) {
      localStorage.setItem(STORAGE_KEYS.USER_INFO, JSON.stringify(data.user))
    }
    
  } catch (error) {
    console.error('Token刷新失败:', error)
    // 刷新失败，清理所有Token
    removeToken()
    throw error
  }
}

/**
 * 检查Token是否存在
 */
export const hasToken = (): boolean => {
  return !!getToken()
}

/**
 * 解析Token
 */
export const parseToken = (token: string): TokenPayload | null => {
  try {
    return jwtDecode<TokenPayload>(token)
  } catch (error) {
    console.error('Token解析失败:', error)
    return null
  }
}

/**
 * 检查Token是否过期
 */
export const isTokenExpired = (token?: string): boolean => {
  const tokenToCheck = token || getToken()
  if (!tokenToCheck) return true
  
  const payload = parseToken(tokenToCheck)
  if (!payload) return true
  
  // 提前5分钟判断过期
  const now = Date.now() / 1000
  return payload.exp < (now + 300)
}

/**
 * 检查Token是否即将过期（30分钟内）
 */
export const isTokenExpiringSoon = (token?: string): boolean => {
  const tokenToCheck = token || getToken()
  if (!tokenToCheck) return true
  
  const payload = parseToken(tokenToCheck)
  if (!payload) return true
  
  const now = Date.now() / 1000
  return payload.exp < (now + 1800) // 30分钟
}

/**
 * 获取Token剩余时间（秒）
 */
export const getTokenRemainingTime = (token?: string): number => {
  const tokenToCheck = token || getToken()
  if (!tokenToCheck) return 0
  
  const payload = parseToken(tokenToCheck)
  if (!payload) return 0
  
  const now = Date.now() / 1000
  return Math.max(0, payload.exp - now)
}

/**
 * 格式化Token过期时间
 */
export const formatTokenExpireTime = (token?: string): string => {
  const tokenToCheck = token || getToken()
  if (!tokenToCheck) return '未知'
  
  const payload = parseToken(tokenToCheck)
  if (!payload) return '未知'
  
  const expireDate = new Date(payload.exp * 1000)
  return expireDate.toLocaleString()
}

/**
 * 清除所有认证信息
 */
export const clearAuth = (): void => {
  removeToken()
  // 清除其他可能的用户相关缓存
  const keysToRemove = [
    STORAGE_KEYS.USER_INFO,
    STORAGE_KEYS.WATCHLIST,
    STORAGE_KEYS.TRADING_SETTINGS
  ]
  
  keysToRemove.forEach(key => {
    localStorage.removeItem(key)
  })
}

/**
 * 生成随机字符串（用于state参数等）
 */
export const generateRandomString = (length = 32): string => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let result = ''
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  return result
}

/**
 * 安全的数据加密 - 使用Web Crypto API
 */
export const encryptSensitiveData = (data: string): string => {
  try {
    // 使用更安全的加密方式
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(data)
    
    // 简单的Base64编码（实际项目中应该使用AES等对称加密）
    return btoa(String.fromCharCode(...new Uint8Array(dataBuffer)))
  } catch (error) {
    console.error('数据加密失败:', error)
    return btoa(encodeURIComponent(data))
  }
}

/**
 * 安全的数据解密
 */
export const decryptSensitiveData = (encryptedData: string): string => {
  try {
    const decoded = atob(encryptedData)
    const bytes = new Uint8Array(decoded.length)
    
    for (let i = 0; i < decoded.length; i++) {
      bytes[i] = decoded.charCodeAt(i)
    }
    
    const decoder = new TextDecoder()
    return decoder.decode(bytes)
  } catch (error) {
    console.error('数据解密失败:', error)
    try {
      return decodeURIComponent(atob(encryptedData))
    } catch {
      return encryptedData
    }
  }
}

/**
 * 生成CSRF Token
 */
export const generateCSRFToken = (): string => {
  const token = generateRandomString(32)
  sessionStorage.setItem('csrf_token', token)
  return token
}

/**
 * 获取CSRF Token
 */
export const getCSRFToken = (): string | null => {
  return sessionStorage.getItem('csrf_token')
}

/**
 * 验证CSRF Token
 */
export const validateCSRFToken = (token: string): boolean => {
  const storedToken = getCSRFToken()
  return storedToken === token
}

/**
 * 检查密码强度
 */
export const checkPasswordStrength = (password: string): {
  score: number
  level: 'weak' | 'medium' | 'strong' | 'very-strong'
  suggestions: string[]
} => {
  let score = 0
  const suggestions: string[] = []
  
  // 长度检查
  if (password.length >= 8) {
    score += 1
  } else {
    suggestions.push('密码长度至少8位')
  }
  
  if (password.length >= 12) {
    score += 1
  }
  
  // 包含小写字母
  if (/[a-z]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含小写字母')
  }
  
  // 包含大写字母
  if (/[A-Z]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含大写字母')
  }
  
  // 包含数字
  if (/\d/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含数字')
  }
  
  // 包含特殊字符
  if (/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(password)) {
    score += 1
  } else {
    suggestions.push('包含特殊字符')
  }
  
  // 不包含常见弱密码模式
  const weakPatterns = [
    /123456/,
    /password/i,
    /qwerty/i,
    /abc123/i,
    /111111/,
    /000000/
  ]
  
  const hasWeakPattern = weakPatterns.some(pattern => pattern.test(password))
  if (hasWeakPattern) {
    score -= 2
    suggestions.push('避免使用常见的弱密码模式')
  }
  
  // 确定强度等级
  let level: 'weak' | 'medium' | 'strong' | 'very-strong'
  if (score <= 2) {
    level = 'weak'
  } else if (score <= 4) {
    level = 'medium'
  } else if (score <= 5) {
    level = 'strong'
  } else {
    level = 'very-strong'
  }
  
  return {
    score: Math.max(0, score),
    level,
    suggestions
  }
}

/**
 * 检查是否需要重新登录
 */
export const shouldReLogin = (): boolean => {
  const token = getToken()
  if (!token) return true
  
  // 检查Token是否过期
  if (isTokenExpired(token)) return true
  
  // 检查上次登录时间（可选）
  const lastLoginTime = localStorage.getItem('last_login_time')
  if (lastLoginTime) {
    const lastLogin = parseInt(lastLoginTime)
    const now = Date.now()
    // 如果超过7天未登录，要求重新登录
    if (now - lastLogin > 7 * 24 * 60 * 60 * 1000) {
      return true
    }
  }
  
  return false
}

/**
 * 记录登录时间
 */
export const recordLoginTime = (): void => {
  localStorage.setItem('last_login_time', Date.now().toString())
}

/**
 * 获取用户权限列表
 */
export const getUserPermissions = (): string[] => {
  const token = getToken()
  if (!token) return []
  
  const payload = parseToken(token)
  if (!payload) return []
  
  // 从Token中解析权限，或从本地存储获取
  const userInfo = localStorage.getItem(STORAGE_KEYS.USER_INFO)
  if (userInfo) {
    try {
      const user = JSON.parse(userInfo)
      return user.permissions || []
    } catch (error) {
      console.error('解析用户信息失败:', error)
    }
  }
  
  return []
}

/**
 * 检查用户是否有指定权限
 */
export const hasPermission = (permission: string): boolean => {
  const permissions = getUserPermissions()
  return permissions.includes(permission) || permissions.includes('*')
}

/**
 * 检查用户是否有任一权限
 */
export const hasAnyPermission = (permissions: string[]): boolean => {
  return permissions.some(permission => hasPermission(permission))
}

/**
 * 检查用户是否有所有权限
 */
export const hasAllPermissions = (permissions: string[]): boolean => {
  return permissions.every(permission => hasPermission(permission))
}

/**
 * 安全的API请求拦截器
 */
export const secureApiInterceptor = {
  request: (config: any) => {
    // 添加CSRF Token
    const csrfToken = getCSRFToken()
    if (csrfToken) {
      config.headers['X-CSRF-Token'] = csrfToken
    }
    
    // 添加访问令牌
    const accessToken = getToken()
    if (accessToken) {
      config.headers['Authorization'] = `Bearer ${accessToken}`
    }
    
    return config
  },
  
  response: async (response: any) => {
    // 检查是否需要刷新Token
    if (response.status === 401 && response.data?.code === 'TOKEN_EXPIRED') {
      try {
        const newToken = await secureRefreshToken()
        if (newToken) {
          // 重新发送原请求
          response.config.headers['Authorization'] = `Bearer ${newToken}`
          return fetch(response.config)
        }
      } catch (error) {
        // 刷新失败，跳转到登录页
        window.location.href = '/login'
      }
    }
    
    return response
  }
}

/**
 * 页面刷新时恢复Token
 */
export const restoreTokenOnPageLoad = (): void => {
  // 页面加载时，尝试从localStorage恢复Token到内存
  const encryptedToken = localStorage.getItem(STORAGE_KEYS.AUTH_TOKEN)
  if (encryptedToken) {
    try {
      const token = decryptSensitiveData(encryptedToken)
      if (token && !isTokenExpired(token)) {
        memoryAccessToken = token
      }
    } catch (error) {
      console.error('恢复Token失败:', error)
    }
  }
  
  const encryptedRefreshToken = localStorage.getItem(STORAGE_KEYS.REFRESH_TOKEN)
  if (encryptedRefreshToken) {
    try {
      const refreshToken = decryptSensitiveData(encryptedRefreshToken)
      if (refreshToken) {
        memoryRefreshToken = refreshToken
      }
    } catch (error) {
      console.error('恢复刷新Token失败:', error)
    }
  }
}

// 页面加载时自动恢复Token
if (typeof window !== 'undefined') {
  window.addEventListener('load', restoreTokenOnPageLoad)
}