import { test, expect } from '@playwright/test'

test.describe('登录流程测试', () => {
  test.beforeEach(async ({ page }) => {
    // 访问登录页面
    await page.goto('/login')
  })

  test('页面应该正确加载', async ({ page }) => {
    // 检查页面标题
    await expect(page).toHaveTitle(/量化投资平台/)
    
    // 检查登录表单元素
    await expect(page.locator('input[type="text"]')).toBeVisible()
    await expect(page.locator('input[type="password"]')).toBeVisible()
    await expect(page.locator('button[type="submit"]')).toBeVisible()
  })

  test('应该显示必填字段验证', async ({ page }) => {
    // 点击提交按钮而不填写任何信息
    await page.click('button[type="submit"]')
    
    // 检查验证错误消息
    await expect(page.locator('.el-form-item__error')).toBeVisible()
  })

  test('应该显示用户名格式验证', async ({ page }) => {
    // 输入无效的用户名
    await page.fill('input[type="text"]', 'a')
    await page.fill('input[type="password"]', 'password123')
    await page.click('button[type="submit"]')
    
    // 检查用户名长度验证
    const errorMessage = page.locator('.el-form-item__error')
    await expect(errorMessage).toContainText('用户名长度不能少于')
  })

  test('应该显示密码强度验证', async ({ page }) => {
    // 输入弱密码
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', '123')
    await page.click('button[type="submit"]')
    
    // 检查密码强度验证
    const errorMessage = page.locator('.el-form-item__error')
    await expect(errorMessage).toContainText('密码长度不能少于')
  })

  test('验证码应该正确显示和刷新', async ({ page }) => {
    // 检查验证码图片是否显示
    const captchaImage = page.locator('.captcha-image')
    await expect(captchaImage).toBeVisible()
    
    // 点击刷新验证码
    await page.click('.captcha-refresh')
    
    // 验证码应该重新加载
    await page.waitForTimeout(1000)
    await expect(captchaImage).toBeVisible()
  })

  test('登录成功应该跳转到仪表板', async ({ page }) => {
    // 模拟成功登录的API响应
    await page.route('**/api/v1/auth/login', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          user: {
            id: '1',
            username: 'testuser',
            email: '<EMAIL>',
            roles: ['user']
          },
          token: 'mock-jwt-token',
          refreshToken: 'mock-refresh-token'
        })
      })
    })
    
    // 填写登录表单
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', 'password123')
    await page.fill('input[placeholder="验证码"]', '1234')
    
    // 提交表单
    await page.click('button[type="submit"]')
    
    // 等待跳转到仪表板
    await expect(page).toHaveURL('/dashboard')
  })

  test('登录失败应该显示错误消息', async ({ page }) => {
    // 模拟登录失败的API响应
    await page.route('**/api/v1/auth/login', async route => {
      await route.fulfill({
        status: 400,
        contentType: 'application/json',
        body: JSON.stringify({
          message: '用户名或密码错误'
        })
      })
    })
    
    // 填写登录表单
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', 'wrongpassword')
    await page.fill('input[placeholder="验证码"]', '1234')
    
    // 提交表单
    await page.click('button[type="submit"]')
    
    // 检查错误提示
    await expect(page.locator('.el-notification')).toContainText('登录失败')
  })

  test('记住我功能应该正常工作', async ({ page }) => {
    // 勾选记住我
    await page.check('input[type="checkbox"]')
    
    // 填写表单
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', 'password123')
    
    // 检查复选框状态
    const checkbox = page.locator('input[type="checkbox"]')
    await expect(checkbox).toBeChecked()
  })

  test('忘记密码链接应该可以点击', async ({ page }) => {
    // 点击忘记密码链接
    await page.click('text=忘记密码?')
    
    // 检查是否跳转到忘记密码页面或显示相关对话框
    await expect(page.locator('.forgot-password-dialog')).toBeVisible()
  })

  test('注册链接应该可以点击', async ({ page }) => {
    // 点击注册链接
    await page.click('text=立即注册')
    
    // 检查是否跳转到注册页面
    await expect(page).toHaveURL('/register')
  })

  test('响应式设计应该在移动端正常工作', async ({ page }) => {
    // 设置移动端视窗大小
    await page.setViewportSize({ width: 375, height: 667 })
    
    // 检查移动端布局
    const loginForm = page.locator('.login-form')
    await expect(loginForm).toBeVisible()
    
    // 检查输入框在移动端的显示
    const usernameInput = page.locator('input[type="text"]')
    await expect(usernameInput).toBeVisible()
  })

  test('键盘导航应该正常工作', async ({ page }) => {
    // 使用Tab键导航
    await page.keyboard.press('Tab')
    await expect(page.locator('input[type="text"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('input[type="password"]')).toBeFocused()
    
    await page.keyboard.press('Tab')
    await expect(page.locator('input[placeholder="验证码"]')).toBeFocused()
  })

  test('Enter键应该提交表单', async ({ page }) => {
    // 填写表单
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', 'password123')
    await page.fill('input[placeholder="验证码"]', '1234')
    
    // 在密码输入框中按Enter键
    await page.locator('input[type="password"]').press('Enter')
    
    // 验证表单被提交（通过检查API调用或页面变化）
    await page.waitForRequest('**/api/v1/auth/login')
  })
})

test.describe('登录安全测试', () => {
  test('应该防止XSS攻击', async ({ page }) => {
    await page.goto('/login')
    
    // 尝试输入XSS脚本
    const xssScript = '<script>alert("XSS")</script>'
    await page.fill('input[type="text"]', xssScript)
    
    // 检查脚本是否被正确转义
    const inputValue = await page.inputValue('input[type="text"]')
    expect(inputValue).toBe(xssScript)
    
    // 提交表单后页面不应该执行恶意脚本
    await page.click('button[type="submit"]')
    
    // 页面应该保持正常状态
    await expect(page.locator('.login-form')).toBeVisible()
  })

  test('应该限制登录尝试次数', async ({ page }) => {
    await page.goto('/login')
    
    // 模拟多次登录失败
    for (let i = 0; i < 5; i++) {
      await page.route('**/api/v1/auth/login', async route => {
        await route.fulfill({
          status: 400,
          contentType: 'application/json',
          body: JSON.stringify({
            message: '用户名或密码错误'
          })
        })
      })
      
      await page.fill('input[type="text"]', 'testuser')
      await page.fill('input[type="password"]', 'wrongpassword')
      await page.fill('input[placeholder="验证码"]', '1234')
      await page.click('button[type="submit"]')
      
      await page.waitForTimeout(1000)
    }
    
    // 第6次尝试应该显示账户锁定提示
    await page.route('**/api/v1/auth/login', async route => {
      await route.fulfill({
        status: 429,
        contentType: 'application/json',
        body: JSON.stringify({
          message: '登录尝试次数过多，账户已被临时锁定'
        })
      })
    })
    
    await page.fill('input[type="text"]', 'testuser')
    await page.fill('input[type="password"]', 'wrongpassword')
    await page.fill('input[placeholder="验证码"]', '1234')
    await page.click('button[type="submit"]')
    
    await expect(page.locator('.el-notification')).toContainText('账户已被临时锁定')
  })
})