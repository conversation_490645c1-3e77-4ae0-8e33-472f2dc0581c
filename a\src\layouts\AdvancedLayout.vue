<template>
  <div class="advanced-layout">
    <!-- 左侧导航栏 -->
    <aside class="sidebar" :class="{ 'sidebar-collapsed': sidebarCollapsed }">
      <!-- Logo区域 -->
      <div class="sidebar-header">
        <div class="logo-section">
          <div class="logo-icon">V</div>
          <span v-show="!sidebarCollapsed" class="logo-text">量化平台</span>
        </div>
      </div>

      <!-- 导航菜单 -->
      <nav class="sidebar-nav">
        <div class="nav-section">
          <router-link to="/dashboard" class="nav-item" :class="{ active: $route.path === '/dashboard' }">
            <el-icon class="nav-icon"><Monitor /></el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">仪表盘</span>
          </router-link>

          <div class="nav-group">
            <div class="nav-group-header" @click="toggleGroup('market')">
              <el-icon class="nav-icon"><TrendCharts /></el-icon>
              <span v-show="!sidebarCollapsed" class="nav-text">行情中心</span>
              <el-icon v-show="!sidebarCollapsed" class="expand-icon" :class="{ expanded: expandedGroups.market }">
                <ArrowDown />
              </el-icon>
            </div>
            <div v-show="!sidebarCollapsed && expandedGroups.market" class="nav-submenu">
              <router-link to="/market/realtime" class="nav-subitem">实时行情</router-link>
              <router-link to="/market/analysis" class="nav-subitem">行情分析</router-link>
              <router-link to="/market/trends" class="nav-subitem">市场趋势</router-link>
            </div>
          </div>

          <div class="nav-group">
            <div class="nav-group-header" @click="toggleGroup('trading')">
              <el-icon class="nav-icon"><Money /></el-icon>
              <span v-show="!sidebarCollapsed" class="nav-text">交易中心</span>
              <el-icon v-show="!sidebarCollapsed" class="expand-icon" :class="{ expanded: expandedGroups.trading }">
                <ArrowDown />
              </el-icon>
            </div>
            <div v-show="!sidebarCollapsed && expandedGroups.trading" class="nav-submenu">
              <router-link to="/trading/orders" class="nav-subitem">订单管理</router-link>
              <router-link to="/trading/positions" class="nav-subitem">持仓管理</router-link>
              <router-link to="/trading/history" class="nav-subitem">交易历史</router-link>
            </div>
          </div>

          <div class="nav-group">
            <div class="nav-group-header" @click="toggleGroup('strategy')">
              <el-icon class="nav-icon"><DataAnalysis /></el-icon>
              <span v-show="!sidebarCollapsed" class="nav-text">策略中心</span>
              <el-icon v-show="!sidebarCollapsed" class="expand-icon" :class="{ expanded: expandedGroups.strategy }">
                <ArrowDown />
              </el-icon>
            </div>
            <div v-show="!sidebarCollapsed && expandedGroups.strategy" class="nav-submenu">
              <router-link to="/strategy/develop" class="nav-subitem">策略开发</router-link>
              <router-link to="/strategy/monitor" class="nav-subitem">策略监控</router-link>
              <router-link to="/strategy/library" class="nav-subitem">策略库</router-link>
            </div>
          </div>

          <router-link to="/backtest" class="nav-item" :class="{ active: $route.path === '/backtest' }">
            <el-icon class="nav-icon"><Refresh /></el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">回测分析</span>
          </router-link>

          <router-link to="/portfolio" class="nav-item" :class="{ active: $route.path === '/portfolio' }">
            <el-icon class="nav-icon"><PieChart /></el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">投资组合</span>
          </router-link>

          <router-link to="/risk" class="nav-item" :class="{ active: $route.path === '/risk' }">
            <el-icon class="nav-icon"><Warning /></el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">风险管理</span>
          </router-link>

          <router-link to="/components" class="nav-item" :class="{ active: $route.path === '/components' }">
            <el-icon class="nav-icon"><Grid /></el-icon>
            <span v-show="!sidebarCollapsed" class="nav-text">组件展示</span>
          </router-link>
        </div>
      </nav>

      <!-- 侧边栏底部 -->
      <div class="sidebar-footer">
        <el-button text @click="toggleSidebar" class="collapse-btn">
          <el-icon><Fold v-if="!sidebarCollapsed" /><Expand v-else /></el-icon>
        </el-button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <div class="main-container">
      <!-- 顶部标题栏 -->
      <header class="main-header">
        <div class="header-content">
          <div class="page-title">
            <h1>{{ pageTitle }}</h1>
            <div class="breadcrumb">
              <span v-for="(item, index) in breadcrumbs" :key="index">
                <span v-if="index > 0"> / </span>
                <span :class="{ active: index === breadcrumbs.length - 1 }">{{ item }}</span>
              </span>
            </div>
          </div>
          
          <div class="header-actions">
            <el-button text>
              <el-icon><Bell /></el-icon>
            </el-button>
            <el-button text>
              <el-icon><Setting /></el-icon>
            </el-button>
            <el-avatar size="small">U</el-avatar>
          </div>
        </div>
      </header>

      <!-- 页面内容 -->
      <main class="main-content">
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'
import { 
  Monitor, TrendCharts, Money, DataAnalysis, Refresh, PieChart, Warning, Grid,
  ArrowDown, Fold, Expand, Bell, Setting
} from '@element-plus/icons-vue'

const route = useRoute()

// 响应式数据
const sidebarCollapsed = ref(false)
const expandedGroups = ref({
  market: true,
  trading: false,
  strategy: false
})

// 缓存的视图组件
const cachedViews = ref(['Dashboard', 'Market', 'Trading'])

// 计算属性
const pageTitle = computed(() => {
  const titleMap: Record<string, string> = {
    '/dashboard': '仪表盘',
    '/market': '实时行情',
    '/market/realtime': '实时行情',
    '/market/analysis': '行情分析',
    '/market/trends': '市场趋势',
    '/trading': '交易中心',
    '/strategy': '策略中心',
    '/backtest': '回测分析',
    '/portfolio': '投资组合',
    '/risk': '风险管理',
    '/components': '组件展示'
  }
  return titleMap[route.path] || '量化平台'
})

const breadcrumbs = computed(() => {
  const path = route.path
  if (path === '/dashboard') return ['首页', '仪表盘']
  if (path.startsWith('/market')) return ['首页', '行情中心', pageTitle.value]
  if (path.startsWith('/trading')) return ['首页', '交易中心', pageTitle.value]
  if (path.startsWith('/strategy')) return ['首页', '策略中心', pageTitle.value]
  return ['首页', pageTitle.value]
})

// 方法
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
}

const toggleGroup = (groupName: keyof typeof expandedGroups.value) => {
  if (!sidebarCollapsed.value) {
    expandedGroups.value[groupName] = !expandedGroups.value[groupName]
  }
}
</script>

<style scoped>
.advanced-layout {
  display: flex;
  height: 100vh;
  background: #f5f7fa;
}

/* 侧边栏样式 */
.sidebar {
  width: 240px;
  background: white;
  border-right: 1px solid #e8e8e8;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
}

.sidebar-collapsed {
  width: 64px;
}

.sidebar-header {
  height: 64px;
  display: flex;
  align-items: center;
  padding: 0 16px;
  border-bottom: 1px solid #e8e8e8;
}

.logo-section {
  display: flex;
  align-items: center;
  gap: 12px;
}

.logo-icon {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 18px;
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

/* 导航样式 */
.sidebar-nav {
  flex: 1;
  padding: 16px 0;
  overflow-y: auto;
}

.nav-item, .nav-group-header {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  color: #666;
  text-decoration: none;
  transition: all 0.3s ease;
  cursor: pointer;
  border-radius: 0 24px 24px 0;
  margin-right: 8px;
}

.nav-item:hover, .nav-group-header:hover {
  background: #f0f9ff;
  color: #1890ff;
}

.nav-item.active {
  background: #e6f7ff;
  color: #1890ff;
  border-right: 3px solid #1890ff;
}

.nav-icon {
  font-size: 18px;
  margin-right: 12px;
  min-width: 18px;
}

.nav-text {
  flex: 1;
  font-size: 14px;
}

.expand-icon {
  font-size: 12px;
  transition: transform 0.3s ease;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.nav-submenu {
  margin-left: 46px;
  margin-bottom: 8px;
}

.nav-subitem {
  display: block;
  padding: 8px 16px;
  color: #999;
  text-decoration: none;
  font-size: 13px;
  border-radius: 4px;
  margin: 2px 0;
  transition: all 0.3s ease;
}

.nav-subitem:hover {
  background: #f0f9ff;
  color: #1890ff;
}

.sidebar-footer {
  padding: 16px;
  border-top: 1px solid #e8e8e8;
}

.collapse-btn {
  width: 100%;
  justify-content: center;
}

/* 主内容区域 */
.main-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-width: 0;
}

.main-header {
  height: 64px;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  padding: 0 24px;
}

.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.page-title h1 {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: #333;
}

.breadcrumb {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}

.breadcrumb .active {
  color: #1890ff;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.main-content {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

/* 过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
