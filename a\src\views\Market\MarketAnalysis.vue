<template>
  <div class="market-analysis">
    <!-- 页面标题区域 -->
    <div class="page-header">
      <div class="header-left">
        <h2>行情分析</h2>
        <div class="search-section">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索股票代码或名称"
            prefix-icon="Search"
            style="width: 300px;"
            @input="handleSearch"
          />
          <el-button type="primary">刷新</el-button>
        </div>
      </div>
    </div>

    <!-- 市场趋势卡片 -->
    <div class="trend-section">
      <el-card class="trend-card">
        <template #header>
          <div class="card-header">
            <span>市场趋势</span>
            <div class="trend-tabs">
              <el-button 
                v-for="period in trendPeriods" 
                :key="period.value"
                :type="selectedPeriod === period.value ? 'primary' : 'default'"
                size="small"
                @click="selectedPeriod = period.value"
              >
                {{ period.label }}
              </el-button>
            </div>
          </div>
        </template>
        
        <div class="trend-content">
          <div class="trend-indicator">
            <span class="trend-label">加载失败</span>
            <div class="error-actions">
              <el-button size="small" @click="retryLoad">重试</el-button>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 股票筛选工具栏 -->
    <div class="filter-toolbar">
      <div class="filter-left">
        <el-button 
          v-for="category in stockCategories" 
          :key="category.value"
          :type="selectedCategory === category.value ? 'primary' : 'default'"
          @click="selectedCategory = category.value"
        >
          {{ category.label }}
        </el-button>
      </div>
      
      <div class="filter-right">
        <el-select v-model="selectedIndustry" placeholder="选择行业" style="width: 120px;">
          <el-option label="科技" value="tech" />
          <el-option label="金融" value="finance" />
          <el-option label="医药" value="medical" />
        </el-select>
        
        <el-button-group>
          <el-button :type="viewMode === 'list' ? 'primary' : 'default'" @click="viewMode = 'list'">
            <el-icon><List /></el-icon>
          </el-button>
          <el-button :type="viewMode === 'grid' ? 'primary' : 'default'" @click="viewMode = 'grid'">
            <el-icon><Grid /></el-icon>
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 股票列表 -->
    <div class="stock-list-section">
      <el-card>
        <template #header>
          <div class="list-header">
            <span>股票列表</span>
            <span class="stock-count">共 {{ stockList.length }} 只股票</span>
          </div>
        </template>

        <el-table 
          :data="filteredStockList" 
          stripe 
          style="width: 100%"
          :default-sort="{ prop: 'change_percent', order: 'descending' }"
        >
          <el-table-column prop="code" label="代码" width="100" />
          <el-table-column prop="name" label="名称" width="120" />
          <el-table-column prop="price" label="现价" width="100" align="right">
            <template #default="{ row }">
              <span :class="getPriceClass(row.change)">{{ row.price }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="change" label="涨跌额" width="100" align="right">
            <template #default="{ row }">
              <span :class="getPriceClass(row.change)">
                {{ row.change > 0 ? '+' : '' }}{{ row.change }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="change_percent" label="涨跌幅" width="100" align="right">
            <template #default="{ row }">
              <span :class="getPriceClass(row.change)">
                {{ row.change > 0 ? '+' : '' }}{{ row.change_percent }}%
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="volume" label="成交量" width="120" align="right">
            <template #default="{ row }">
              {{ formatVolume(row.volume) }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="200" align="center">
            <template #default="{ row }">
              <el-button size="small" @click="viewDetail(row)">详情</el-button>
              <el-button size="small" type="primary" @click="addToWatch(row)">自选</el-button>
              <el-button size="small" type="success" @click="trade(row)">交易</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, List, Grid } from '@element-plus/icons-vue'

// 响应式数据
const searchKeyword = ref('')
const selectedPeriod = ref('day')
const selectedCategory = ref('all')
const selectedIndustry = ref('')
const viewMode = ref('list')

// 趋势周期选项
const trendPeriods = [
  { label: '日线', value: 'day' },
  { label: '周线', value: 'week' },
  { label: '月线', value: 'month' }
]

// 股票分类
const stockCategories = [
  { label: '全部', value: 'all' },
  { label: '沪A', value: 'sh_a' },
  { label: '深A', value: 'sz_a' },
  { label: '创业板', value: 'cy' },
  { label: '科创板', value: 'kc' }
]

// 模拟股票数据
const stockList = ref([
  {
    code: '000166',
    name: '申万宏源',
    price: 62.39,
    change: 2.83,
    change_percent: 4.32,
    volume: 335275,
    industry: 'finance'
  },
  {
    code: '000858',
    name: '五粮液',
    price: 28.39,
    change: 1.08,
    change_percent: 3.80,
    volume: 244275,
    industry: 'consumer'
  },
  {
    code: '300059',
    name: '东方财富',
    price: 103.89,
    change: 3.90,
    change_percent: 3.76,
    volume: 641175,
    industry: 'finance'
  },
  {
    code: '600036',
    name: '招商银行',
    price: 35.67,
    change: -0.23,
    change_percent: -0.64,
    volume: 892456,
    industry: 'finance'
  },
  {
    code: '000001',
    name: '平安银行',
    price: 12.45,
    change: -0.15,
    change_percent: -1.19,
    volume: 567890,
    industry: 'finance'
  }
])

// 计算属性
const filteredStockList = computed(() => {
  let filtered = stockList.value

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(stock => 
      stock.code.toLowerCase().includes(keyword) || 
      stock.name.toLowerCase().includes(keyword)
    )
  }

  // 按分类筛选
  if (selectedCategory.value !== 'all') {
    // 这里可以根据股票代码前缀进行筛选
    filtered = filtered.filter(stock => {
      if (selectedCategory.value === 'sh_a') return stock.code.startsWith('6')
      if (selectedCategory.value === 'sz_a') return stock.code.startsWith('0')
      if (selectedCategory.value === 'cy') return stock.code.startsWith('3')
      return true
    })
  }

  // 按行业筛选
  if (selectedIndustry.value) {
    filtered = filtered.filter(stock => stock.industry === selectedIndustry.value)
  }

  return filtered
})

// 方法
const handleSearch = () => {
  // 搜索逻辑
}

const retryLoad = () => {
  ElMessage.info('正在重新加载数据...')
}

const getPriceClass = (change: number) => {
  if (change > 0) return 'price-up'
  if (change < 0) return 'price-down'
  return 'price-neutral'
}

const formatVolume = (volume: number) => {
  if (volume >= 10000) {
    return (volume / 10000).toFixed(1) + '万'
  }
  return volume.toString()
}

const viewDetail = (stock: any) => {
  ElMessage.info(`查看 ${stock.name} 详情`)
}

const addToWatch = (stock: any) => {
  ElMessage.success(`已添加 ${stock.name} 到自选股`)
}

const trade = (stock: any) => {
  ElMessage.info(`交易 ${stock.name}`)
}

onMounted(() => {
  console.log('市场分析页面已加载')
})
</script>

<style scoped>
.market-analysis {
  padding: 0;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.header-left h2 {
  margin: 0;
  font-size: 24px;
  font-weight: 600;
  color: #333;
}

.search-section {
  display: flex;
  gap: 12px;
}

.trend-section {
  margin-bottom: 24px;
}

.trend-card {
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.trend-tabs {
  display: flex;
  gap: 8px;
}

.trend-content {
  height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #fafafa;
  border-radius: 4px;
}

.trend-indicator {
  text-align: center;
}

.trend-label {
  color: #999;
  font-size: 14px;
}

.error-actions {
  margin-top: 12px;
}

.filter-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 16px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.filter-left {
  display: flex;
  gap: 8px;
}

.filter-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.stock-list-section {
  background: white;
  border-radius: 8px;
}

.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stock-count {
  color: #999;
  font-size: 14px;
}

/* 价格颜色样式 */
.price-up {
  color: #f56565;
}

.price-down {
  color: #48bb78;
}

.price-neutral {
  color: #666;
}

/* 表格样式优化 */
:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background: #fafafa;
  font-weight: 600;
}

:deep(.el-table td) {
  padding: 12px 0;
}

:deep(.el-table .el-button) {
  margin: 0 2px;
}
</style>
