import type { RouteRecordRaw } from 'vue-router'

const backtestRoutes: RouteRecordRaw[] = [
  {
    path: 'backtest',
    redirect: 'backtest/analysis'
  },
  {
    path: 'backtest/analysis',
    name: 'backtest-analysis',
    component: () => import('@/views/Backtest/BacktestView.vue'),
    meta: {
      title: '回测分析'
    }
  },
  {
    path: 'backtest/create',
    name: 'create-backtest',
    component: () => import('@/views/Backtest/CreateBacktest.vue'),
    meta: {
      title: '创建回测'
    }
  },
  {
    path: 'backtest/history',
    name: 'backtest-history',
    component: () => import('@/views/Backtest/BacktestHistory.vue'),
    meta: {
      title: '回测历史'
    }
  },
  {
    path: 'backtest/:id/result',
    name: 'BacktestResult',
    component: () => import('@/views/Backtest/BacktestResultView.vue'),
    meta: { title: '回测结果', requiresAuth: true }
  }
]

export default backtestRoutes
