import { mount, VueWrapper } from '@vue/test-utils'
import { createPinia, Pinia } from 'pinia'
import { Router, createRouter, createWebHistory } from 'vue-router'
import { ElConfigProvider } from 'element-plus'
import type { ComponentMountingOptions } from '@vue/test-utils'

// 创建测试用的Pinia实例
export function createTestPinia(): Pinia {
  return createPinia()
}

// 创建测试用的Router实例
export function createTestRouter(): Router {
  return createRouter({
    history: createWebHistory(),
    routes: [
      { path: '/', component: { template: '<div>Home</div>' } },
      { path: '/login', component: { template: '<div>Login</div>' } },
      { path: '/dashboard', component: { template: '<div>Dashboard</div>' } },
      { path: '/market', component: { template: '<div>Market</div>' } },
      { path: '/trading', component: { template: '<div>Trading</div>' } }
    ]
  })
}

// 测试挂载选项的默认配置
export function getDefaultMountOptions(): ComponentMountingOptions<any> {
  const pinia = createTestPinia()
  const router = createTestRouter()

  return {
    global: {
      plugins: [pinia, router],
      components: {
        ElConfigProvider
      },
      provide: {
        // 提供必要的依赖注入
      },
      stubs: {
        // 存根化一些复杂的组件
        'el-loading': true,
        'el-notification': true
      }
    }
  }
}

// 创建带有默认配置的组件挂载函数
export function mountWithDefaults<T extends Record<string, any>>(
  component: any,
  options: ComponentMountingOptions<T> = {}
): VueWrapper<any> {
  const defaultOptions = getDefaultMountOptions()
  
  // 合并选项
  const mergedOptions = {
    ...defaultOptions,
    ...options,
    global: {
      ...defaultOptions.global,
      ...options.global,
      plugins: [
        ...(defaultOptions.global?.plugins || []),
        ...(options.global?.plugins || [])
      ],
      components: {
        ...defaultOptions.global?.components,
        ...options.global?.components
      },
      stubs: {
        ...defaultOptions.global?.stubs,
        ...options.global?.stubs
      }
    }
  }

  return mount(component, mergedOptions)
}

// 模拟API响应的辅助函数
export class MockAPIHelper {
  private mockFunctions: Map<string, any> = new Map()

  mockAPI(apiName: string, implementation: any) {
    this.mockFunctions.set(apiName, implementation)
    return this
  }

  mockSuccessResponse(apiName: string, data: any) {
    this.mockFunctions.set(apiName, () => Promise.resolve(data))
    return this
  }

  mockErrorResponse(apiName: string, error: any) {
    this.mockFunctions.set(apiName, () => Promise.reject(error))
    return this
  }

  mockDelayedResponse(apiName: string, data: any, delay: number = 1000) {
    this.mockFunctions.set(apiName, () => 
      new Promise(resolve => setTimeout(() => resolve(data), delay))
    )
    return this
  }

  getMock(apiName: string) {
    return this.mockFunctions.get(apiName)
  }

  clearMocks() {
    this.mockFunctions.clear()
  }
}

// 创建模拟的用户数据
export function createMockUser(overrides: Partial<any> = {}) {
  return {
    id: '1',
    username: 'testuser',
    email: '<EMAIL>',
    roles: ['user'],
    permissions: ['read:market', 'write:orders'],
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }
}

// 创建模拟的市场数据
export function createMockQuote(symbol: string = 'AAPL', overrides: Partial<any> = {}) {
  return {
    symbol,
    name: `${symbol} Inc.`,
    price: 150.25,
    change: 2.5,
    changePercent: 1.69,
    volume: 1000000,
    timestamp: Date.now(),
    bid: 150.20,
    ask: 150.30,
    high: 152.00,
    low: 149.50,
    open: 150.00,
    ...overrides
  }
}

// 创建模拟的K线数据
export function createMockKLineData(count: number = 10, overrides: Partial<any> = {}) {
  const data = []
  const baseTime = Date.now() - (count * 24 * 60 * 60 * 1000) // count天前
  let price = 150

  for (let i = 0; i < count; i++) {
    const change = (Math.random() - 0.5) * 10 // -5到5的随机变化
    const open = price
    const close = price + change
    const high = Math.max(open, close) + Math.random() * 2
    const low = Math.min(open, close) - Math.random() * 2

    data.push({
      timestamp: baseTime + (i * 24 * 60 * 60 * 1000),
      open,
      high,
      low,
      close,
      volume: Math.floor(Math.random() * 2000000) + 500000,
      ...overrides
    })

    price = close
  }

  return data
}

// 创建模拟的订单数据
export function createMockOrder(overrides: Partial<any> = {}) {
  return {
    id: `order-${Date.now()}`,
    symbol: 'AAPL',
    side: 'buy',
    type: 'limit',
    quantity: 100,
    price: 150.25,
    filledQuantity: 0,
    status: 'pending',
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    ...overrides
  }
}

// 等待异步操作完成的辅助函数
export async function waitForAsync() {
  await new Promise(resolve => setTimeout(resolve, 0))
}

// 等待Vue组件更新的辅助函数
export async function waitForComponentUpdate(wrapper: VueWrapper<any>) {
  await wrapper.vm.$nextTick()
  await waitForAsync()
}

// 触发组件事件的辅助函数
export async function triggerEvent(wrapper: VueWrapper<any>, selector: string, event: string, data?: any) {
  const element = wrapper.find(selector)
  await element.trigger(event, data)
  await waitForComponentUpdate(wrapper)
}

// 模拟WebSocket连接
export class MockWebSocket {
  private handlers: Map<string, Function[]> = new Map()
  public readyState = WebSocket.CONNECTING

  constructor(public url: string) {
    setTimeout(() => {
      this.readyState = WebSocket.OPEN
      this.emit('open', {})
    }, 100)
  }

  addEventListener(event: string, handler: Function) {
    if (!this.handlers.has(event)) {
      this.handlers.set(event, [])
    }
    this.handlers.get(event)!.push(handler)
  }

  removeEventListener(event: string, handler: Function) {
    const handlers = this.handlers.get(event)
    if (handlers) {
      const index = handlers.indexOf(handler)
      if (index !== -1) {
        handlers.splice(index, 1)
      }
    }
  }

  send(data: string) {
    console.log('MockWebSocket send:', data)
  }

  close() {
    this.readyState = WebSocket.CLOSED
    this.emit('close', {})
  }

  emit(event: string, data: any) {
    const handlers = this.handlers.get(event)
    if (handlers) {
      handlers.forEach(handler => handler(data))
    }
  }

  // 模拟接收消息
  mockReceive(data: any) {
    this.emit('message', { data: JSON.stringify(data) })
  }
}

// 测试环境检查
export function isTestEnvironment(): boolean {
  return process.env.NODE_ENV === 'test' || !!globalThis.describe
}

// 控制台静默（在测试中避免不必要的日志输出）
export function silenceConsole() {
  const originalConsoleError = console.error
  const originalConsoleWarn = console.warn
  
  console.error = (...args: any[]) => {
    // 过滤掉一些不重要的错误
    const message = args[0]?.toString() || ''
    if (message.includes('Warning') || message.includes('[Vue warn]')) {
      return
    }
    originalConsoleError.apply(console, args)
  }
  
  console.warn = (...args: any[]) => {
    // 过滤掉一些不重要的警告
    const message = args[0]?.toString() || ''
    if (message.includes('[Vue warn]') || message.includes('deprecation')) {
      return
    }
    originalConsoleWarn.apply(console, args)
  }

  return () => {
    console.error = originalConsoleError
    console.warn = originalConsoleWarn
  }
}