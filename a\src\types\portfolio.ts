// 投资组合数据
export interface PortfolioData {
  totalAssets: number
  positionValue: number
  availableCash: number
  totalChange: number
  totalChangePercent: number
  todayPnl: number
  todayPnlPercent: number
  updateTime: string
}

// 持仓信息
export interface Position {
  symbol: string
  name: string
  quantity: number
  avgPrice: number
  currentPrice: number
  marketValue: number
  costValue: number
  pnl: number
  pnlPercent: number
  weight: number
  changePercent: number
  industry: string
  sector?: string
  lastUpdate: string
}

// 投资组合绩效
export interface PortfolioPerformance {
  period: string
  data: Array<{
    date: number
    value: number
    netValue: number
    benchmark?: number
    drawdown?: number
  }>
  statistics: {
    totalReturn: number
    annualizedReturn: number
    volatility: number
    sharpeRatio: number
    maxDrawdown: number
    winRate: number
  }
}

// 行业分布
export interface IndustryDistribution {
  name: string
  value: number
  weight: number
  count: number
}

// 资产配置
export interface AssetAllocation {
  type: 'stock' | 'bond' | 'fund' | 'cash' | 'other'
  name: string
  value: number
  weight: number
}

// 再平衡请求
export interface RebalanceRequest {
  method: 'equal_weight' | 'market_cap' | 'risk_parity' | 'custom'
  targets?: Array<{
    symbol: string
    targetWeight: number
  }>
  constraints?: {
    maxPositionWeight?: number
    minPositionWeight?: number
    maxTurnover?: number
    maxTransactionCost?: number
  }
  executeImmediately: boolean
}

// 交易记录
export interface Transaction {
  id: string
  symbol: string
  name: string
  type: 'buy' | 'sell'
  quantity: number
  price: number
  amount: number
  fee: number
  timestamp: string
  status: 'executed' | 'pending' | 'cancelled'
}

// 股息记录
export interface Dividend {
  symbol: string
  name: string
  amount: number
  exDate: string
  payDate: string
  shares: number
  total: number
}