/**
 * 性能监控服务
 * 监控页面加载时间、API响应时间、用户交互性能等
 */

interface PerformanceMetric {
  name: string
  value: number
  timestamp: number
  url?: string
  userAgent?: string
}

interface PageLoadMetrics {
  domContentLoaded: number
  loadComplete: number
  firstContentfulPaint: number
  largestContentfulPaint: number
  firstInputDelay: number
  cumulativeLayoutShift: number
}

interface APIMetrics {
  url: string
  method: string
  duration: number
  status: number
  timestamp: number
}

class PerformanceMonitorService {
  private metrics: PerformanceMetric[] = []
  private apiMetrics: APIMetrics[] = []
  private isEnabled = true
  private reportInterval = 30000 // 30秒上报一次

  constructor() {
    this.init()
  }

  /**
   * 初始化性能监控
   */
  private init(): void {
    if (!this.isEnabled || typeof window === 'undefined') return

    // 监控页面加载性能
    this.monitorPageLoad()

    // 监控Core Web Vitals
    this.monitorWebVitals()

    // 监控资源加载
    this.monitorResourceLoad()

    // 定期上报数据
    this.startReporting()

    console.log('📊 性能监控服务已启动')
  }

  /**
   * 监控页面加载性能
   */
  private monitorPageLoad(): void {
    if (document.readyState === 'complete') {
      this.collectPageLoadMetrics()
    } else {
      window.addEventListener('load', () => {
        // 延迟收集，确保所有指标都可用
        setTimeout(() => this.collectPageLoadMetrics(), 1000)
      })
    }
  }

  /**
   * 收集页面加载指标
   */
  private collectPageLoadMetrics(): void {
    const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
    if (!navigation) return

    const metrics: PageLoadMetrics = {
      domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
      loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
      firstContentfulPaint: 0,
      largestContentfulPaint: 0,
      firstInputDelay: 0,
      cumulativeLayoutShift: 0
    }

    // 收集Paint Timing
    const paintEntries = performance.getEntriesByType('paint')
    paintEntries.forEach(entry => {
      if (entry.name === 'first-contentful-paint') {
        metrics.firstContentfulPaint = entry.startTime
      }
    })

    this.recordMetric('page_load_dom_content_loaded', metrics.domContentLoaded)
    this.recordMetric('page_load_complete', metrics.loadComplete)
    this.recordMetric('page_load_fcp', metrics.firstContentfulPaint)

    console.log('📊 页面加载性能指标:', metrics)
  }

  /**
   * 监控Core Web Vitals
   */
  private monitorWebVitals(): void {
    // LCP (Largest Contentful Paint)
    this.observePerformanceEntry('largest-contentful-paint', (entry) => {
      this.recordMetric('web_vitals_lcp', entry.startTime)
    })

    // FID (First Input Delay) - 通过事件监听器模拟
    let firstInputProcessed = false
    const firstInputHandler = (event: Event) => {
      if (firstInputProcessed) return
      firstInputProcessed = true

      const now = performance.now()
      this.recordMetric('web_vitals_fid', now)

      // 移除监听器
      const eventTypes = ['mousedown', 'keydown', 'touchstart']
      // 检查浏览器是否支持 pointerdown 事件
      if ('PointerEvent' in window) {
        eventTypes.push('pointerdown')
      }

      eventTypes.forEach(type => {
        document.removeEventListener(type, firstInputHandler, true)
      })
    }

    // 添加首次输入监听器
    const eventTypes = ['mousedown', 'keydown', 'touchstart']
    // 检查浏览器是否支持 pointerdown 事件
    if ('PointerEvent' in window) {
      eventTypes.push('pointerdown')
    }

    eventTypes.forEach(type => {
      document.addEventListener(type, firstInputHandler, true)
    })

    // CLS (Cumulative Layout Shift)
    this.observePerformanceEntry('layout-shift', (entry: any) => {
      if (!entry.hadRecentInput) {
        this.recordMetric('web_vitals_cls', entry.value)
      }
    })
  }

  /**
   * 监控资源加载
   */
  private monitorResourceLoad(): void {
    // 监控现有资源
    const resources = performance.getEntriesByType('resource')
    resources.forEach(resource => {
      this.analyzeResourceTiming(resource as PerformanceResourceTiming)
    })

    // 监控新资源
    this.observePerformanceEntry('resource', (entry) => {
      this.analyzeResourceTiming(entry as PerformanceResourceTiming)
    })
  }

  /**
   * 分析资源加载时间
   */
  private analyzeResourceTiming(resource: PerformanceResourceTiming): void {
    const duration = resource.responseEnd - resource.startTime
    const resourceType = this.getResourceType(resource.name)

    this.recordMetric(`resource_load_${resourceType}`, duration, resource.name)

    // 记录慢资源
    if (duration > 3000) { // 超过3秒的资源
      console.warn(`🐌 慢资源加载: ${resource.name} (${duration.toFixed(2)}ms)`)
    }
  }

  /**
   * 获取资源类型
   */
  private getResourceType(url: string): string {
    if (url.includes('.js')) return 'script'
    if (url.includes('.css')) return 'stylesheet'
    if (url.match(/\.(png|jpg|jpeg|gif|svg|webp)$/)) return 'image'
    if (url.includes('/api/')) return 'api'
    return 'other'
  }

  /**
   * 观察性能条目
   */
  private observePerformanceEntry(type: string, callback: (entry: PerformanceEntry) => void): void {
    if (!('PerformanceObserver' in window)) return

    try {
      const observer = new PerformanceObserver((list) => {
        list.getEntries().forEach(callback)
      })
      observer.observe({ entryTypes: [type] })
    } catch (error) {
      console.warn(`无法观察性能条目类型: ${type}`, error)
    }
  }

  /**
   * 记录API性能指标
   */
  recordAPIMetric(url: string, method: string, duration: number, status: number): void {
    const metric: APIMetrics = {
      url,
      method,
      duration,
      status,
      timestamp: Date.now()
    }

    this.apiMetrics.push(metric)
    this.recordMetric('api_response_time', duration, url)

    // 记录慢API
    if (duration > 5000) { // 超过5秒的API
      console.warn(`🐌 慢API请求: ${method} ${url} (${duration.toFixed(2)}ms)`)
    }

    // 记录错误API
    if (status >= 400) {
      console.error(`❌ API错误: ${method} ${url} (状态码: ${status})`)
    }
  }

  /**
   * 记录自定义性能指标
   */
  recordMetric(name: string, value: number, url?: string): void {
    const metric: PerformanceMetric = {
      name,
      value,
      timestamp: Date.now(),
      url,
      userAgent: navigator.userAgent
    }

    this.metrics.push(metric)

    // 限制内存中的指标数量
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-500)
    }

    // 性能优化：检查阈值并触发优化
    this.checkPerformanceThresholds(name, value)
  }

  /**
   * 检查性能阈值并触发优化
   */
  private checkPerformanceThresholds(name: string, value: number): void {
    const thresholds = {
      'api_response_time': 2000, // 2秒
      'web_vitals_lcp': 2500, // 2.5秒
      'web_vitals_fid': 100, // 100ms
      'web_vitals_cls': 0.1, // 0.1
      'resource_load_script': 3000, // 3秒
      'resource_load_stylesheet': 2000, // 2秒
      'page_load_complete': 5000 // 5秒
    }

    if (thresholds[name] && value > thresholds[name]) {
      console.warn(`⚠️ 性能警告: ${name} 超过阈值 ${thresholds[name]}, 当前值: ${value}`)
      this.triggerOptimization(name, value)
    }
  }

  /**
   * 触发性能优化
   */
  private triggerOptimization(metricName: string, value: number): void {
    switch (metricName) {
      case 'api_response_time':
        this.optimizeAPIRequests()
        break
      case 'web_vitals_lcp':
      case 'page_load_complete':
        this.optimizePageLoad()
        break
      case 'resource_load_script':
      case 'resource_load_stylesheet':
        this.optimizeResourceLoading()
        break
      default:
        console.log(`🔧 触发通用优化策略: ${metricName}`)
    }
  }

  /**
   * 优化API请求
   */
  private optimizeAPIRequests(): void {
    try {
      // 启用请求去重
      this.enableRequestDeduplication()

      // 启用请求缓存
      this.enableRequestCaching()

      console.log('🚀 API请求优化完成')
    } catch (error) {
      console.error('❌ API请求优化失败:', error)
    }
  }

  /**
   * 优化页面加载
   */
  private optimizePageLoad(): void {
    try {
      // 预加载关键资源
      this.preloadCriticalResources()

      // 延迟加载非关键资源
      this.lazyLoadNonCriticalResources()

      console.log('🚀 页面加载优化完成')
    } catch (error) {
      console.error('❌ 页面加载优化失败:', error)
    }
  }

  /**
   * 优化资源加载
   */
  private optimizeResourceLoading(): void {
    try {
      // 压缩和合并资源
      this.optimizeResourceCompression()

      // 使用CDN加速
      this.enableCDNAcceleration()

      console.log('🚀 资源加载优化完成')
    } catch (error) {
      console.error('❌ 资源加载优化失败:', error)
    }
  }

  /**
   * 开始性能数据上报
   */
  private startReporting(): void {
    setInterval(() => {
      this.reportMetrics()
    }, this.reportInterval)
  }

  /**
   * 上报性能指标
   */
  private reportMetrics(): void {
    if (this.metrics.length === 0 && this.apiMetrics.length === 0) return

    const report = {
      timestamp: Date.now(),
      url: window.location.href,
      userAgent: navigator.userAgent,
      metrics: this.metrics.splice(0), // 清空已上报的指标
      apiMetrics: this.apiMetrics.splice(0),
      memoryUsage: this.getMemoryUsage(),
      connectionInfo: this.getConnectionInfo()
    }

    // 在开发环境中输出到控制台
    if (import.meta.env.DEV) {
      console.log('📊 性能报告:', report)
    }

    // 在生产环境中可以发送到监控服务
    // this.sendToMonitoringService(report)
  }

  /**
   * 获取内存使用情况
   */
  private getMemoryUsage(): any {
    if ('memory' in performance) {
      const memory = (performance as any).memory
      return {
        usedJSHeapSize: memory.usedJSHeapSize,
        totalJSHeapSize: memory.totalJSHeapSize,
        jsHeapSizeLimit: memory.jsHeapSizeLimit
      }
    }
    return null
  }

  /**
   * 获取网络连接信息
   */
  private getConnectionInfo(): any {
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt
      }
    }
    return null
  }

  /**
   * 获取性能摘要
   */
  getPerformanceSummary(): any {
    const now = Date.now()
    const recentMetrics = this.metrics.filter(m => now - m.timestamp < 300000) // 最近5分钟

    const summary = {
      totalMetrics: recentMetrics.length,
      avgPageLoadTime: this.calculateAverage(recentMetrics, 'page_load_complete'),
      avgAPIResponseTime: this.calculateAverage(this.apiMetrics, 'duration'),
      slowAPIs: this.apiMetrics.filter(api => api.duration > 5000).length,
      errorAPIs: this.apiMetrics.filter(api => api.status >= 400).length
    }

    return summary
  }

  /**
   * 计算平均值
   */
  private calculateAverage(data: any[], field: string): number {
    if (data.length === 0) return 0
    const sum = data.reduce((acc, item) => acc + (item[field] || 0), 0)
    return sum / data.length
  }

  /**
   * 启用请求去重
   */
  private enableRequestDeduplication(): void {
    if ((window as any).__requestDeduplicationEnabled) return

    const pendingRequests = new Map()
    const originalFetch = window.fetch

    window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
      const key = `${input.toString()}_${JSON.stringify(init)}`

      if (pendingRequests.has(key)) {
        return pendingRequests.get(key)
      }

      const promise = originalFetch(input, init).finally(() => {
        pendingRequests.delete(key)
      })

      pendingRequests.set(key, promise)
      return promise
    }

    ;(window as any).__requestDeduplicationEnabled = true
  }

  /**
   * 启用请求缓存
   */
  private enableRequestCaching(): void {
    if ((window as any).__requestCachingEnabled) return

    const cache = new Map()
    const CACHE_DURATION = 5 * 60 * 1000 // 5分钟

    const originalFetch = window.fetch
    window.fetch = function(input: RequestInfo | URL, init?: RequestInit) {
      // 只缓存GET请求
      if (init?.method && init.method !== 'GET') {
        return originalFetch(input, init)
      }

      const key = input.toString()
      const cached = cache.get(key)

      if (cached && Date.now() - cached.timestamp < CACHE_DURATION) {
        return Promise.resolve(cached.response.clone())
      }

      return originalFetch(input, init).then(response => {
        if (response.ok) {
          cache.set(key, {
            response: response.clone(),
            timestamp: Date.now()
          })
        }
        return response
      })
    }

    ;(window as any).__requestCachingEnabled = true
  }

  /**
   * 预加载关键资源
   */
  private preloadCriticalResources(): void {
    const criticalResources = [
      '/api/v1/market/realtime',
      '/api/v1/trading/positions',
      '/api/v1/user/profile'
    ]

    criticalResources.forEach(url => {
      const link = document.createElement('link')
      link.rel = 'prefetch'
      link.href = url
      document.head.appendChild(link)
    })
  }

  /**
   * 延迟加载非关键资源
   */
  private lazyLoadNonCriticalResources(): void {
    // 延迟加载图片
    const images = document.querySelectorAll('img[data-src]')
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target as HTMLImageElement
          img.src = img.dataset.src || ''
          img.removeAttribute('data-src')
          imageObserver.unobserve(img)
        }
      })
    })

    images.forEach(img => imageObserver.observe(img))
  }

  /**
   * 优化资源压缩
   */
  private optimizeResourceCompression(): void {
    // 检查是否支持Brotli压缩
    if (navigator.userAgent.includes('Chrome') || navigator.userAgent.includes('Firefox')) {
      console.log('🗜️ 浏览器支持Brotli压缩')
    }

    // 启用gzip压缩提示
    console.log('💡 建议服务器启用gzip/Brotli压缩')
  }

  /**
   * 启用CDN加速
   */
  private enableCDNAcceleration(): void {
    // 检查静态资源是否使用CDN
    const scripts = document.querySelectorAll('script[src]')
    const stylesheets = document.querySelectorAll('link[rel="stylesheet"]')

    let cdnUsage = 0
    const totalResources = scripts.length + stylesheets.length

    scripts.forEach(script => {
      const src = (script as HTMLScriptElement).src
      if (src.includes('cdn') || src.includes('unpkg') || src.includes('jsdelivr')) {
        cdnUsage++
      }
    })

    stylesheets.forEach(link => {
      const href = (link as HTMLLinkElement).href
      if (href.includes('cdn') || href.includes('unpkg') || href.includes('jsdelivr')) {
        cdnUsage++
      }
    })

    const cdnPercentage = totalResources > 0 ? (cdnUsage / totalResources) * 100 : 0
    console.log(`📡 CDN使用率: ${cdnPercentage.toFixed(1)}%`)

    if (cdnPercentage < 50) {
      console.log('💡 建议使用CDN加速静态资源加载')
    }
  }

  /**
   * 启用/禁用性能监控
   */
  setEnabled(enabled: boolean): void {
    this.isEnabled = enabled
    console.log(`📊 性能监控已${enabled ? '启用' : '禁用'}`)
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitorService()

// 导出类型
export type { PerformanceMetric, PageLoadMetrics, APIMetrics }
