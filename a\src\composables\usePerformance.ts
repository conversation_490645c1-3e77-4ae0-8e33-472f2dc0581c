/**
 * 性能优化组合式函数
 * 提供常用的性能优化功能
 * @deprecated 请使用 @/composables/core/usePerformance 代替
 */

// 重新导出core版本的性能监控函数，保持向后兼容
export { 
  usePerformance,
  useDebounce, 
  useThrottle,
  useVirtualScroll,
  useLazyImage as useImageLazyLoad,
  useComponentCache 
} from '@/composables/core/usePerformance'

// 保留一些旧版本函数名的兼容性导出
import { PerformanceMonitor } from '@/utils/performance'
import { ref, onMounted, onUnmounted } from 'vue'

/**
 * 使用性能监控 - 兼容旧版本
 * @deprecated 请使用 usePerformance 代替
 */
export function usePerformanceMonitor() {
  const mark = (name: string) => {
    PerformanceMonitor.mark(name)
  }

  const measure = (name: string, startMark: string) => {
    return PerformanceMonitor.measure(name, startMark)
  }

  const getMemoryUsage = () => {
    return PerformanceMonitor.getMemoryUsage()
  }

  const getFID = () => {
    return PerformanceMonitor.getFID()
  }

  return {
    mark,
    measure,
    getMemoryUsage,
    getFID
  }
}

/**
 * 使用懒加载 - 兼容旧版本
 * @deprecated 请使用 useLazyImage 代替
 */
export function useLazyLoad() {
  const isVisible = ref(false)
  const targetRef = ref<HTMLElement>()

  onMounted(() => {
    if (!targetRef.value) return

    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            isVisible.value = true
            observer.unobserve(entry.target)
          }
        })
      },
      {
        rootMargin: '50px'
      }
    )

    observer.observe(targetRef.value)

    onUnmounted(() => {
      observer.disconnect()
    })
  })

  return {
    isVisible,
    targetRef
  }
}
