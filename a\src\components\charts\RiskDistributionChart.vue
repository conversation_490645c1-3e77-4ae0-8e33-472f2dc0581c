<template>
  <div ref="chartRef" :style="{ height: height }"></div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch } from 'vue'
import * as echarts from 'echarts'

const props = defineProps<{
  data: Array<{ name: string; value: number }>
  chartType: 'pie' | 'bar'
  height?: string
}>()

const chartRef = ref<HTMLElement>()
let chartInstance: echarts.ECharts | null = null

const initChart = () => {
  if (!chartRef.value) return
  
  chartInstance = echarts.init(chartRef.value)
  updateChart()
}

const updateChart = () => {
  if (!chartInstance) return
  
  const option: echarts.EChartsOption = props.chartType === 'pie' ? {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      right: 10,
      top: 'center',
      data: props.data.map(item => item.name)
    },
    series: [
      {
        name: '风险分布',
        type: 'pie',
        radius: ['40%', '70%'],
        avoidLabelOverlap: false,
        itemStyle: {
          borderRadius: 10,
          borderColor: '#fff',
          borderWidth: 2
        },
        label: {
          show: false,
          position: 'center'
        },
        emphasis: {
          label: {
            show: true,
            fontSize: 20,
            fontWeight: 'bold'
          }
        },
        labelLine: {
          show: false
        },
        data: props.data.map(item => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: getColorByName(item.name)
          }
        }))
      }
    ]
  } : {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: props.data.map(item => item.name),
      axisTick: {
        alignWithLabel: true
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: '{value}%'
      }
    },
    series: [
      {
        name: '风险占比',
        type: 'bar',
        barWidth: '60%',
        data: props.data.map(item => ({
          value: item.value,
          itemStyle: {
            color: getColorByName(item.name)
          }
        }))
      }
    ]
  }
  
  chartInstance.setOption(option)
}

const getColorByName = (name: string) => {
  const colorMap: Record<string, string> = {
    '市场风险': '#5470c6',
    '信用风险': '#91cc75',
    '流动性风险': '#fac858',
    '操作风险': '#ee6666',
    '系统风险': '#73c0de',
    '法律风险': '#3ba272',
    '声誉风险': '#fc8452',
    '其他风险': '#9a60b4'
  }
  return colorMap[name] || '#5470c6'
}

const handleResize = () => {
  chartInstance?.resize()
}

watch(() => [props.data, props.chartType], () => {
  updateChart()
}, { deep: true })

onMounted(() => {
  initChart()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
  chartInstance?.dispose()
})

defineOptions({
  name: 'RiskDistributionChart'
})
</script>