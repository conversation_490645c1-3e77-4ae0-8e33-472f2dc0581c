#!/usr/bin/env node

/**
 * 简化的前端开发服务器启动脚本
 * 用于解决复杂配置导致的启动问题
 */

const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')

console.log('🚀 启动量化投资平台前端开发服务器...')

// 检查必要文件
const requiredFiles = [
  'package.json',
  'vite.config.ts',
  'src/main.ts',
  'src/App.vue',
  'index.html'
]

console.log('📋 检查必要文件...')
for (const file of requiredFiles) {
  if (!fs.existsSync(path.join(__dirname, file))) {
    console.error(`❌ 缺少必要文件: ${file}`)
    process.exit(1)
  }
}
console.log('✅ 所有必要文件检查通过')

// 检查node_modules
if (!fs.existsSync(path.join(__dirname, 'node_modules'))) {
  console.log('📦 node_modules不存在，正在安装依赖...')
  const install = spawn('npm', ['install'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  })
  
  install.on('close', (code) => {
    if (code === 0) {
      console.log('✅ 依赖安装完成')
      startDevServer()
    } else {
      console.error('❌ 依赖安装失败')
      process.exit(1)
    }
  })
} else {
  startDevServer()
}

function startDevServer() {
  console.log('🌐 启动Vite开发服务器...')
  
  const vite = spawn('npx', ['vite', '--host', '0.0.0.0', '--port', '5173'], {
    cwd: __dirname,
    stdio: 'inherit',
    shell: true
  })
  
  vite.on('error', (err) => {
    console.error('❌ 启动失败:', err.message)
  })
  
  vite.on('close', (code) => {
    if (code !== 0) {
      console.error(`❌ 服务器异常退出，代码: ${code}`)
    }
  })
  
  // 优雅关闭
  process.on('SIGINT', () => {
    console.log('\n🛑 正在关闭开发服务器...')
    vite.kill('SIGINT')
    process.exit(0)
  })
}
