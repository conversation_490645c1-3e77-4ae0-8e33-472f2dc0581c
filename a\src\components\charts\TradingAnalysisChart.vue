<template>
  <div class="trading-analysis-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-controls">
        <el-button-group>
          <el-button 
            v-for="view in views" 
            :key="view.value"
            :type="selectedView === view.value ? 'primary' : 'default'"
            size="small"
            @click="changeView(view.value)"
          >
            {{ view.label }}
          </el-button>
        </el-button-group>
      </div>
    </div>
    
    <div class="chart-container">
      <v-chart 
        :option="chartOption" 
        :loading="loading"
        :loading-options="loadingOptions"
        autoresize
      />
    </div>
    
    <!-- 交易统计 -->
    <div class="trading-stats" v-if="statistics">
      <div class="stats-section">
        <h4>交易统计</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">总交易次数</span>
            <span class="stat-value">{{ statistics.totalTrades }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">盈利交易</span>
            <span class="stat-value positive">{{ statistics.profitableTrades }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">亏损交易</span>
            <span class="stat-value negative">{{ statistics.losingTrades }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">胜率</span>
            <span class="stat-value" :class="getWinRateClass(statistics.winRate)">
              {{ formatPercent(statistics.winRate) }}
            </span>
          </div>
        </div>
      </div>
      
      <div class="stats-section">
        <h4>盈亏分析</h4>
        <div class="stats-grid">
          <div class="stat-item">
            <span class="stat-label">平均盈利</span>
            <span class="stat-value positive">{{ formatCurrency(statistics.avgProfit) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">平均亏损</span>
            <span class="stat-value negative">{{ formatCurrency(statistics.avgLoss) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">盈亏比</span>
            <span class="stat-value" :class="getProfitRateClass(statistics.profitFactor)">
              {{ statistics.profitFactor?.toFixed(2) }}
            </span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大单笔盈利</span>
            <span class="stat-value positive">{{ formatCurrency(statistics.maxProfit) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">最大单笔亏损</span>
            <span class="stat-value negative">{{ formatCurrency(statistics.maxLoss) }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">连续盈利最多</span>
            <span class="stat-value">{{ statistics.maxConsecutiveWins }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'
import { formatPercent, formatCurrency } from '@/utils/format'

// ECharts 组件已在全局 plugins/echarts.ts 中注册，无需重复注册

interface Trade {
  date: string
  type: 'buy' | 'sell'
  price: number
  quantity: number
  profit: number
  symbol: string
  duration: number
}

interface TradingStatistics {
  totalTrades: number
  profitableTrades: number
  losingTrades: number
  winRate: number
  avgProfit: number
  avgLoss: number
  profitFactor: number
  maxProfit: number
  maxLoss: number
  maxConsecutiveWins: number
  maxConsecutiveLosses: number
  avgTradeDuration: number
}

interface Props {
  title?: string
  trades: Trade[]
  statistics?: TradingStatistics
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '交易分析',
  loading: false
})

const selectedView = ref('profit_loss')
const views = [
  { label: '盈亏分布', value: 'profit_loss' },
  { label: '交易频率', value: 'frequency' },
  { label: '持仓时间', value: 'duration' },
  { label: '累计盈亏', value: 'cumulative' }
]

const loadingOptions = {
  text: '分析中...',
  color: '#409EFF',
  textColor: '#409EFF',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0
}

// 图表选项
const chartOption = computed(() => {
  switch (selectedView.value) {
    case 'profit_loss':
      return getProfitLossOption()
    case 'frequency':
      return getFrequencyOption()
    case 'duration':
      return getDurationOption()
    case 'cumulative':
      return getCumulativeOption()
    default:
      return {}
  }
})

// 盈亏分布图
const getProfitLossOption = () => {
  const profits = props.trades.map(trade => trade.profit)
  const dates = props.trades.map(trade => trade.date)
  
  // 分为盈利和亏损
  const profitData = profits.map((profit, index) => profit > 0 ? profit : 0)
  const lossData = profits.map((profit, index) => profit < 0 ? profit : 0)

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    legend: {
      data: ['盈利', '亏损']
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatCurrency(value, 0)
      }
    },
    series: [
      {
        name: '盈利',
        type: 'bar',
        stack: 'total',
        data: profitData,
        itemStyle: {
          color: '#67C23A'
        }
      },
      {
        name: '亏损',
        type: 'bar',
        stack: 'total',
        data: lossData,
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }
}

// 交易频率图
const getFrequencyOption = () => {
  // 按月统计交易频率
  const monthlyTrades = new Map<string, number>()
  
  props.trades.forEach(trade => {
    const month = trade.date.substring(0, 7) // YYYY-MM
    monthlyTrades.set(month, (monthlyTrades.get(month) || 0) + 1)
  })
  
  const months = Array.from(monthlyTrades.keys()).sort()
  const frequencies = months.map(month => monthlyTrades.get(month) || 0)

  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: months
    },
    yAxis: {
      type: 'value',
      name: '交易次数'
    },
    series: [
      {
        name: '交易频率',
        type: 'line',
        data: frequencies,
        smooth: true,
        lineStyle: {
          width: 3,
          color: '#409EFF'
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(64, 158, 255, 0.3)'
            }, {
              offset: 1,
              color: 'rgba(64, 158, 255, 0.1)'
            }]
          }
        }
      }
    ]
  }
}

// 持仓时间分布图
const getDurationOption = () => {
  const durations = props.trades.map(trade => trade.duration)
  
  // 分组统计
  const bins = [
    { label: '1天内', min: 0, max: 1 },
    { label: '1-3天', min: 1, max: 3 },
    { label: '3-7天', min: 3, max: 7 },
    { label: '1-2周', min: 7, max: 14 },
    { label: '2-4周', min: 14, max: 28 },
    { label: '1个月以上', min: 28, max: Infinity }
  ]
  
  const distribution = bins.map(bin => ({
    name: bin.label,
    value: durations.filter(d => d > bin.min && d <= bin.max).length
  }))

  return {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    legend: {
      orient: 'vertical',
      left: 'left'
    },
    series: [
      {
        name: '持仓时间',
        type: 'pie',
        radius: '50%',
        data: distribution,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

// 累计盈亏图
const getCumulativeOption = () => {
  let cumulative = 0
  const cumulativeData = props.trades.map(trade => {
    cumulative += trade.profit
    return cumulative
  })
  
  const dates = props.trades.map(trade => trade.date)

  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '3%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      axisLabel: {
        formatter: (value: number) => formatCurrency(value, 0)
      }
    },
    series: [
      {
        name: '累计盈亏',
        type: 'line',
        data: cumulativeData,
        smooth: true,
        lineStyle: {
          width: 3
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(64, 158, 255, 0.3)'
            }, {
              offset: 1,
              color: 'rgba(64, 158, 255, 0.1)'
            }]
          }
        },
        markLine: {
          data: [
            { type: 'average', name: '平均值' }
          ]
        }
      }
    ]
  }
}

// 方法
const changeView = (view: string) => {
  selectedView.value = view
}

const getWinRateClass = (winRate: number) => {
  if (winRate >= 0.6) return 'excellent'
  if (winRate >= 0.5) return 'good'
  return 'neutral'
}

const getProfitRateClass = (profitFactor: number) => {
  if (profitFactor >= 2) return 'excellent'
  if (profitFactor >= 1.5) return 'good'
  if (profitFactor >= 1) return 'neutral'
  return 'poor'
}
</script>

<style scoped>
.trading-analysis-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #ebeef5;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-container {
  flex: 1;
  min-height: 400px;
  padding: 20px;
}

.trading-stats {
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #ebeef5;
}

.stats-section {
  margin-bottom: 24px;
}

.stats-section:last-child {
  margin-bottom: 0;
}

.stats-section h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.stat-value.positive {
  color: #67c23a;
}

.stat-value.negative {
  color: #f56c6c;
}

.stat-value.excellent {
  color: #67c23a;
}

.stat-value.good {
  color: #e6a23c;
}

.stat-value.neutral {
  color: #909399;
}

.stat-value.poor {
  color: #f56c6c;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
}
</style>