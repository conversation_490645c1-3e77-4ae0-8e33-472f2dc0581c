/**
 * 真实用户测试脚本
 * 模拟用户操作，检测平台问题
 */

const puppeteer = require('puppeteer');

class UserTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5173';
    this.testResults = {
      timestamp: new Date().toISOString(),
      userExperience: {},
      functionalIssues: [],
      performanceIssues: [],
      uiIssues: [],
      dataIssues: []
    };
  }

  async init() {
    console.log('🚀 启动用户测试...');
    this.browser = await puppeteer.launch({
      headless: false,
      devtools: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
    
    // 监听控制台错误
    this.page.on('console', msg => {
      if (msg.type() === 'error') {
        this.testResults.functionalIssues.push({
          type: 'JavaScript Error',
          message: msg.text(),
          timestamp: new Date().toISOString()
        });
      }
    });

    // 监听网络请求失败
    this.page.on('requestfailed', request => {
      this.testResults.dataIssues.push({
        type: 'Network Request Failed',
        url: request.url(),
        error: request.failure().errorText,
        timestamp: new Date().toISOString()
      });
    });
  }

  async testHomePage() {
    console.log('📊 测试首页...');
    await this.page.goto(this.baseUrl);
    await this.page.waitForTimeout(3000);

    // 检查页面标题
    const title = await this.page.title();
    console.log(`页面标题: ${title}`);

    // 检查是否有导航菜单
    const navLinks = await this.page.$$('.nav-link');
    console.log(`导航链接数量: ${navLinks.length}`);

    // 检查是否有数据显示
    const metricCards = await this.page.$$('.metric-card');
    console.log(`指标卡片数量: ${metricCards.length}`);

    // 截图
    await this.page.screenshot({ 
      path: `test-screenshots/homepage-${Date.now()}.png`,
      fullPage: true 
    });

    return {
      title,
      navLinksCount: navLinks.length,
      metricCardsCount: metricCards.length
    };
  }

  async testMarketPage() {
    console.log('📈 测试市场行情页面...');
    await this.page.goto(`${this.baseUrl}/market`);
    await this.page.waitForTimeout(3000);

    // 检查是否有股票列表
    const stockItems = await this.page.$$('.stock-item, .market-item, .quote-item');
    console.log(`股票项目数量: ${stockItems.length}`);

    // 检查是否有图表
    const charts = await this.page.$$('canvas, .echarts-container, .chart-container');
    console.log(`图表数量: ${charts.length}`);

    // 检查是否有实时数据更新
    const priceElements = await this.page.$$('.price, .current-price, .last-price');
    console.log(`价格元素数量: ${priceElements.length}`);

    await this.page.screenshot({ 
      path: `test-screenshots/market-${Date.now()}.png`,
      fullPage: true 
    });

    return {
      stockItemsCount: stockItems.length,
      chartsCount: charts.length,
      priceElementsCount: priceElements.length
    };
  }

  async testTradingPage() {
    console.log('💰 测试交易页面...');
    await this.page.goto(`${this.baseUrl}/trading`);
    await this.page.waitForTimeout(3000);

    // 检查交易表单
    const buyButton = await this.page.$('.buy-button, button[type="submit"]');
    const sellButton = await this.page.$('.sell-button');
    const orderForm = await this.page.$('form, .order-form, .trading-form');

    console.log(`买入按钮: ${buyButton ? '存在' : '不存在'}`);
    console.log(`卖出按钮: ${sellButton ? '存在' : '不存在'}`);
    console.log(`订单表单: ${orderForm ? '存在' : '不存在'}`);

    // 尝试模拟下单操作
    if (orderForm) {
      try {
        // 查找股票代码输入框
        const symbolInput = await this.page.$('input[placeholder*="股票"], input[placeholder*="代码"], input[name="symbol"]');
        if (symbolInput) {
          await symbolInput.type('000001');
        }

        // 查找数量输入框
        const quantityInput = await this.page.$('input[placeholder*="数量"], input[name="quantity"]');
        if (quantityInput) {
          await quantityInput.type('100');
        }

        console.log('✅ 成功填写交易表单');
      } catch (error) {
        this.testResults.functionalIssues.push({
          type: 'Trading Form Error',
          message: error.message,
          timestamp: new Date().toISOString()
        });
      }
    }

    await this.page.screenshot({ 
      path: `test-screenshots/trading-${Date.now()}.png`,
      fullPage: true 
    });

    return {
      hasBuyButton: !!buyButton,
      hasSellButton: !!sellButton,
      hasOrderForm: !!orderForm
    };
  }

  async testBacktestPage() {
    console.log('🔄 测试回测页面...');
    await this.page.goto(`${this.baseUrl}/backtest`);
    await this.page.waitForTimeout(3000);

    // 检查回测配置
    const configForm = await this.page.$('.backtest-config, .strategy-config, form');
    const startButton = await this.page.$('.start-backtest, .run-backtest, button[type="submit"]');
    const resultArea = await this.page.$('.backtest-result, .result-container, .performance-metrics');

    console.log(`配置表单: ${configForm ? '存在' : '不存在'}`);
    console.log(`开始按钮: ${startButton ? '存在' : '不存在'}`);
    console.log(`结果区域: ${resultArea ? '存在' : '不存在'}`);

    await this.page.screenshot({ 
      path: `test-screenshots/backtest-${Date.now()}.png`,
      fullPage: true 
    });

    return {
      hasConfigForm: !!configForm,
      hasStartButton: !!startButton,
      hasResultArea: !!resultArea
    };
  }

  async runFullTest() {
    try {
      await this.init();

      console.log('🧪 开始全面用户测试...');
      
      const homePageResult = await this.testHomePage();
      const marketPageResult = await this.testMarketPage();
      const tradingPageResult = await this.testTradingPage();
      const backtestPageResult = await this.testBacktestPage();

      this.testResults.userExperience = {
        homePage: homePageResult,
        marketPage: marketPageResult,
        tradingPage: tradingPageResult,
        backtestPage: backtestPageResult
      };

      // 生成测试报告
      await this.generateReport();

    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
      this.testResults.functionalIssues.push({
        type: 'Test Execution Error',
        message: error.message,
        timestamp: new Date().toISOString()
      });
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async generateReport() {
    const reportPath = `user-test-report-${Date.now()}.json`;
    require('fs').writeFileSync(reportPath, JSON.stringify(this.testResults, null, 2));
    
    console.log('\n📋 用户测试报告:');
    console.log('================');
    console.log(`✅ 首页导航链接: ${this.testResults.userExperience.homePage?.navLinksCount || 0}`);
    console.log(`✅ 首页指标卡片: ${this.testResults.userExperience.homePage?.metricCardsCount || 0}`);
    console.log(`📈 市场股票项目: ${this.testResults.userExperience.marketPage?.stockItemsCount || 0}`);
    console.log(`📊 市场图表数量: ${this.testResults.userExperience.marketPage?.chartsCount || 0}`);
    console.log(`💰 交易表单可用: ${this.testResults.userExperience.tradingPage?.hasOrderForm ? '是' : '否'}`);
    console.log(`🔄 回测配置可用: ${this.testResults.userExperience.backtestPage?.hasConfigForm ? '是' : '否'}`);
    console.log(`❌ JavaScript错误: ${this.testResults.functionalIssues.length}`);
    console.log(`🌐 网络请求失败: ${this.testResults.dataIssues.length}`);
    console.log(`\n📄 详细报告已保存到: ${reportPath}`);
  }
}

// 运行测试
const tester = new UserTester();
tester.runFullTest().catch(console.error);
