<template>
  <div class="low-freq-trading">
    <!-- 顶部状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <h2 class="module-title">
          <el-icon><TrendCharts /></el-icon>
          低频交易系统
        </h2>
        <div class="system-status">
          <el-tag :type="systemStatus === 'active' ? 'success' : 'info'" size="small">
            <el-icon><Monitor /></el-icon>
            {{ systemStatus === 'active' ? '活跃中' : '待机中' }}
          </el-tag>
          <span class="active-strategies">活跃策略: {{ activeStrategiesCount }}</span>
          <span class="total-positions">总持仓: {{ totalPositions }}</span>
        </div>
      </div>
      <div class="status-right">
        <el-button-group>
          <el-button
            type="primary"
            @click="showStrategyWizard = true"
          >
            <el-icon><Plus /></el-icon>
            策略向导
          </el-button>
          <el-button @click="showPortfolioAnalysis = true">
            <el-icon><PieChart /></el-icon>
            组合分析
          </el-button>
          <el-button @click="showBacktest = true">
            <el-icon><DataAnalysis /></el-icon>
            回测分析
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <el-row :gutter="16">
        <!-- 左侧：策略管理 -->
        <el-col :span="6">
          <el-card class="strategy-management-panel">
            <template #header>
              <div class="card-header">
                <span>策略管理</span>
                <el-dropdown @command="handleStrategyAction">
                  <el-button size="small">
                    <el-icon><MoreFilled /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="create">新建策略</el-dropdown-item>
                      <el-dropdown-item command="import">导入策略</el-dropdown-item>
                      <el-dropdown-item command="template">策略模板</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>

            <div class="strategy-list">
              <div
                v-for="strategy in strategies"
                :key="strategy.id"
                class="strategy-card"
                :class="{ active: selectedStrategy?.id === strategy.id }"
                @click="selectStrategy(strategy)"
              >
                <div class="strategy-header">
                  <div class="strategy-name">{{ strategy.name }}</div>
                  <el-switch
                    v-model="strategy.enabled"
                    size="small"
                    @change="toggleStrategy(strategy)"
                  />
                </div>
                
                <div class="strategy-info">
                  <div class="info-row">
                    <span class="label">类型:</span>
                    <span class="value">{{ strategy.type }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">周期:</span>
                    <span class="value">{{ strategy.frequency }}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">收益率:</span>
                    <span class="value" :class="strategy.returnRate >= 0 ? 'positive' : 'negative'">
                      {{ strategy.returnRate >= 0 ? '+' : '' }}{{ strategy.returnRate.toFixed(2) }}%
                    </span>
                  </div>
                  <div class="info-row">
                    <span class="label">最大回撤:</span>
                    <span class="value negative">{{ strategy.maxDrawdown.toFixed(2) }}%</span>
                  </div>
                </div>

                <div class="strategy-progress">
                  <div class="progress-label">
                    <span>策略进度</span>
                    <span>{{ strategy.progress }}%</span>
                  </div>
                  <el-progress
                    :percentage="strategy.progress"
                    :stroke-width="4"
                    :show-text="false"
                    :color="getProgressColor(strategy.progress)"
                  />
                </div>

                <div class="strategy-actions">
                  <el-button-group size="small">
                    <el-button @click.stop="editStrategy(strategy)">编辑</el-button>
                    <el-button @click.stop="backtestStrategy(strategy)">回测</el-button>
                    <el-button @click.stop="deleteStrategy(strategy)" type="danger">删除</el-button>
                  </el-button-group>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 中间：策略详情和图表 -->
        <el-col :span="12">
          <div class="center-panels">
            <!-- 策略详情 -->
            <el-card class="strategy-detail-panel" v-if="selectedStrategy">
              <template #header>
                <div class="card-header">
                  <span>{{ selectedStrategy.name }} - 详细信息</span>
                  <el-button-group size="small">
                    <el-button @click="refreshStrategy">
                      <el-icon><Refresh /></el-icon>
                    </el-button>
                    <el-button @click="optimizeStrategy">
                      <el-icon><Tools /></el-icon>
                      优化
                    </el-button>
                  </el-button-group>
                </div>
              </template>

              <div class="strategy-metrics">
                <el-row :gutter="12">
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-label">总收益</div>
                      <div class="metric-value positive">{{ formatCurrency(selectedStrategy.totalReturn) }}</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-label">年化收益率</div>
                      <div class="metric-value" :class="selectedStrategy.annualizedReturn >= 0 ? 'positive' : 'negative'">
                        {{ selectedStrategy.annualizedReturn.toFixed(2) }}%
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-label">夏普比率</div>
                      <div class="metric-value">{{ selectedStrategy.sharpeRatio.toFixed(2) }}</div>
                    </div>
                  </el-col>
                  <el-col :span="6">
                    <div class="metric-item">
                      <div class="metric-label">胜率</div>
                      <div class="metric-value">{{ selectedStrategy.winRate.toFixed(1) }}%</div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <div class="strategy-chart">
                <div class="chart-header">
                  <span>收益曲线</span>
                  <el-button-group size="small">
                    <el-button
                      v-for="period in chartPeriods"
                      :key="period.value"
                      :type="selectedChartPeriod === period.value ? 'primary' : 'default'"
                      @click="selectedChartPeriod = period.value"
                      size="small"
                    >
                      {{ period.label }}
                    </el-button>
                  </el-button-group>
                </div>
                <div class="chart-container">
                  <!-- 这里应该集成真实的图表库 -->
                  <div class="mock-chart">
                    <div class="chart-line"></div>
                    <div class="chart-info">收益曲线图表 ({{ selectedChartPeriod }})</div>
                  </div>
                </div>
              </div>

              <div class="strategy-parameters">
                <div class="parameters-header">
                  <span>策略参数</span>
                  <el-button size="small" @click="showParameterEditor = true">
                    <el-icon><Edit /></el-icon>
                    编辑参数
                  </el-button>
                </div>
                <div class="parameters-grid">
                  <div
                    v-for="(value, key) in selectedStrategy.parameters"
                    :key="key"
                    class="parameter-item"
                  >
                    <div class="parameter-name">{{ key }}</div>
                    <div class="parameter-value">{{ value }}</div>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 默认状态 -->
            <el-card class="empty-strategy-panel" v-else>
              <div class="empty-content">
                <el-icon class="empty-icon"><FolderOpened /></el-icon>
                <h3>请选择一个策略</h3>
                <p>选择左侧策略以查看详细信息和图表</p>
              </div>
            </el-card>
          </div>
        </el-col>

        <!-- 右侧：市场分析和工具 -->
        <el-col :span="6">
          <div class="right-panels">
            <!-- 市场概览 -->
            <el-card class="market-overview-panel">
              <template #header>
                <span>市场概览</span>
              </template>
              
              <div class="market-indices">
                <div v-for="index in marketIndices" :key="index.code" class="index-item">
                  <div class="index-header">
                    <span class="index-name">{{ index.name }}</span>
                    <span class="index-code">{{ index.code }}</span>
                  </div>
                  <div class="index-price">
                    <span class="current-price">{{ index.price.toFixed(2) }}</span>
                    <span class="price-change" :class="index.change >= 0 ? 'positive' : 'negative'">
                      {{ index.change >= 0 ? '+' : '' }}{{ index.change.toFixed(2) }}
                      ({{ index.changePercent.toFixed(2) }}%)
                    </span>
                  </div>
                </div>
              </div>

              <div class="market-sentiment">
                <div class="sentiment-header">市场情绪</div>
                <div class="sentiment-indicators">
                  <div class="sentiment-item">
                    <span class="label">VIX指数</span>
                    <span class="value">{{ marketSentiment.vix.toFixed(2) }}</span>
                  </div>
                  <div class="sentiment-item">
                    <span class="label">涨跌比</span>
                    <span class="value">{{ marketSentiment.advanceDeclineRatio.toFixed(2) }}</span>
                  </div>
                  <div class="sentiment-item">
                    <span class="label">资金流入</span>
                    <span class="value" :class="marketSentiment.moneyFlow >= 0 ? 'positive' : 'negative'">
                      {{ formatCurrency(marketSentiment.moneyFlow) }}
                    </span>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 交易工具 -->
            <el-card class="trading-tools-panel">
              <template #header>
                <span>交易工具</span>
              </template>

              <div class="tools-list">
                <div class="tool-category">
                  <h4>策略工具</h4>
                  <div class="tool-buttons">
                    <el-button size="small" @click="showStrategyOptimizer = true" block>
                      <el-icon><Tools /></el-icon>
                      策略优化器
                    </el-button>
                    <el-button size="small" @click="showRiskAnalyzer = true" block>
                      <el-icon><Warning /></el-icon>
                      风险分析器
                    </el-button>
                    <el-button size="small" @click="showCorrelationMatrix = true" block>
                      <el-icon><Grid /></el-icon>
                      相关性矩阵
                    </el-button>
                  </div>
                </div>

                <div class="tool-category">
                  <h4>技术分析</h4>
                  <div class="tool-buttons">
                    <el-button size="small" @click="showTechnicalScanner = true" block>
                      <el-icon><Search /></el-icon>
                      技术选股器
                    </el-button>
                    <el-button size="small" @click="showPatternRecognition = true" block>
                      <el-icon><Camera /></el-icon>
                      形态识别
                    </el-button>
                    <el-button size="small" @click="showIndicatorBuilder = true" block>
                      <el-icon><Connection /></el-icon>
                      指标构建器
                    </el-button>
                  </div>
                </div>

                <div class="tool-category">
                  <h4>报告工具</h4>
                  <div class="tool-buttons">
                    <el-button size="small" @click="generatePerformanceReport" block>
                      <el-icon><Document /></el-icon>
                      绩效报告
                    </el-button>
                    <el-button size="small" @click="generateRiskReport" block>
                      <el-icon><DocumentCopy /></el-icon>
                      风险报告
                    </el-button>
                    <el-button size="small" @click="exportData" block>
                      <el-icon><Download /></el-icon>
                      导出数据
                    </el-button>
                  </div>
                </div>
              </div>
            </el-card>
          </div>
        </el-col>
      </el-row>

      <!-- 底部：持仓和订单 -->
      <div class="bottom-panels">
        <el-card class="positions-orders-panel">
          <template #header>
            <div class="card-header">
              <el-tabs v-model="bottomActiveTab" @tab-change="handleBottomTabChange">
                <el-tab-pane label="当前持仓" name="positions">
                  <template #label>
                    <span>当前持仓 ({{ positions.length }})</span>
                  </template>
                </el-tab-pane>
                <el-tab-pane label="委托订单" name="orders">
                  <template #label>
                    <span>委托订单 ({{ orders.length }})</span>
                  </template>
                </el-tab-pane>
                <el-tab-pane label="成交记录" name="trades">
                  <template #label>
                    <span>成交记录 ({{ trades.length }})</span>
                  </template>
                </el-tab-pane>
              </el-tabs>
              
              <div class="tab-actions">
                <el-button size="small" @click="refreshBottomData">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
                <el-button size="small" @click="exportBottomData">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
              </div>
            </div>
          </template>

          <div class="tab-content">
            <!-- 持仓列表 -->
            <div v-if="bottomActiveTab === 'positions'" class="positions-table">
              <el-table :data="positions" stripe size="small" max-height="200">
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="name" label="名称" width="120" />
                <el-table-column prop="quantity" label="数量" width="80" align="right" />
                <el-table-column prop="avgPrice" label="均价" width="80" align="right">
                  <template #default="{ row }">{{ row.avgPrice.toFixed(2) }}</template>
                </el-table-column>
                <el-table-column prop="currentPrice" label="现价" width="80" align="right">
                  <template #default="{ row }">{{ row.currentPrice.toFixed(2) }}</template>
                </el-table-column>
                <el-table-column label="盈亏" width="100" align="right">
                  <template #default="{ row }">
                    <span :class="row.pnl >= 0 ? 'positive' : 'negative'">
                      {{ formatCurrency(row.pnl) }}
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="盈亏率" width="80" align="right">
                  <template #default="{ row }">
                    <span :class="row.pnlPercent >= 0 ? 'positive' : 'negative'">
                      {{ row.pnlPercent.toFixed(2) }}%
                    </span>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template #default="{ row }">
                    <el-button-group size="small">
                      <el-button @click="sellPosition(row)" size="small">卖出</el-button>
                      <el-button @click="adjustPosition(row)" size="small">调仓</el-button>
                    </el-button-group>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 订单列表 -->
            <div v-if="bottomActiveTab === 'orders'" class="orders-table">
              <el-table :data="orders" stripe size="small" max-height="200">
                <el-table-column prop="orderTime" label="时间" width="120" />
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="side" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'" size="small">
                      {{ row.side === 'buy' ? '买' : '卖' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" align="right" />
                <el-table-column prop="price" label="价格" width="80" align="right">
                  <template #default="{ row }">{{ row.price.toFixed(2) }}</template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="80">
                  <template #default="{ row }">
                    <el-tag :type="getOrderStatusType(row.status)" size="small">
                      {{ getOrderStatusText(row.status) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="strategy" label="策略" width="100" />
                <el-table-column label="操作" width="80">
                  <template #default="{ row }">
                    <el-button
                      v-if="row.status === 'pending'"
                      @click="cancelOrder(row)"
                      size="small"
                      type="danger"
                    >
                      撤单
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 成交记录 -->
            <div v-if="bottomActiveTab === 'trades'" class="trades-table">
              <el-table :data="trades" stripe size="small" max-height="200">
                <el-table-column prop="tradeTime" label="时间" width="120" />
                <el-table-column prop="symbol" label="代码" width="80" />
                <el-table-column prop="side" label="方向" width="60">
                  <template #default="{ row }">
                    <el-tag :type="row.side === 'buy' ? 'danger' : 'success'" size="small">
                      {{ row.side === 'buy' ? '买' : '卖' }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="quantity" label="数量" width="80" align="right" />
                <el-table-column prop="price" label="价格" width="80" align="right">
                  <template #default="{ row }">{{ row.price.toFixed(2) }}</template>
                </el-table-column>
                <el-table-column prop="amount" label="金额" width="100" align="right">
                  <template #default="{ row }">{{ formatCurrency(row.amount) }}</template>
                </el-table-column>
                <el-table-column prop="strategy" label="策略" width="100" />
                <el-table-column prop="commission" label="手续费" width="80" align="right">
                  <template #default="{ row }">{{ row.commission.toFixed(2) }}</template>
                </el-table-column>
              </el-table>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 各种弹窗 -->
    <!-- 策略向导 -->
    <el-dialog
      v-model="showStrategyWizard"
      title="策略创建向导"
      width="800px"
      :close-on-click-modal="false"
    >
      <StrategyWizard
        @strategy-created="handleStrategyCreated"
        @cancel="showStrategyWizard = false"
      />
    </el-dialog>

    <!-- 参数编辑器 -->
    <el-dialog
      v-model="showParameterEditor"
      title="参数编辑"
      width="600px"
      :close-on-click-modal="false"
    >
      <ParameterEditor
        v-if="selectedStrategy"
        :strategy="selectedStrategy"
        @parameters-updated="handleParametersUpdated"
        @cancel="showParameterEditor = false"
      />
    </el-dialog>

    <!-- 组合分析 -->
    <el-dialog
      v-model="showPortfolioAnalysis"
      title="投资组合分析"
      width="1000px"
      :close-on-click-modal="false"
    >
      <PortfolioAnalysis
        :strategies="strategies"
        :positions="positions"
        @close="showPortfolioAnalysis = false"
      />
    </el-dialog>

    <!-- 回测分析 -->
    <el-dialog
      v-model="showBacktest"
      title="策略回测"
      width="900px"
      :close-on-click-modal="false"
    >
      <BacktestEngine
        :strategy="selectedStrategy"
        @backtest-complete="handleBacktestComplete"
        @close="showBacktest = false"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  TrendCharts, Monitor, Plus, PieChart, DataAnalysis, MoreFilled,
  Refresh, Tools, Edit, FolderOpened, Search, Camera, Connection,
  Document, DocumentCopy, Download, Grid, Warning
} from '@element-plus/icons-vue'

// Props
interface Props {
  account: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'strategy-update': [strategy: any]
  'order-placed': [order: any]
}>()

// 状态数据
const systemStatus = ref<'active' | 'idle'>('active')
const selectedStrategy = ref(null)
const selectedChartPeriod = ref('1d')
const bottomActiveTab = ref('positions')

// 弹窗状态
const showStrategyWizard = ref(false)
const showParameterEditor = ref(false)
const showPortfolioAnalysis = ref(false)
const showBacktest = ref(false)
const showStrategyOptimizer = ref(false)
const showRiskAnalyzer = ref(false)
const showCorrelationMatrix = ref(false)
const showTechnicalScanner = ref(false)
const showPatternRecognition = ref(false)
const showIndicatorBuilder = ref(false)

// 策略数据
const strategies = ref([
  {
    id: 'lft_001',
    name: '价值投资策略',
    type: '价值投资',
    frequency: '周调仓',
    enabled: true,
    returnRate: 12.5,
    maxDrawdown: -8.2,
    progress: 75,
    totalReturn: 125000,
    annualizedReturn: 15.8,
    sharpeRatio: 1.24,
    winRate: 65.5,
    parameters: {
      'PE阈值': '< 15',
      'PB阈值': '< 2.0',
      '最小市值': '50亿',
      '最大持仓数': 20,
      '单股权重': '5%'
    }
  },
  {
    id: 'lft_002',
    name: '动量轮动策略',
    type: '行业轮动',
    frequency: '月调仓',
    enabled: true,
    returnRate: 8.3,
    maxDrawdown: -12.5,
    progress: 60,
    totalReturn: 83000,
    annualizedReturn: 10.2,
    sharpeRatio: 0.89,
    winRate: 58.2,
    parameters: {
      '动量周期': '20日',
      '轮动数量': '5个行业',
      '调仓频率': '月度',
      '止损比例': '10%'
    }
  },
  {
    id: 'lft_003',
    name: '均值回归策略',
    type: '均值回归',
    frequency: '双周调仓',
    enabled: false,
    returnRate: -2.1,
    maxDrawdown: -15.8,
    progress: 30,
    totalReturn: -21000,
    annualizedReturn: -4.2,
    sharpeRatio: -0.25,
    winRate: 42.8,
    parameters: {
      '回归周期': '60日',
      '偏离阈值': '2倍标准差',
      '持仓期限': '15日',
      '风控比例': '8%'
    }
  }
])

// 市场指数数据
const marketIndices = ref([
  {
    name: '上证指数',
    code: '000001',
    price: 3256.89,
    change: 15.26,
    changePercent: 0.47
  },
  {
    name: '深证成指',
    code: '399001',
    price: 12458.52,
    change: -28.75,
    changePercent: -0.23
  },
  {
    name: '创业板指',
    code: '399006',
    price: 2645.18,
    change: 8.92,
    changePercent: 0.34
  }
])

// 市场情绪数据
const marketSentiment = reactive({
  vix: 18.45,
  advanceDeclineRatio: 1.25,
  moneyFlow: 2850000000
})

// 持仓数据
const positions = ref([
  {
    symbol: '000001',
    name: '平安银行',
    quantity: 1000,
    avgPrice: 12.50,
    currentPrice: 13.25,
    pnl: 750,
    pnlPercent: 6.0
  },
  {
    symbol: '000002',
    name: '万科A',
    quantity: 500,
    avgPrice: 18.20,
    currentPrice: 17.80,
    pnl: -200,
    pnlPercent: -2.2
  }
])

// 订单数据
const orders = ref([
  {
    orderTime: '09:30:15',
    symbol: '600036',
    side: 'buy',
    quantity: 1000,
    price: 45.60,
    status: 'pending',
    strategy: '价值投资策略'
  },
  {
    orderTime: '09:25:30',
    symbol: '000858',
    side: 'sell',
    quantity: 500,
    price: 28.90,
    status: 'filled',
    strategy: '动量轮动策略'
  }
])

// 成交记录
const trades = ref([
  {
    tradeTime: '09:25:30',
    symbol: '000858',
    side: 'sell',
    quantity: 500,
    price: 28.90,
    amount: 14450,
    strategy: '动量轮动策略',
    commission: 7.23
  },
  {
    tradeTime: '09:20:45',
    symbol: '002415',
    side: 'buy',
    quantity: 1000,
    price: 35.20,
    amount: 35200,
    strategy: '价值投资策略',
    commission: 17.60
  }
])

// 图表周期选项
const chartPeriods = [
  { label: '1天', value: '1d' },
  { label: '1周', value: '1w' },
  { label: '1月', value: '1m' },
  { label: '3月', value: '3m' },
  { label: '1年', value: '1y' }
]

// 计算属性
const activeStrategiesCount = computed(() => {
  return strategies.value.filter(s => s.enabled).length
})

const totalPositions = computed(() => {
  return positions.value.length
})

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('zh-CN', {
    style: 'currency',
    currency: 'CNY',
    minimumFractionDigits: 2
  }).format(amount)
}

// 方法
const selectStrategy = (strategy: any) => {
  selectedStrategy.value = strategy
}

const toggleStrategy = (strategy: any) => {
  const action = strategy.enabled ? '启用' : '停用'
  ElMessage.success(`策略 ${strategy.name} 已${action}`)
  emit('strategy-update', strategy)
}

const editStrategy = (strategy: any) => {
  selectedStrategy.value = strategy
  showParameterEditor.value = true
}

const backtestStrategy = (strategy: any) => {
  selectedStrategy.value = strategy
  showBacktest.value = true
}

const deleteStrategy = async (strategy: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除策略 "${strategy.name}" 吗？此操作不可撤销。`,
      '删除策略',
      {
        confirmButtonText: '确定删除',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    const index = strategies.value.findIndex(s => s.id === strategy.id)
    if (index !== -1) {
      strategies.value.splice(index, 1)
      if (selectedStrategy.value?.id === strategy.id) {
        selectedStrategy.value = null
      }
    }
    
    ElMessage.success('策略删除成功')
  } catch (error) {
    // 用户取消
  }
}

const getProgressColor = (progress: number) => {
  if (progress >= 80) return '#67c23a'
  if (progress >= 60) return '#e6a23c'
  if (progress >= 40) return '#409eff'
  return '#f56c6c'
}

const refreshStrategy = () => {
  ElMessage.success('策略数据已刷新')
}

const optimizeStrategy = () => {
  showStrategyOptimizer.value = true
}

const handleStrategyAction = (command: string) => {
  switch (command) {
    case 'create':
      showStrategyWizard.value = true
      break
    case 'import':
      ElMessage.info('导入策略功能开发中')
      break
    case 'template':
      ElMessage.info('策略模板功能开发中')
      break
  }
}

const handleBottomTabChange = (tab: string) => {
  bottomActiveTab.value = tab
}

const refreshBottomData = () => {
  ElMessage.success(`${bottomActiveTab.value}数据已刷新`)
}

const exportBottomData = () => {
  ElMessage.info('数据导出功能开发中')
}

const sellPosition = (position: any) => {
  ElMessage.info(`卖出 ${position.name} 功能开发中`)
}

const adjustPosition = (position: any) => {
  ElMessage.info(`调仓 ${position.name} 功能开发中`)
}

const cancelOrder = async (order: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要撤销订单 ${order.symbol} 吗？`,
      '撤销订单',
      {
        confirmButtonText: '确定撤销',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )
    
    order.status = 'cancelled'
    ElMessage.success('订单撤销成功')
  } catch (error) {
    // 用户取消
  }
}

const getOrderStatusType = (status: string) => {
  const types: Record<string, string> = {
    pending: 'warning',
    filled: 'success',
    cancelled: 'info',
    rejected: 'danger'
  }
  return types[status] || 'info'
}

const getOrderStatusText = (status: string) => {
  const texts: Record<string, string> = {
    pending: '待成交',
    filled: '已成交',
    cancelled: '已撤销',
    rejected: '已拒绝'
  }
  return texts[status] || status
}

// 工具函数
const generatePerformanceReport = () => {
  ElMessage.info('绩效报告生成功能开发中')
}

const generateRiskReport = () => {
  ElMessage.info('风险报告生成功能开发中')
}

const exportData = () => {
  ElMessage.info('数据导出功能开发中')
}

// 事件处理
const handleStrategyCreated = (strategy: any) => {
  strategies.value.push(strategy)
  showStrategyWizard.value = false
  ElMessage.success('策略创建成功')
}

const handleParametersUpdated = (updatedStrategy: any) => {
  const index = strategies.value.findIndex(s => s.id === updatedStrategy.id)
  if (index !== -1) {
    strategies.value[index] = updatedStrategy
    selectedStrategy.value = updatedStrategy
  }
  showParameterEditor.value = false
  ElMessage.success('参数更新成功')
}

const handleBacktestComplete = (result: any) => {
  ElMessage.success('回测完成')
  showBacktest.value = false
}

// 生命周期
onMounted(() => {
  console.log('低频交易模块已加载')
  
  // 模拟实时更新市场数据
  const marketUpdateTimer = setInterval(() => {
    marketIndices.value.forEach(index => {
      const changePercent = (Math.random() - 0.5) * 0.1
      const change = index.price * changePercent / 100
      index.change += change
      index.changePercent = (index.change / (index.price - index.change)) * 100
    })
  }, 5000)
  
  onUnmounted(() => {
    clearInterval(marketUpdateTimer)
  })
})
</script>

<style scoped lang="scss">
.low-freq-trading {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: white;
  border-bottom: 1px solid #e4e7ed;
  margin-bottom: 16px;

  .status-left {
    display: flex;
    align-items: center;
    gap: 24px;

    .module-title {
      display: flex;
      align-items: center;
      gap: 8px;
      margin: 0;
      font-size: 20px;
      font-weight: 600;
      color: #303133;
    }

    .system-status {
      display: flex;
      align-items: center;
      gap: 12px;
      font-size: 14px;

      .active-strategies, .total-positions {
        color: #606266;
        font-weight: 500;
      }
    }
  }
}

.main-content {
  flex: 1;
  overflow: auto;
}

.strategy-management-panel {
  height: 400px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .strategy-list {
    height: 320px;
    overflow-y: auto;

    .strategy-card {
      border: 1px solid #e4e7ed;
      border-radius: 8px;
      padding: 12px;
      margin-bottom: 12px;
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
      }

      &.active {
        border-color: #409eff;
        background: #f0f9ff;
      }

      .strategy-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .strategy-name {
          font-weight: 600;
          color: #303133;
        }
      }

      .strategy-info {
        margin-bottom: 8px;

        .info-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          font-size: 13px;

          .label {
            color: #606266;
          }

          .value {
            color: #303133;
            font-weight: 500;

            &.positive {
              color: #67c23a;
            }
            &.negative {
              color: #f56c6c;
            }
          }
        }
      }

      .strategy-progress {
        margin-bottom: 8px;

        .progress-label {
          display: flex;
          justify-content: space-between;
          margin-bottom: 4px;
          font-size: 12px;
          color: #606266;
        }
      }

      .strategy-actions {
        display: flex;
        justify-content: center;
      }
    }
  }
}

.strategy-detail-panel {
  height: 400px;
  margin-bottom: 16px;

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .strategy-metrics {
    margin-bottom: 16px;

    .metric-item {
      text-align: center;
      padding: 8px;
      background: #f8f9fa;
      border-radius: 6px;

      .metric-label {
        font-size: 12px;
        color: #606266;
        margin-bottom: 4px;
      }

      .metric-value {
        font-size: 16px;
        font-weight: 600;
        color: #303133;

        &.positive {
          color: #67c23a;
        }
        &.negative {
          color: #f56c6c;
        }
      }
    }
  }

  .strategy-chart {
    margin-bottom: 16px;

    .chart-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .chart-container {
      height: 120px;

      .mock-chart {
        height: 100%;
        background: #f8f9fa;
        border-radius: 6px;
        display: flex;
        align-items: center;
        justify-content: center;
        position: relative;

        .chart-line {
          width: 80%;
          height: 2px;
          background: linear-gradient(90deg, #f56c6c, #409eff, #67c23a);
          border-radius: 1px;
        }

        .chart-info {
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          font-size: 12px;
          color: #606266;
        }
      }
    }
  }

  .strategy-parameters {
    .parameters-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;
    }

    .parameters-grid {
      display: grid;
      grid-template-columns: 1fr 1fr;
      gap: 8px;

      .parameter-item {
        display: flex;
        justify-content: space-between;
        padding: 6px 8px;
        background: #f8f9fa;
        border-radius: 4px;
        font-size: 13px;

        .parameter-name {
          color: #606266;
        }

        .parameter-value {
          color: #303133;
          font-weight: 500;
        }
      }
    }
  }
}

.empty-strategy-panel {
  height: 400px;
  margin-bottom: 16px;

  .empty-content {
    height: 320px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-align: center;

    .empty-icon {
      font-size: 64px;
      color: #c0c4cc;
      margin-bottom: 16px;
    }

    h3 {
      margin: 0 0 8px 0;
      color: #303133;
    }

    p {
      margin: 0;
      color: #606266;
    }
  }
}

.market-overview-panel {
  height: 200px;
  margin-bottom: 16px;

  .market-indices {
    margin-bottom: 16px;

    .index-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 6px 0;
      border-bottom: 1px solid #f0f0f0;

      &:last-child {
        border-bottom: none;
      }

      .index-header {
        display: flex;
        flex-direction: column;
        gap: 2px;

        .index-name {
          font-weight: 500;
          color: #303133;
          font-size: 14px;
        }

        .index-code {
          font-size: 12px;
          color: #909399;
        }
      }

      .index-price {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        gap: 2px;

        .current-price {
          font-weight: 600;
          color: #303133;
        }

        .price-change {
          font-size: 12px;

          &.positive {
            color: #f56c6c;
          }
          &.negative {
            color: #67c23a;
          }
        }
      }
    }
  }

  .market-sentiment {
    .sentiment-header {
      font-weight: 600;
      color: #303133;
      margin-bottom: 8px;
      font-size: 14px;
    }

    .sentiment-indicators {
      .sentiment-item {
        display: flex;
        justify-content: space-between;
        padding: 4px 0;
        font-size: 13px;

        .label {
          color: #606266;
        }

        .value {
          color: #303133;
          font-weight: 500;

          &.positive {
            color: #67c23a;
          }
          &.negative {
            color: #f56c6c;
          }
        }
      }
    }
  }
}

.trading-tools-panel {
  height: 180px;

  .tools-list {
    .tool-category {
      margin-bottom: 12px;

      h4 {
        margin: 0 0 6px 0;
        font-size: 13px;
        color: #303133;
        border-bottom: 1px solid #e4e7ed;
        padding-bottom: 4px;
      }

      .tool-buttons {
        display: flex;
        flex-direction: column;
        gap: 4px;
      }
    }
  }
}

.bottom-panels {
  margin-top: 16px;

  .positions-orders-panel {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .tab-actions {
        display: flex;
        gap: 8px;
      }
    }

    .tab-content {
      .positive {
        color: #67c23a;
      }
      .negative {
        color: #f56c6c;
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .main-content .el-row .el-col {
    margin-bottom: 16px;
  }
}
</style>