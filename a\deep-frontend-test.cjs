const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class FrontendTester {
  constructor() {
    this.browser = null;
    this.page = null;
    this.baseUrl = 'http://localhost:5174';
    this.testResults = {
      timestamp: new Date().toISOString(),
      totalTests: 0,
      passedTests: 0,
      failedTests: 0,
      errors: [],
      warnings: [],
      pages: {},
      performance: {}
    };
  }

  async init() {
    console.log('🚀 启动Puppeteer浏览器...');
    this.browser = await puppeteer.launch({
      headless: false, // 设为false以便观察测试过程
      devtools: true,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    });
    
    this.page = await this.browser.newPage();
    await this.page.setViewport({ width: 1920, height: 1080 });
    
    // 监听控制台消息
    this.page.on('console', msg => {
      const text = msg.text();
      const type = msg.type();
      
      if (type === 'error') {
        this.testResults.errors.push({
          type: 'console_error',
          message: text,
          timestamp: new Date().toISOString()
        });
      } else if (type === 'warning') {
        this.testResults.warnings.push({
          type: 'console_warning',
          message: text,
          timestamp: new Date().toISOString()
        });
      }
      
      console.log(`📝 [${type.toUpperCase()}] ${text}`);
    });
    
    // 监听请求失败
    this.page.on('requestfailed', request => {
      this.testResults.errors.push({
        type: 'request_failed',
        url: request.url(),
        error: request.failure().errorText,
        timestamp: new Date().toISOString()
      });
      console.log(`❌ 请求失败: ${request.url()} - ${request.failure().errorText}`);
    });
    
    // 监听页面错误
    this.page.on('pageerror', error => {
      this.testResults.errors.push({
        type: 'page_error',
        message: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      });
      console.log(`💥 页面错误: ${error.message}`);
    });
  }

  async testHomePage() {
    console.log('\n🏠 测试首页...');
    try {
      const startTime = Date.now();
      await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0', timeout: 30000 });
      const loadTime = Date.now() - startTime;
      
      // 截图
      await this.page.screenshot({ path: `test-screenshots/homepage-${Date.now()}.png` });
      
      // 检查基本元素
      const title = await this.page.title();
      const bodyContent = await this.page.evaluate(() => document.body.innerText);
      
      this.testResults.pages.homepage = {
        status: 'success',
        loadTime,
        title,
        hasContent: bodyContent.length > 0,
        screenshot: `homepage-${Date.now()}.png`
      };
      
      this.testResults.performance.homepage = loadTime;
      this.testResults.passedTests++;
      console.log(`✅ 首页加载成功 (${loadTime}ms)`);
      
    } catch (error) {
      this.testResults.pages.homepage = {
        status: 'failed',
        error: error.message
      };
      this.testResults.errors.push({
        type: 'homepage_load_error',
        message: error.message,
        timestamp: new Date().toISOString()
      });
      this.testResults.failedTests++;
      console.log(`❌ 首页加载失败: ${error.message}`);
    }
    this.testResults.totalTests++;
  }

  async testNavigation() {
    console.log('\n🧭 测试导航功能...');
    try {
      // 查找所有可能的导航链接
      const navLinks = await this.page.$$eval('a, [role="button"], .menu-item, .nav-item', elements => 
        elements.map(el => ({
          text: el.innerText?.trim() || el.textContent?.trim() || '',
          href: el.href || '',
          clickable: true
        })).filter(link => link.text && link.text.length > 0)
      );
      
      console.log(`📋 发现 ${navLinks.length} 个导航元素`);
      
      this.testResults.pages.navigation = {
        status: 'success',
        totalLinks: navLinks.length,
        links: navLinks.slice(0, 10) // 只记录前10个
      };
      
      this.testResults.passedTests++;
      
    } catch (error) {
      this.testResults.pages.navigation = {
        status: 'failed',
        error: error.message
      };
      this.testResults.failedTests++;
      console.log(`❌ 导航测试失败: ${error.message}`);
    }
    this.testResults.totalTests++;
  }

  async testRoutes() {
    console.log('\n🛣️ 测试路由页面...');
    const testRoutes = [
      '/dashboard',
      '/trading',
      '/strategy',
      '/backtest',
      '/market',
      '/portfolio',
      '/risk',
      '/settings'
    ];

    for (const route of testRoutes) {
      try {
        console.log(`📍 测试路由: ${route}`);
        const startTime = Date.now();
        
        await this.page.goto(`${this.baseUrl}${route}`, { 
          waitUntil: 'networkidle0', 
          timeout: 15000 
        });
        
        const loadTime = Date.now() - startTime;
        
        // 等待页面内容加载
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        const title = await this.page.title();
        const bodyContent = await this.page.evaluate(() => document.body.innerText);
        const hasContent = bodyContent.length > 100; // 判断是否有实际内容
        
        // 检查是否有错误页面标识
        const hasError = await this.page.$('.error-page, .not-found, [class*="error"], [class*="404"]');
        
        this.testResults.pages[route] = {
          status: hasError ? 'error' : 'success',
          loadTime,
          title,
          hasContent,
          contentLength: bodyContent.length
        };
        
        if (!hasError) {
          this.testResults.passedTests++;
          console.log(`  ✅ ${route} 加载成功 (${loadTime}ms)`);
        } else {
          this.testResults.failedTests++;
          console.log(`  ❌ ${route} 显示错误页面`);
        }
        
      } catch (error) {
        this.testResults.pages[route] = {
          status: 'failed',
          error: error.message,
          loadTime: -1
        };
        this.testResults.failedTests++;
        console.log(`  ❌ ${route} 访问失败: ${error.message}`);
      }
      this.testResults.totalTests++;
      
      // 短暂等待避免请求过快
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  async testInteractiveElements() {
    console.log('\n🖱️ 测试交互元素...');
    try {
      // 返回首页进行交互测试
      await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0' });
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 查找并测试按钮
      const buttons = await this.page.$$('button, [role="button"], .btn');
      console.log(`🔘 发现 ${buttons.length} 个按钮`);
      
      let clickableButtons = 0;
      for (let i = 0; i < Math.min(buttons.length, 5); i++) {
        try {
          const button = buttons[i];
          const isVisible = await button.isIntersectingViewport();
          if (isVisible) {
            await button.click();
            clickableButtons++;
            await new Promise(resolve => setTimeout(resolve, 500)); // 等待可能的动画或响应
          }
        } catch (error) {
          console.log(`  ⚠️ 按钮 ${i} 点击失败: ${error.message}`);
        }
      }
      
      // 查找并测试输入框
      const inputs = await this.page.$$('input, textarea, [contenteditable]');
      console.log(`📝 发现 ${inputs.length} 个输入元素`);
      
      let workingInputs = 0;
      for (let i = 0; i < Math.min(inputs.length, 3); i++) {
        try {
          const input = inputs[i];
          const isVisible = await input.isIntersectingViewport();
          if (isVisible) {
            await input.focus();
            await input.type('测试输入');
            workingInputs++;
            await new Promise(resolve => setTimeout(resolve, 300));
          }
        } catch (error) {
          console.log(`  ⚠️ 输入框 ${i} 测试失败: ${error.message}`);
        }
      }
      
      this.testResults.pages.interactive = {
        status: 'success',
        totalButtons: buttons.length,
        clickableButtons,
        totalInputs: inputs.length,
        workingInputs
      };
      
      this.testResults.passedTests++;
      console.log(`✅ 交互元素测试完成 - 按钮: ${clickableButtons}/${buttons.length}, 输入框: ${workingInputs}/${inputs.length}`);
      
    } catch (error) {
      this.testResults.pages.interactive = {
        status: 'failed',
        error: error.message
      };
      this.testResults.failedTests++;
      console.log(`❌ 交互测试失败: ${error.message}`);
    }
    this.testResults.totalTests++;
  }

  async testPerformance() {
    console.log('\n⚡ 测试性能指标...');
    try {
      await this.page.goto(this.baseUrl, { waitUntil: 'networkidle0' });
      
      const performanceMetrics = await this.page.evaluate(() => {
        const navigation = performance.getEntriesByType('navigation')[0];
        const paint = performance.getEntriesByType('paint');
        
        return {
          domContentLoaded: navigation.domContentLoadedEventEnd - navigation.domContentLoadedEventStart,
          loadComplete: navigation.loadEventEnd - navigation.loadEventStart,
          firstPaint: paint.find(p => p.name === 'first-paint')?.startTime || 0,
          firstContentfulPaint: paint.find(p => p.name === 'first-contentful-paint')?.startTime || 0,
          resourceCount: performance.getEntriesByType('resource').length
        };
      });
      
      this.testResults.performance = {
        ...this.testResults.performance,
        ...performanceMetrics
      };
      
      this.testResults.passedTests++;
      console.log(`✅ 性能测试完成 - FCP: ${performanceMetrics.firstContentfulPaint}ms`);
      
    } catch (error) {
      this.testResults.failedTests++;
      console.log(`❌ 性能测试失败: ${error.message}`);
    }
    this.testResults.totalTests++;
  }

  async generateReport() {
    const reportPath = path.join(__dirname, `deep-test-report-${Date.now()}.json`);
    const report = {
      ...this.testResults,
      summary: {
        successRate: (this.testResults.passedTests / this.testResults.totalTests * 100).toFixed(2) + '%',
        totalErrors: this.testResults.errors.length,
        totalWarnings: this.testResults.warnings.length,
        testDuration: new Date().toISOString()
      }
    };
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    
    console.log('\n📊 测试报告摘要:');
    console.log(`总测试数: ${this.testResults.totalTests}`);
    console.log(`通过: ${this.testResults.passedTests}`);
    console.log(`失败: ${this.testResults.failedTests}`);
    console.log(`成功率: ${report.summary.successRate}`);
    console.log(`错误数: ${this.testResults.errors.length}`);
    console.log(`警告数: ${this.testResults.warnings.length}`);
    console.log(`报告文件: ${reportPath}`);
    
    return reportPath;
  }

  async close() {
    if (this.browser) {
      await this.browser.close();
    }
  }

  async runFullTest() {
    try {
      await this.init();
      
      console.log('🔍 开始深度测试前端应用...');
      console.log(`🌐 测试地址: ${this.baseUrl}`);
      
      await this.testHomePage();
      await this.testNavigation();
      await this.testRoutes();
      await this.testInteractiveElements();
      await this.testPerformance();
      
      const reportPath = await this.generateReport();
      
      console.log('\n✅ 所有测试完成!');
      return reportPath;
      
    } catch (error) {
      console.error('💥 测试过程中出现严重错误:', error);
      throw error;
    } finally {
      await this.close();
    }
  }
}

// 运行测试
if (require.main === module) {
  const tester = new FrontendTester();
  tester.runFullTest()
    .then(reportPath => {
      console.log(`\n🎉 测试完成! 详细报告: ${reportPath}`);
      process.exit(0);
    })
    .catch(error => {
      console.error('❌ 测试失败:', error);
      process.exit(1);
    });
}

module.exports = FrontendTester;