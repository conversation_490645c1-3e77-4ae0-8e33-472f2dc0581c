<template>
  <div class="simple-layout">
    <header class="simple-header">
      <div class="header-content">
        <h1 class="app-title">🚀 量化投资平台</h1>
        <nav class="simple-nav">
          <router-link to="/" class="nav-link">🏠 首页</router-link>
          <router-link to="/dashboard" class="nav-link">📊 仪表盘</router-link>
          <router-link to="/market" class="nav-link">📈 市场</router-link>
          <router-link to="/trading" class="nav-link">💰 交易</router-link>
          <router-link to="/strategy" class="nav-link">🧠 策略</router-link>
          <router-link to="/backtest" class="nav-link">🔄 回测</router-link>
          <router-link to="/portfolio" class="nav-link">📋 投资组合</router-link>
          <router-link to="/risk" class="nav-link">🛡️ 风险</router-link>
          <router-link to="/safe-test" class="nav-link">🧪 测试</router-link>
        </nav>
      </div>
    </header>

    <main class="simple-main">
      <div class="content-container">
        <router-view v-slot="{ Component }">
          <transition name="fade" mode="out-in">
            <component :is="Component" />
          </transition>
        </router-view>
      </div>
    </main>

    <footer class="simple-footer">
      <p>&copy; 2024 量化投资平台. 版权所有.</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
// 简单布局不需要复杂的逻辑
console.log('✅ 简单布局组件已加载')
</script>

<style scoped>
.simple-layout {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.simple-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 1rem;
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: bold;
}

.simple-nav {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.nav-link {
  color: white;
  text-decoration: none;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
}

.nav-link:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateY(-1px);
}

.nav-link.router-link-active {
  background: rgba(255, 255, 255, 0.3);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.simple-main {
  flex: 1;
  background: #f8fafc;
}

.content-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem 1rem;
}

.simple-footer {
  background: #374151;
  color: white;
  text-align: center;
  padding: 1rem;
  margin-top: auto;
}

.simple-footer p {
  margin: 0;
}

/* 页面过渡动画 */
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .simple-nav {
    justify-content: center;
  }
  
  .nav-link {
    font-size: 0.9rem;
    padding: 0.4rem 0.8rem;
  }
  
  .content-container {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .app-title {
    font-size: 1.2rem;
  }
  
  .nav-link {
    font-size: 0.8rem;
    padding: 0.3rem 0.6rem;
  }
}
</style>