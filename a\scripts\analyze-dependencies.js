#!/usr/bin/env node

/**
 * 依赖分析脚本
 * 分析package.json中的依赖使用情况
 */

import fs from 'fs'
import path from 'path'
import { execSync } from 'child_process'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

class DependencyAnalyzer {
  constructor() {
    this.projectRoot = path.resolve(__dirname, '..')
    this.packageJson = JSON.parse(fs.readFileSync(path.join(this.projectRoot, 'package.json'), 'utf8'))
    this.srcPath = path.join(this.projectRoot, 'src')
    this.unusedDeps = []
    this.usedDeps = new Set()
  }

  // 检查依赖是否在代码中被使用
  isDependencyUsed(depName) {
    const searchPatterns = [
      `import.*from.*['"]${depName}['"]`,
      `import.*['"]${depName}['"]`,
      `require\\(['"]${depName}['"]\\)`,
      `from.*['"]${depName}['"]`,
      // 检查子模块导入
      `import.*from.*['"]${depName}/`,
      `import.*['"]${depName}/`,
      `require\\(['"]${depName}/`
    ]

    try {
      for (const pattern of searchPatterns) {
        const result = execSync(
          `grep -r -E "${pattern}" ${this.srcPath} --include="*.ts" --include="*.js" --include="*.vue" 2>/dev/null || true`,
          { encoding: 'utf8' }
        )
        if (result.trim()) {
          return true
        }
      }
      return false
    } catch (error) {
      console.warn(`检查依赖 ${depName} 时出错:`, error.message)
      return true // 出错时保守处理，认为被使用
    }
  }

  // 分析所有依赖
  analyzeDependencies() {
    console.log('🔍 开始分析依赖使用情况...\n')

    const dependencies = this.packageJson.dependencies || {}
    const devDependencies = this.packageJson.devDependencies || {}

    // 分析生产依赖
    console.log('📦 分析生产依赖:')
    for (const [depName, version] of Object.entries(dependencies)) {
      const isUsed = this.isDependencyUsed(depName)
      if (isUsed) {
        this.usedDeps.add(depName)
        console.log(`  ✅ ${depName}@${version}`)
      } else {
        this.unusedDeps.push({ name: depName, version, type: 'dependency' })
        console.log(`  ❌ ${depName}@${version} (未使用)`)
      }
    }

    console.log('\n🛠️ 分析开发依赖:')
    for (const [depName, version] of Object.entries(devDependencies)) {
      // 开发依赖通常在配置文件中使用，需要特殊处理
      const isUsed = this.isDependencyUsed(depName) || this.isDevDependencyUsed(depName)
      if (isUsed) {
        this.usedDeps.add(depName)
        console.log(`  ✅ ${depName}@${version}`)
      } else {
        this.unusedDeps.push({ name: depName, version, type: 'devDependency' })
        console.log(`  ❌ ${depName}@${version} (未使用)`)
      }
    }
  }

  // 检查开发依赖是否被使用（配置文件等）
  isDevDependencyUsed(depName) {
    const configFiles = [
      'vite.config.ts',
      'vitest.config.ts',
      'eslint.config.js',
      '.eslintrc.js',
      'tailwind.config.js',
      'postcss.config.js'
    ]

    for (const configFile of configFiles) {
      const configPath = path.join(this.projectRoot, configFile)
      if (fs.existsSync(configPath)) {
        const content = fs.readFileSync(configPath, 'utf8')
        if (content.includes(depName)) {
          return true
        }
      }
    }

    // 检查package.json scripts
    const scripts = this.packageJson.scripts || {}
    for (const script of Object.values(scripts)) {
      if (script.includes(depName)) {
        return true
      }
    }

    return false
  }

  // 生成报告
  generateReport() {
    console.log('\n📊 依赖分析报告:')
    console.log('=' * 50)

    console.log(`\n✅ 已使用的依赖: ${this.usedDeps.size} 个`)
    console.log(`❌ 未使用的依赖: ${this.unusedDeps.length} 个`)

    if (this.unusedDeps.length > 0) {
      console.log('\n🗑️ 建议移除的依赖:')
      for (const dep of this.unusedDeps) {
        console.log(`  - ${dep.name}@${dep.version} (${dep.type})`)
      }

      console.log('\n💡 移除命令:')
      const prodDeps = this.unusedDeps.filter(d => d.type === 'dependency').map(d => d.name)
      const devDeps = this.unusedDeps.filter(d => d.type === 'devDependency').map(d => d.name)

      if (prodDeps.length > 0) {
        console.log(`npm uninstall ${prodDeps.join(' ')}`)
      }
      if (devDeps.length > 0) {
        console.log(`npm uninstall --save-dev ${devDeps.join(' ')}`)
      }
    } else {
      console.log('\n🎉 所有依赖都在使用中！')
    }

    // 保存报告到文件
    const report = {
      timestamp: new Date().toISOString(),
      usedDependencies: Array.from(this.usedDeps),
      unusedDependencies: this.unusedDeps,
      summary: {
        total: this.usedDeps.size + this.unusedDeps.length,
        used: this.usedDeps.size,
        unused: this.unusedDeps.length
      }
    }

    fs.writeFileSync(
      path.join(this.projectRoot, 'dependency-analysis-report.json'),
      JSON.stringify(report, null, 2)
    )
    console.log('\n📄 详细报告已保存到: dependency-analysis-report.json')
  }

  // 运行分析
  run() {
    this.analyzeDependencies()
    this.generateReport()
  }
}

// 运行分析
const analyzer = new DependencyAnalyzer()
analyzer.run()
