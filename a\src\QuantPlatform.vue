<template>
  <div id="quant-platform">
    <div style="padding: 2rem; text-align: center; min-height: 100vh; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
      <div style="background: white; padding: 3rem; border-radius: 16px; box-shadow: 0 20px 40px rgba(0,0,0,0.1); max-width: 800px; margin: 0 auto;">
        <h1 style="color: #2c3e50; margin-bottom: 1rem;">🚀 量化投资平台</h1>
        <p style="color: #6b7280; margin-bottom: 2rem;">Vue应用已成功启动！</p>
        
        <div style="background: #e8f5e8; padding: 1rem; border-radius: 8px; border-left: 4px solid #10b981; margin-bottom: 2rem;">
          <p style="color: #065f46; font-weight: 500;">✅ 应用修复成功！</p>
          <p style="color: #047857; font-size: 0.9rem;">当前时间: {{ currentTime }}</p>
        </div>

        <nav style="margin-bottom: 2rem;">
          <button 
            v-for="page in pages" 
            :key="page.name"
            @click="currentPage = page.name"
            :style="buttonStyle(page.name)"
          >
            {{ page.emoji }} {{ page.title }}
          </button>
        </nav>

        <div v-if="currentPage === 'dashboard'" style="text-align: left;">
          <h2>📊 投资仪表盘</h2>
          <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem; margin-top: 1rem;">
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px;">
              <h3>总资产</h3>
              <div style="font-size: 1.5rem; font-weight: bold; color: #10b981;">¥1,234,567.89</div>
            </div>
            <div style="padding: 1rem; background: #f8fafc; border-radius: 8px;">
              <h3>今日收益</h3>
              <div style="font-size: 1.5rem; font-weight: bold; color: #3b82f6;">+12.34%</div>
            </div>
          </div>
        </div>

        <div v-if="currentPage === 'market'" style="text-align: left;">
          <h2>📈 市场行情</h2>
          <p>实时市场数据功能正常运行</p>
          <div style="background: #f0f9ff; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
            <p>上证指数：3,123.45 (+1.23%)</p>
            <p>深证成指：10,987.65 (+0.87%)</p>
          </div>
        </div>

        <div v-if="currentPage === 'trading'" style="text-align: left;">
          <h2>💰 智能交易</h2>
          <p>自动化交易系统已就绪</p>
          <div style="background: #fef3c7; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
            <p>当前持仓：5只股票</p>
            <p>今日交易：3笔</p>
          </div>
        </div>

        <div v-if="currentPage === 'strategy'" style="text-align: left;">
          <h2>🧠 策略研发</h2>
          <p>量化策略开发工具可用</p>
          <div style="background: #f3e8ff; padding: 1rem; border-radius: 8px; margin-top: 1rem;">
            <p>运行中策略：2个</p>
            <p>策略收益：+15.67%</p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'QuantPlatform',
  data() {
    return {
      currentTime: new Date().toLocaleString(),
      currentPage: 'dashboard',
      pages: [
        { name: 'dashboard', title: '仪表盘', emoji: '📊' },
        { name: 'market', title: '市场', emoji: '📈' },
        { name: 'trading', title: '交易', emoji: '💰' },
        { name: 'strategy', title: '策略', emoji: '🧠' }
      ]
    }
  },
  methods: {
    buttonStyle(pageName) {
      const isActive = this.currentPage === pageName
      return {
        padding: '8px 16px',
        margin: '0 8px 8px 0',
        border: 'none',
        borderRadius: '8px',
        cursor: 'pointer',
        backgroundColor: isActive ? '#667eea' : '#f3f4f6',
        color: isActive ? 'white' : '#374151',
        fontSize: '0.9rem'
      }
    }
  },
  mounted() {
    console.log('✅ Vue组件已挂载')
    setInterval(() => {
      this.currentTime = new Date().toLocaleString()
    }, 1000)
  }
}
</script>