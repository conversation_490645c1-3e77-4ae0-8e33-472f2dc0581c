<template>
  <div class="drawdown-chart">
    <div class="chart-header">
      <h3>{{ title }}</h3>
      <div class="chart-info">
        <el-tag v-if="maxDrawdown" type="danger" size="small">
          最大回撤: {{ formatPercent(maxDrawdown) }}
        </el-tag>
        <el-tag v-if="currentDrawdown" :type="getCurrentDrawdownType()" size="small">
          当前回撤: {{ formatPercent(currentDrawdown) }}
        </el-tag>
      </div>
    </div>
    
    <div class="chart-container">
      <v-chart 
        :option="chartOption" 
        :loading="loading"
        :loading-options="loadingOptions"
        autoresize
      />
    </div>
    
    <!-- 回撤统计 -->
    <div class="drawdown-stats" v-if="statistics">
      <div class="stats-grid">
        <div class="stat-item">
          <span class="stat-label">回撤次数</span>
          <span class="stat-value">{{ statistics.drawdownCount }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">平均回撤</span>
          <span class="stat-value">{{ formatPercent(statistics.avgDrawdown) }}</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">最长回撤期</span>
          <span class="stat-value">{{ statistics.maxDrawdownDuration }}天</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">当前回撤期</span>
          <span class="stat-value">{{ statistics.currentDrawdownDuration }}天</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">恢复时间</span>
          <span class="stat-value">{{ statistics.avgRecoveryTime }}天</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">Calmar比率</span>
          <span class="stat-value">{{ statistics.calmarRatio?.toFixed(2) || 'N/A' }}</span>
        </div>
      </div>
    </div>
    
    <!-- 回撤分布直方图 -->
    <div class="drawdown-distribution" v-if="showDistribution">
      <h4>回撤分布</h4>
      <div class="distribution-chart">
        <v-chart 
          :option="distributionOption" 
          style="height: 200px;"
          autoresize
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import VChart from 'vue-echarts'
import { formatPercent } from '@/utils/format'

// ECharts 组件已在全局 plugins/echarts.ts 中注册，无需重复注册

interface DrawdownPoint {
  date: string
  drawdown: number
  isInDrawdown: boolean
  peakValue: number
  currentValue: number
}

interface DrawdownStatistics {
  drawdownCount: number
  avgDrawdown: number
  maxDrawdownDuration: number
  currentDrawdownDuration: number
  avgRecoveryTime: number
  calmarRatio: number
}

interface Props {
  title?: string
  data: DrawdownPoint[]
  maxDrawdown?: number
  currentDrawdown?: number
  statistics?: DrawdownStatistics
  showDistribution?: boolean
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  title: '回撤分析',
  showDistribution: false,
  loading: false
})

const loadingOptions = {
  text: '分析中...',
  color: '#F56C6C',
  textColor: '#F56C6C',
  maskColor: 'rgba(255, 255, 255, 0.8)',
  zlevel: 0
}

// 计算图表选项
const chartOption = computed(() => {
  const dates = props.data.map(point => point.date)
  const drawdowns = props.data.map(point => point.drawdown * 100) // 转换为百分比
  
  // 找出回撤区间用于标记
  const drawdownPeriods = []
  let inDrawdown = false
  let startIndex = 0
  
  for (let i = 0; i < props.data.length; i++) {
    if (props.data[i].isInDrawdown && !inDrawdown) {
      inDrawdown = true
      startIndex = i
    } else if (!props.data[i].isInDrawdown && inDrawdown) {
      inDrawdown = false
      drawdownPeriods.push({
        xAxis: dates[startIndex],
        x2Axis: dates[i - 1]
      })
    }
  }
  
  // 如果最后还在回撤中
  if (inDrawdown) {
    drawdownPeriods.push({
      xAxis: dates[startIndex],
      x2Axis: dates[dates.length - 1]
    })
  }

  return {
    title: {
      show: false
    },
    tooltip: {
      trigger: 'axis',
      formatter: (params: any[]) => {
        const date = params[0].axisValueLabel
        const drawdown = params[0].value
        const point = props.data[params[0].dataIndex]
        
        return `
          <div style="margin-bottom: 5px; font-weight: bold;">${date}</div>
          <div>回撤: <span style="color: #F56C6C; font-weight: bold;">${drawdown.toFixed(2)}%</span></div>
          <div>峰值: <span style="color: #67C23A;">${point.peakValue.toLocaleString()}</span></div>
          <div>当前值: <span style="color: #409EFF;">${point.currentValue.toLocaleString()}</span></div>
        `
      }
    },
    grid: {
      left: '3%',
      right: '4%',
      top: '10%',
      bottom: '10%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: dates,
      axisLine: {
        onZero: false
      }
    },
    yAxis: {
      type: 'value',
      name: '回撤 (%)',
      max: 0,
      axisLabel: {
        formatter: '{value}%'
      },
      splitLine: {
        lineStyle: {
          type: 'dashed'
        }
      }
    },
    series: [
      {
        name: '回撤',
        type: 'line',
        data: drawdowns,
        lineStyle: {
          color: '#F56C6C',
          width: 2
        },
        areaStyle: {
          color: {
            type: 'linear',
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [{
              offset: 0,
              color: 'rgba(245, 108, 108, 0.5)'
            }, {
              offset: 1,
              color: 'rgba(245, 108, 108, 0.1)'
            }]
          }
        },
        markLine: {
          silent: true,
          data: [
            {
              yAxis: (props.maxDrawdown || 0) * 100,
              lineStyle: {
                color: '#E6A23C',
                type: 'dashed',
                width: 2
              },
              label: {
                show: true,
                formatter: '最大回撤: {c}%',
                position: 'insideEndTop'
              }
            }
          ]
        },
        markArea: {
          silent: true,
          data: drawdownPeriods.map(period => [
            {
              xAxis: period.xAxis,
              itemStyle: {
                color: 'rgba(245, 108, 108, 0.1)'
              }
            },
            {
              xAxis: period.x2Axis
            }
          ])
        }
      }
    ]
  }
})

// 回撤分布图表选项
const distributionOption = computed(() => {
  if (!props.data.length) return {}
  
  // 计算回撤分布
  const drawdowns = props.data
    .filter(point => point.drawdown < 0)
    .map(point => Math.abs(point.drawdown * 100))
  
  if (!drawdowns.length) return {}
  
  // 分组统计
  const bins = 10
  const max = Math.max(...drawdowns)
  const binSize = max / bins
  const distribution = new Array(bins).fill(0)
  const labels = []
  
  for (let i = 0; i < bins; i++) {
    const start = i * binSize
    const end = (i + 1) * binSize
    labels.push(`${start.toFixed(1)}-${end.toFixed(1)}%`)
    
    distribution[i] = drawdowns.filter(d => d >= start && d < end).length
  }

  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: labels,
      axisLabel: {
        rotate: 45
      }
    },
    yAxis: {
      type: 'value',
      name: '频次'
    },
    series: [
      {
        name: '回撤分布',
        type: 'bar',
        data: distribution,
        itemStyle: {
          color: '#F56C6C'
        }
      }
    ]
  }
})

// 方法
const getCurrentDrawdownType = () => {
  if (!props.currentDrawdown) return 'info'
  const absDrawdown = Math.abs(props.currentDrawdown)
  if (absDrawdown > 0.1) return 'danger'
  if (absDrawdown > 0.05) return 'warning'
  return 'info'
}
</script>

<style scoped>
.drawdown-chart {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px 12px;
  border-bottom: 1px solid #ebeef5;
}

.chart-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.chart-info {
  display: flex;
  gap: 8px;
}

.chart-container {
  flex: 1;
  min-height: 300px;
  padding: 20px;
}

.drawdown-stats {
  padding: 16px 20px;
  background: #fafbfc;
  border-top: 1px solid #ebeef5;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
}

.stat-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 4px;
  text-align: center;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.drawdown-distribution {
  padding: 16px 20px;
  border-top: 1px solid #ebeef5;
}

.drawdown-distribution h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.distribution-chart {
  width: 100%;
}

@media (max-width: 768px) {
  .chart-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .chart-info {
    width: 100%;
    justify-content: flex-start;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 12px;
  }
}
</style>