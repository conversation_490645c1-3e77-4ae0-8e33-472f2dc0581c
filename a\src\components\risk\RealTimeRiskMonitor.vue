<template>
  <div class="real-time-risk-monitor">
    <el-card>
      <template #header>
        <div class="monitor-header">
          <span>实时风险监控</span>
          <div class="header-actions">
            <el-switch
              v-model="isMonitoring"
              active-text="监控中"
              inactive-text="已暂停"
              @change="toggleMonitoring"
            />
            <el-button size="small" @click="refreshRiskData" :loading="loading">
              <el-icon><Refresh /></el-icon>
            </el-button>
          </div>
        </div>
      </template>

      <!-- 实时风险指标 -->
      <div class="risk-metrics-grid">
        <div class="metric-item" v-for="metric in riskMetrics" :key="metric.key">
          <div class="metric-header">
            <span class="metric-name">{{ metric.name }}</span>
            <el-tag
              :type="getRiskTagType(metric.level)"
              size="small"
              effect="dark"
            >
              {{ getRiskLevelText(metric.level) }}
            </el-tag>
          </div>
          <div class="metric-value" :class="getValueClass(metric.trend)">
            {{ formatMetricValue(metric) }}
          </div>
          <div class="metric-trend">
            <el-icon v-if="metric.trend === 'up'" class="trend-up">
              <ArrowUp />
            </el-icon>
            <el-icon v-else-if="metric.trend === 'down'" class="trend-down">
              <ArrowDown />
            </el-icon>
            <el-icon v-else class="trend-stable">
              <Minus />
            </el-icon>
            <span class="trend-text">{{ metric.changeText }}</span>
          </div>
          <div class="metric-progress">
            <el-progress
              :percentage="metric.percentage"
              :color="getProgressColor(metric.level)"
              :show-text="false"
              :stroke-width="4"
            />
          </div>
        </div>
      </div>

      <!-- 风险热力图 -->
      <div class="risk-heatmap-section">
        <h4 class="section-title">持仓风险热力图</h4>
        <div class="heatmap-container">
          <div
            v-for="position in positionRisks"
            :key="position.symbol"
            class="position-risk-cell"
            :class="getRiskCellClass(position.riskLevel)"
            :style="{ 
              width: `${position.weight * 200}px`,
              height: `${Math.max(position.weight * 100, 30)}px`
            }"
            @click="showPositionDetail(position)"
          >
            <div class="cell-content">
              <div class="symbol">{{ position.symbol }}</div>
              <div class="risk-value">{{ formatPercent(position.riskValue) }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 实时预警 -->
      <div class="real-time-alerts" v-if="realtimeAlerts.length > 0">
        <h4 class="section-title">
          实时预警
          <el-badge :value="realtimeAlerts.length" type="danger" />
        </h4>
        <div class="alerts-timeline">
          <div
            v-for="alert in realtimeAlerts"
            :key="alert.id"
            class="alert-item"
            :class="getAlertClass(alert.severity)"
          >
            <div class="alert-time">{{ formatTime(alert.timestamp) }}</div>
            <div class="alert-content">
              <div class="alert-title">{{ alert.title }}</div>
              <div class="alert-message">{{ alert.message }}</div>
            </div>
            <div class="alert-actions">
              <el-button size="small" @click="handleAlert(alert)">
                处理
              </el-button>
              <el-button size="small" @click="dismissAlert(alert.id)">
                忽略
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 风险趋势图表 -->
      <div class="risk-trend-chart">
        <h4 class="section-title">风险趋势</h4>
        <div ref="riskChartRef" class="chart-container" style="height: 200px;"></div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import {
  Refresh, ArrowUp, ArrowDown, Minus
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'

// 状态
const isMonitoring = ref(true)
const loading = ref(false)
const riskChartRef = ref()
let riskChart: echarts.ECharts | null = null
let monitoringTimer: number | null = null

// 实时风险指标
const riskMetrics = reactive([
  {
    key: 'var',
    name: 'VaR (95%)',
    value: 125000,
    threshold: 150000,
    level: 'medium',
    trend: 'up',
    changeText: '+2.3%',
    unit: 'currency'
  },
  {
    key: 'volatility',
    name: '组合波动率',
    value: 0.18,
    threshold: 0.25,
    level: 'low',
    trend: 'down',
    changeText: '-0.5%',
    unit: 'percent'
  },
  {
    key: 'concentration',
    name: '集中度风险',
    value: 0.35,
    threshold: 0.4,
    level: 'medium',
    trend: 'stable',
    changeText: '0.0%',
    unit: 'percent'
  },
  {
    key: 'correlation',
    name: '相关性风险',
    value: 0.72,
    threshold: 0.8,
    level: 'high',
    trend: 'up',
    changeText: '+3.1%',
    unit: 'number'
  }
])

// 持仓风险数据
const positionRisks = reactive([
  { symbol: 'AAPL', weight: 0.15, riskValue: 0.12, riskLevel: 'low' },
  { symbol: 'TSLA', weight: 0.08, riskValue: 0.28, riskLevel: 'high' },
  { symbol: 'MSFT', weight: 0.12, riskValue: 0.15, riskLevel: 'medium' },
  { symbol: 'GOOGL', weight: 0.10, riskValue: 0.18, riskLevel: 'medium' },
  { symbol: 'AMZN', weight: 0.09, riskValue: 0.22, riskLevel: 'high' },
  { symbol: 'NVDA', weight: 0.07, riskValue: 0.35, riskLevel: 'high' }
])

// 实时预警
const realtimeAlerts = reactive([
  {
    id: 1,
    timestamp: new Date(),
    severity: 'high',
    title: 'VaR超限预警',
    message: 'VaR值已超过设定阈值的90%，建议调整持仓'
  },
  {
    id: 2,
    timestamp: new Date(Date.now() - 5 * 60 * 1000),
    severity: 'medium',
    title: '集中度风险',
    message: '科技股持仓比例过高，建议分散投资'
  }
])

// 计算属性
const formatMetricValue = (metric: any) => {
  switch (metric.unit) {
    case 'currency':
      return new Intl.NumberFormat('zh-CN', {
        style: 'currency',
        currency: 'CNY'
      }).format(metric.value)
    case 'percent':
      return (metric.value * 100).toFixed(2) + '%'
    case 'number':
      return metric.value.toFixed(2)
    default:
      return metric.value.toString()
  }
}

// 计算百分比
riskMetrics.forEach(metric => {
  metric.percentage = Math.min((metric.value / metric.threshold) * 100, 100)
})

// 方法
const getRiskTagType = (level: string) => {
  switch (level) {
    case 'low': return 'success'
    case 'medium': return 'warning'
    case 'high': return 'danger'
    default: return 'info'
  }
}

const getRiskLevelText = (level: string) => {
  switch (level) {
    case 'low': return '低风险'
    case 'medium': return '中风险'
    case 'high': return '高风险'
    default: return '未知'
  }
}

const getValueClass = (trend: string) => {
  switch (trend) {
    case 'up': return 'value-up'
    case 'down': return 'value-down'
    default: return 'value-stable'
  }
}

const getProgressColor = (level: string) => {
  switch (level) {
    case 'low': return '#67c23a'
    case 'medium': return '#e6a23c'
    case 'high': return '#f56c6c'
    default: return '#909399'
  }
}

const getRiskCellClass = (level: string) => {
  return `risk-${level}`
}

const getAlertClass = (severity: string) => {
  return `alert-${severity}`
}

const formatPercent = (value: number) => {
  return (value * 100).toFixed(1) + '%'
}

const formatTime = (date: Date) => {
  return date.toLocaleTimeString('zh-CN', { 
    hour: '2-digit', 
    minute: '2-digit',
    second: '2-digit'
  })
}

const toggleMonitoring = (value: boolean) => {
  if (value) {
    startMonitoring()
    ElMessage.success('风险监控已启动')
  } else {
    stopMonitoring()
    ElMessage.info('风险监控已暂停')
  }
}

const startMonitoring = () => {
  if (monitoringTimer) return
  
  monitoringTimer = setInterval(() => {
    updateRiskData()
  }, 5000) // 每5秒更新一次
}

const stopMonitoring = () => {
  if (monitoringTimer) {
    clearInterval(monitoringTimer)
    monitoringTimer = null
  }
}

const updateRiskData = () => {
  // 模拟实时数据更新
  riskMetrics.forEach(metric => {
    const change = (Math.random() - 0.5) * 0.1
    metric.value *= (1 + change)
    metric.percentage = Math.min((metric.value / metric.threshold) * 100, 100)
    
    // 更新趋势
    if (change > 0.02) {
      metric.trend = 'up'
    } else if (change < -0.02) {
      metric.trend = 'down'
    } else {
      metric.trend = 'stable'
    }
    
    metric.changeText = (change * 100).toFixed(1) + '%'
  })
  
  // 更新图表
  updateRiskChart()
}

const refreshRiskData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    updateRiskData()
    ElMessage.success('风险数据已刷新')
  } catch (error) {
    ElMessage.error('刷新失败')
  } finally {
    loading.value = false
  }
}

const showPositionDetail = (position: any) => {
  ElMessage.info(`查看 ${position.symbol} 详细风险信息`)
}

const handleAlert = (alert: any) => {
  ElMessage.success(`正在处理预警: ${alert.title}`)
}

const dismissAlert = (alertId: number) => {
  const index = realtimeAlerts.findIndex(alert => alert.id === alertId)
  if (index > -1) {
    realtimeAlerts.splice(index, 1)
  }
}

const initRiskChart = () => {
  if (!riskChartRef.value) return
  
  riskChart = echarts.init(riskChartRef.value)
  updateRiskChart()
}

const updateRiskChart = () => {
  if (!riskChart) return
  
  const option = {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['VaR', '波动率', '集中度']
    },
    xAxis: {
      type: 'category',
      data: Array.from({ length: 20 }, (_, i) => {
        const time = new Date(Date.now() - (19 - i) * 60000)
        return time.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
      })
    },
    yAxis: {
      type: 'value'
    },
    series: [
      {
        name: 'VaR',
        type: 'line',
        data: Array.from({ length: 20 }, () => Math.random() * 100 + 50),
        smooth: true
      },
      {
        name: '波动率',
        type: 'line',
        data: Array.from({ length: 20 }, () => Math.random() * 30 + 10),
        smooth: true
      },
      {
        name: '集中度',
        type: 'line',
        data: Array.from({ length: 20 }, () => Math.random() * 40 + 20),
        smooth: true
      }
    ]
  }
  
  riskChart.setOption(option)
}

// 生命周期
onMounted(() => {
  nextTick(() => {
    initRiskChart()
    if (isMonitoring.value) {
      startMonitoring()
    }
  })
})

onUnmounted(() => {
  stopMonitoring()
  if (riskChart) {
    riskChart.dispose()
  }
})
</script>

<style scoped>
.real-time-risk-monitor {
  .monitor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .header-actions {
      display: flex;
      align-items: center;
      gap: 12px;
    }
  }

  .risk-metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
    margin-bottom: 24px;

    .metric-item {
      padding: 16px;
      border: 1px solid #e6e6e6;
      border-radius: 8px;
      background: #fafafa;

      .metric-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 8px;

        .metric-name {
          font-weight: 500;
          color: #333;
        }
      }

      .metric-value {
        font-size: 20px;
        font-weight: 600;
        margin-bottom: 4px;

        &.value-up { color: #f56c6c; }
        &.value-down { color: #67c23a; }
        &.value-stable { color: #333; }
      }

      .metric-trend {
        display: flex;
        align-items: center;
        gap: 4px;
        font-size: 12px;
        color: #666;
        margin-bottom: 8px;

        .trend-up { color: #f56c6c; }
        .trend-down { color: #67c23a; }
        .trend-stable { color: #909399; }
      }

      .metric-progress {
        margin-top: 8px;
      }
    }
  }

  .risk-heatmap-section {
    margin-bottom: 24px;

    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .heatmap-container {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;

      .position-risk-cell {
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 4px;
        cursor: pointer;
        transition: all 0.3s;

        &.risk-low { background: #f0f9ff; border: 1px solid #67c23a; }
        &.risk-medium { background: #fefce8; border: 1px solid #e6a23c; }
        &.risk-high { background: #fef2f2; border: 1px solid #f56c6c; }

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .cell-content {
          text-align: center;

          .symbol {
            font-weight: 600;
            font-size: 12px;
          }

          .risk-value {
            font-size: 10px;
            color: #666;
          }
        }
      }
    }
  }

  .real-time-alerts {
    margin-bottom: 24px;

    .section-title {
      display: flex;
      align-items: center;
      gap: 8px;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .alerts-timeline {
      .alert-item {
        display: flex;
        align-items: center;
        gap: 12px;
        padding: 12px;
        border-radius: 6px;
        margin-bottom: 8px;

        &.alert-high { background: #fef2f2; border-left: 4px solid #f56c6c; }
        &.alert-medium { background: #fefce8; border-left: 4px solid #e6a23c; }
        &.alert-low { background: #f0f9ff; border-left: 4px solid #409eff; }

        .alert-time {
          font-size: 12px;
          color: #666;
          min-width: 80px;
        }

        .alert-content {
          flex: 1;

          .alert-title {
            font-weight: 500;
            margin-bottom: 2px;
          }

          .alert-message {
            font-size: 12px;
            color: #666;
          }
        }

        .alert-actions {
          display: flex;
          gap: 8px;
        }
      }
    }
  }

  .risk-trend-chart {
    .section-title {
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 12px;
      color: #333;
    }

    .chart-container {
      border: 1px solid #e6e6e6;
      border-radius: 6px;
    }
  }
}
</style>
