<template>
  <div class="professional-layout">
    <!-- 顶部导航栏 -->
    <header class="top-navbar">
      <div class="navbar-content">
        <div class="navbar-left">
          <div class="logo-section">
            <span class="app-title">🚀 量化投资平台</span>
          </div>
        </div>

        <nav class="navbar-nav">
          <router-link to="/dashboard" class="nav-item">📊 仪表盘</router-link>
          <router-link to="/market" class="nav-item">📈 市场</router-link>
          <router-link to="/trading" class="nav-item">💰 交易</router-link>
          <router-link to="/strategy" class="nav-item">🧠 策略</router-link>
          <router-link to="/portfolio" class="nav-item">📋 投资组合</router-link>
          <router-link to="/backtest" class="nav-item">🔄 回测</router-link>
          <router-link to="/risk" class="nav-item">🛡️ 风险</router-link>
          <router-link to="/demo" class="nav-item">🎯 演示</router-link>
          <router-link to="/safe-test" class="nav-item">🧪 测试</router-link>
        </nav>
      </div>
    </header>

    <!-- 主要内容区域 -->
    <main class="main-content">
      <div class="content-wrapper">
        <!-- 路由视图 -->
        <router-view v-slot="{ Component, route }">
          <transition name="fade-slide" mode="out-in">
            <keep-alive :include="cachedViews">
              <component :is="Component" :key="route.path" />
            </keep-alive>
          </transition>
        </router-view>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

// 缓存的视图组件
const cachedViews = ref(['Dashboard', 'Market', 'Trading'])

// 当前页面标题
const pageTitle = computed(() => {
  return route.meta?.title || '量化投资平台'
})
</script>

<style scoped>
.professional-layout {
  min-height: 100vh;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
}

.top-navbar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 1000;
}

.navbar-content {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.navbar-left {
  display: flex;
  align-items: center;
}

.logo-section {
  display: flex;
  align-items: center;
}

.app-title {
  font-size: 1.5rem;
  font-weight: bold;
  color: white;
  text-decoration: none;
}

.navbar-nav {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.nav-item {
  padding: 0.75rem 1rem;
  color: rgba(255, 255, 255, 0.9);
  text-decoration: none;
  border-radius: 8px;
  transition: all 0.3s ease;
  font-weight: 500;
  white-space: nowrap;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-1px);
}

.nav-item.router-link-active {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1400px;
  margin: 0 auto;
  width: 100%;
}

.content-wrapper {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  min-height: calc(100vh - 120px);
  overflow: hidden;
}

/* 页面切换动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition: all 0.3s ease;
}

.fade-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .navbar-content {
    padding: 0 1rem;
  }
  
  .main-content {
    padding: 1rem;
  }
}

@media (max-width: 768px) {
  .navbar-nav {
    gap: 0.25rem;
  }
  
  .nav-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.9rem;
  }
  
  .app-title {
    font-size: 1.25rem;
  }
}

@media (max-width: 640px) {
  .navbar-content {
    flex-direction: column;
    height: auto;
    padding: 1rem;
    gap: 1rem;
  }
  
  .navbar-nav {
    flex-wrap: wrap;
    justify-content: center;
  }
  
  .main-content {
    padding: 0.5rem;
  }
}
</style>
