<template>
  <div class="market-search">
    <div class="search-container">
      <!-- 搜索输入框 -->
      <el-autocomplete
        v-model="searchQuery"
        :fetch-suggestions="fetchSuggestions"
        :trigger-on-focus="false"
        placeholder="输入股票代码或名称搜索"
        class="search-input"
        size="large"
        clearable
        @select="handleSelect"
        @clear="handleClear"
      >
        <template #prefix>
          <el-icon><Search /></el-icon>
        </template>
        
        <template #default="{ item }">
          <div class="suggestion-item">
            <div class="stock-info">
              <span class="stock-symbol">{{ item.symbol }}</span>
              <span class="stock-name">{{ item.name }}</span>
            </div>
            <div class="stock-price" v-if="item.currentPrice">
              <span class="price" :class="getPriceClass(item.change)">
                {{ formatPrice(item.currentPrice) }}
              </span>
              <span class="change" :class="getPriceClass(item.change)">
                {{ formatChange(item.change) }}
              </span>
            </div>
          </div>
        </template>
      </el-autocomplete>

      <!-- 搜索按钮 -->
      <el-button 
        type="primary" 
        size="large"
        :loading="searching"
        @click="handleSearch"
      >
        <el-icon><Search /></el-icon>
        搜索
      </el-button>
    </div>

    <!-- 快速搜索标签 -->
    <div class="quick-search" v-if="showQuickSearch">
      <div class="quick-title">热门搜索</div>
      <div class="quick-tags">
        <el-tag
          v-for="tag in quickSearchTags"
          :key="tag"
          class="quick-tag"
          @click="handleQuickSearch(tag)"
        >
          {{ tag }}
        </el-tag>
      </div>
    </div>

    <!-- 搜索历史 -->
    <div class="search-history" v-if="showHistory && searchHistory.length > 0">
      <div class="history-header">
        <span class="history-title">搜索历史</span>
        <el-button 
          text 
          size="small" 
          @click="clearHistory"
        >
          <el-icon><Delete /></el-icon>
          清空
        </el-button>
      </div>
      <div class="history-items">
        <div
          v-for="item in searchHistory"
          :key="item.symbol"
          class="history-item"
          @click="handleHistoryClick(item)"
        >
          <div class="history-info">
            <span class="history-symbol">{{ item.symbol }}</span>
            <span class="history-name">{{ item.name }}</span>
          </div>
          <div class="history-time">{{ formatTime(item.searchTime) }}</div>
        </div>
      </div>
    </div>

    <!-- 搜索结果 -->
    <div class="search-results" v-if="showResults && searchResults.length > 0">
      <div class="results-header">
        <span class="results-title">搜索结果 ({{ searchResults.length }})</span>
        <el-button 
          text 
          size="small" 
          @click="clearResults"
        >
          <el-icon><Close /></el-icon>
          清空
        </el-button>
      </div>
      <div class="results-list">
        <div
          v-for="stock in searchResults"
          :key="stock.symbol"
          class="result-item"
          @click="handleResultClick(stock)"
        >
          <div class="result-info">
            <div class="result-main">
              <span class="result-symbol">{{ stock.symbol }}</span>
              <span class="result-name">{{ stock.name }}</span>
            </div>
            <div class="result-industry" v-if="stock.industry">
              {{ stock.industry }}
            </div>
          </div>
          <div class="result-price" v-if="stock.currentPrice">
            <div class="price" :class="getPriceClass(stock.change)">
              {{ formatPrice(stock.currentPrice) }}
            </div>
            <div class="change" :class="getPriceClass(stock.change)">
              {{ formatPercent(stock.changePercent) }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 无结果提示 -->
    <div class="no-results" v-if="showResults && searchResults.length === 0 && !searching">
      <el-empty description="未找到相关股票">
        <el-button type="primary" @click="handleClear">重新搜索</el-button>
      </el-empty>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { Search, Delete, Close } from '@element-plus/icons-vue'
import { formatPrice, formatChange, formatPercent, formatTime } from '@/utils/formatters'
import { debounce } from 'lodash-es'
import type { QuoteData, SearchResult } from '@/types/market'

interface Props {
  placeholder?: string
  showQuickSearch?: boolean
  showHistory?: boolean
  maxHistoryItems?: number
}

const props = withDefaults(defineProps<Props>(), {
  placeholder: '输入股票代码或名称搜索',
  showQuickSearch: true,
  showHistory: true,
  maxHistoryItems: 10
})

const emit = defineEmits<{
  (e: 'search', query: string): void
  (e: 'select', stock: QuoteData): void
  (e: 'clear'): void
}>()

// 响应式数据
const searchQuery = ref('')
const searching = ref(false)
const searchResults = ref<QuoteData[]>([])
const searchHistory = ref<Array<QuoteData & { searchTime: number }>>([])
const showResults = ref(false)

// 快速搜索标签
const quickSearchTags = ref([
  '贵州茅台', '招商银行', '平安银行', '比亚迪', '宁德时代',
  '腾讯控股', '阿里巴巴', '美团', '小米集团', '中国移动'
])

// 计算属性
const showHistory = computed(() => props.showHistory && !showResults.value && searchQuery.value === '')

// 方法
const getPriceClass = (change: number) => {
  if (change > 0) return 'up'
  if (change < 0) return 'down'
  return 'neutral'
}

// 防抖搜索建议
const fetchSuggestions = debounce(async (queryString: string, cb: Function) => {
  if (!queryString) {
    cb([])
    return
  }

  try {
    // 这里应该调用实际的搜索API
    // const results = await marketApi.searchStocks(queryString)
    
    // 模拟搜索结果
    const mockResults = [
      { symbol: '000001.SZ', name: '平安银行', currentPrice: 12.50, change: 0.05, changePercent: 0.4 },
      { symbol: '600036.SH', name: '招商银行', currentPrice: 42.15, change: 0.25, changePercent: 0.6 },
      { symbol: '600519.SH', name: '贵州茅台', currentPrice: 1680.00, change: 15.50, changePercent: 0.9 }
    ].filter(item => 
      item.symbol.includes(queryString.toUpperCase()) || 
      item.name.includes(queryString)
    )

    cb(mockResults)
  } catch (error) {
    console.error('搜索建议失败:', error)
    cb([])
  }
}, 300)

const handleSearch = async () => {
  if (!searchQuery.value.trim()) return

  searching.value = true
  showResults.value = true

  try {
    // 这里应该调用实际的搜索API
    // const results = await marketApi.searchStocks(searchQuery.value)
    
    // 模拟搜索结果
    const mockResults = [
      { symbol: '000001.SZ', name: '平安银行', currentPrice: 12.50, change: 0.05, changePercent: 0.4, industry: '银行' },
      { symbol: '600036.SH', name: '招商银行', currentPrice: 42.15, change: 0.25, changePercent: 0.6, industry: '银行' }
    ].filter(item => 
      item.symbol.includes(searchQuery.value.toUpperCase()) || 
      item.name.includes(searchQuery.value)
    )

    searchResults.value = mockResults
    emit('search', searchQuery.value)
  } catch (error) {
    console.error('搜索失败:', error)
    searchResults.value = []
  } finally {
    searching.value = false
  }
}

const handleSelect = (item: QuoteData) => {
  addToHistory(item)
  emit('select', item)
  showResults.value = false
}

const handleQuickSearch = (tag: string) => {
  searchQuery.value = tag
  handleSearch()
}

const handleHistoryClick = (item: QuoteData) => {
  searchQuery.value = item.name
  emit('select', item)
}

const handleResultClick = (stock: QuoteData) => {
  addToHistory(stock)
  emit('select', stock)
  showResults.value = false
}

const handleClear = () => {
  searchQuery.value = ''
  searchResults.value = []
  showResults.value = false
  emit('clear')
}

const clearResults = () => {
  searchResults.value = []
  showResults.value = false
}

const clearHistory = () => {
  searchHistory.value = []
  localStorage.removeItem('market-search-history')
}

const addToHistory = (stock: QuoteData) => {
  const historyItem = {
    ...stock,
    searchTime: Date.now()
  }

  // 移除重复项
  const index = searchHistory.value.findIndex(item => item.symbol === stock.symbol)
  if (index > -1) {
    searchHistory.value.splice(index, 1)
  }

  // 添加到开头
  searchHistory.value.unshift(historyItem)

  // 限制历史记录数量
  if (searchHistory.value.length > props.maxHistoryItems) {
    searchHistory.value = searchHistory.value.slice(0, props.maxHistoryItems)
  }

  // 保存到本地存储
  localStorage.setItem('market-search-history', JSON.stringify(searchHistory.value))
}

const loadHistory = () => {
  try {
    const saved = localStorage.getItem('market-search-history')
    if (saved) {
      searchHistory.value = JSON.parse(saved)
    }
  } catch (error) {
    console.error('加载搜索历史失败:', error)
  }
}

// 生命周期
onMounted(() => {
  loadHistory()
})
</script>

<style scoped>
.market-search {
  width: 100%;
}

.search-container {
  display: flex;
  gap: 12px;
  margin-bottom: 16px;
}

.search-input {
  flex: 1;
}

.quick-search {
  margin-bottom: 16px;
}

.quick-title {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.quick-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-tag {
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-tag:hover {
  background-color: #409eff;
  color: white;
}

.search-history, .search-results {
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  overflow: hidden;
}

.history-header, .results-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #f8f9fa;
  border-bottom: 1px solid #e4e7ed;
}

.history-title, .results-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
}

.history-items, .results-list {
  max-height: 300px;
  overflow-y: auto;
}

.history-item, .result-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.history-item:hover, .result-item:hover {
  background-color: #f5f7fa;
}

.history-item:not(:last-child), .result-item:not(:last-child) {
  border-bottom: 1px solid #f0f0f0;
}

.suggestion-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.stock-info, .history-info, .result-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.result-main {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stock-symbol, .history-symbol, .result-symbol {
  font-family: 'Courier New', monospace;
  font-weight: 600;
  color: #303133;
}

.stock-name, .history-name, .result-name {
  color: #606266;
}

.result-industry {
  font-size: 12px;
  color: #909399;
}

.stock-price, .result-price {
  text-align: right;
}

.price {
  font-weight: 600;
  margin-bottom: 2px;
}

.change {
  font-size: 12px;
}

.history-time {
  font-size: 12px;
  color: #909399;
}

.no-results {
  padding: 40px 20px;
  text-align: center;
}

/* 价格颜色 */
.up {
  color: #f56c6c;
}

.down {
  color: #67c23a;
}

.neutral {
  color: #909399;
}
</style>
