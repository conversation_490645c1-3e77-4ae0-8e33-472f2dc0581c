// 风险指标数据
export interface RiskMetricsData {
  portfolio_value: number
  var: {
    '1_day_95': number
    '1_day_99': number
  }
  beta: number
  sharpe_ratio: number
  max_drawdown: number
  volatility: number
  concentration: {
    top_5_holdings: number
    single_stock_limit: number
  }
  stress_test: {
    market_down_10: number
    market_down_20: number
  }
}

// 风险警报
export interface RiskAlert {
  id: string
  type: 'concentration' | 'var' | 'drawdown' | 'leverage' | 'volatility'
  level: 'low' | 'medium' | 'high' | 'critical'
  title: string
  message: string
  symbol?: string
  current_value: number
  threshold: number
  status: 'active' | 'handled' | 'dismissed'
  created_at: string
}

// 风险警报响应
export interface RiskAlertsData {
  data: RiskAlert[]
  summary: {
    critical: number
    high: number
    medium: number
    low: number
  }
}

// 风险配置
export interface RiskConfig {
  varConfidence: number
  maxDrawdownLimit: number
  leverageLimit: number
  concentrationLimit: number
  volatilityLimit: number
  betaLimit: number
  alertChannels: string[]
  alertThresholds: {
    var: number
    drawdown: number
    leverage: number
    concentration: number
  }
}

// 风险指标
export interface RiskMetric {
  name: string
  current: number
  threshold: number
  status: 'good' | 'normal' | 'warning' | 'danger'
  trend: 'up' | 'down' | 'stable'
  type: 'currency' | 'percent' | 'number'
  history: Array<{
    date: number
    value: number
  }>
}

// 风险限额
export interface RiskLimit {
  name: string
  current: number
  total: number
  usage: number
  type: 'currency' | 'percent' | 'number'
}

// 压力测试场景
export interface StressTestScenario {
  id: string
  name: string
  description: string
  parameters: {
    market_change?: number
    interest_rate_change?: number
    volatility_change?: number
    correlation_change?: number
  }
  result?: {
    portfolio_impact: number
    var_impact: number
    expected_loss: number
    worst_case_loss: number
  }
}

// 风险分布
export interface RiskDistribution {
  name: string
  value: number
  percentage: number
}

// 风险热力图数据
export interface RiskHeatmapData {
  categories: string[]
  data: Array<{
    x: string
    y: string
    value: number
  }>
}