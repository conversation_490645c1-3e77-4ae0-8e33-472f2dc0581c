<template>
  <div class="simulated-trading-fixed">
    <!-- 头部控制栏 -->
    <div class="trading-header">
      <div class="simulation-badge">
        <span class="badge-text">模拟交易</span>
        <span class="badge-tip">无风险练习</span>
      </div>

      <div class="account-overview">
        <div class="account-item">
          <span class="label">总资产:</span>
          <span class="value">¥{{ accountInfo.totalAssets.toLocaleString() }}</span>
        </div>
        <div class="account-item">
          <span class="label">可用资金:</span>
          <span class="value">¥{{ accountInfo.availableCash.toLocaleString() }}</span>
        </div>
        <div class="account-item">
          <span class="label">持仓市值:</span>
          <span class="value">¥{{ accountInfo.positionValue.toLocaleString() }}</span>
        </div>
        <div class="account-item">
          <span class="label">今日盈亏:</span>
          <span class="value" :class="accountInfo.todayPnL >= 0 ? 'profit' : 'loss'">
            {{ accountInfo.todayPnL >= 0 ? '+' : '' }}¥{{ accountInfo.todayPnL.toLocaleString() }}
          </span>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 左侧：股票信息和五档行情 -->
      <div class="left-panel">
        <!-- 股票搜索 -->
        <div class="stock-search">
          <el-input
            v-model="searchKeyword"
            placeholder="输入股票代码或名称"
            class="search-input"
            @input="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <div v-if="searchResults.length > 0" class="search-dropdown">
            <div
              v-for="stock in searchResults"
              :key="stock.code"
              class="search-item"
              @click="selectStock(stock)"
            >
              <span class="stock-code">{{ stock.code }}</span>
              <span class="stock-name">{{ stock.name }}</span>
            </div>
          </div>
        </div>

        <!-- 当前股票信息 -->
        <div v-if="currentStock" class="stock-info">
          <div class="stock-header">
            <h3>{{ currentStock.name }} ({{ currentStock.code }})</h3>
            <div class="price-info">
              <span class="current-price" :class="currentStock.change >= 0 ? 'up' : 'down'">
                ¥{{ currentStock.price.toFixed(2) }}
              </span>
              <span class="change" :class="currentStock.change >= 0 ? 'up' : 'down'">
                {{ currentStock.change >= 0 ? '+' : '' }}{{ currentStock.change.toFixed(2) }}
                ({{ currentStock.changePercent >= 0 ? '+' : '' }}{{ currentStock.changePercent.toFixed(2) }}%)
              </span>
            </div>
          </div>

          <!-- 五档行情 -->
          <div class="market-depth">
            <div class="depth-header">五档行情</div>
            <div class="depth-content">
              <div v-for="(item, index) in marketDepth.sell" :key="`sell-${index}`" class="depth-row sell">
                <span class="level">卖{{ 5 - index }}</span>
                <span class="price">{{ item.price.toFixed(2) }}</span>
                <span class="volume">{{ item.volume }}</span>
              </div>
              <div class="depth-row current">
                <span class="level">现价</span>
                <span class="price">{{ currentStock.price.toFixed(2) }}</span>
                <span class="volume">-</span>
              </div>
              <div v-for="(item, index) in marketDepth.buy" :key="`buy-${index}`" class="depth-row buy">
                <span class="level">买{{ index + 1 }}</span>
                <span class="price">{{ item.price.toFixed(2) }}</span>
                <span class="volume">{{ item.volume }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧：交易操作区 -->
      <div class="right-panel">
        <div class="trading-form">
          <div class="form-tabs">
            <button
              :class="['tab-btn', { active: activeTab === 'buy' }]"
              @click="activeTab = 'buy'"
            >
              买入
            </button>
            <button
              :class="['tab-btn', { active: activeTab === 'sell' }]"
              @click="activeTab = 'sell'"
            >
              卖出
            </button>
          </div>

          <div class="form-content">
            <div class="form-row">
              <label>股票代码:</label>
              <el-input v-model="orderForm.stockCode" placeholder="请选择股票" readonly />
            </div>

            <div class="form-row">
              <label>委托价格:</label>
              <el-input v-model="orderForm.price" placeholder="0.00" type="number" step="0.01" />
            </div>

            <div class="form-row">
              <label>委托数量:</label>
              <el-input v-model="orderForm.quantity" placeholder="100" type="number" step="100" />
            </div>

            <div class="form-row">
              <label>委托金额:</label>
              <span class="calculated-amount">¥{{ calculatedAmount.toLocaleString() }}</span>
            </div>

            <div class="form-actions">
              <el-button
                type="danger"
                size="large"
                :disabled="!canSubmitOrder"
                @click="submitOrder('buy')"
                v-if="activeTab === 'buy'"
              >
                买入
              </el-button>
              <el-button
                type="success"
                size="large"
                :disabled="!canSubmitOrder"
                @click="submitOrder('sell')"
                v-if="activeTab === 'sell'"
              >
                卖出
              </el-button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部面板：持仓、委托、成交 -->
    <div class="bottom-panel">
      <el-tabs v-model="bottomActiveTab">
        <el-tab-pane label="持仓" name="positions">
          <el-table :data="positions" style="width: 100%">
            <el-table-column prop="stockCode" label="股票代码" width="120" />
            <el-table-column prop="stockName" label="股票名称" width="150" />
            <el-table-column prop="quantity" label="持仓数量" width="120" />
            <el-table-column prop="avgPrice" label="成本价" width="120" />
            <el-table-column prop="currentPrice" label="现价" width="120" />
            <el-table-column prop="marketValue" label="市值" width="120" />
            <el-table-column prop="pnl" label="盈亏" width="120">
              <template #default="{ row }">
                <span :class="row.pnl >= 0 ? 'profit' : 'loss'">
                  {{ row.pnl >= 0 ? '+' : '' }}{{ row.pnl.toFixed(2) }}
                </span>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="委托" name="orders">
          <el-table :data="orders" style="width: 100%">
            <el-table-column prop="orderTime" label="委托时间" width="150" />
            <el-table-column prop="stockCode" label="股票代码" width="120" />
            <el-table-column prop="stockName" label="股票名称" width="150" />
            <el-table-column prop="direction" label="买卖方向" width="100" />
            <el-table-column prop="price" label="委托价格" width="120" />
            <el-table-column prop="quantity" label="委托数量" width="120" />
            <el-table-column prop="status" label="状态" width="100" />
          </el-table>
        </el-tab-pane>

        <el-tab-pane label="成交" name="trades">
          <el-table :data="trades" style="width: 100%">
            <el-table-column prop="tradeTime" label="成交时间" width="150" />
            <el-table-column prop="stockCode" label="股票代码" width="120" />
            <el-table-column prop="stockName" label="股票名称" width="150" />
            <el-table-column prop="direction" label="买卖方向" width="100" />
            <el-table-column prop="price" label="成交价格" width="120" />
            <el-table-column prop="quantity" label="成交数量" width="120" />
            <el-table-column prop="amount" label="成交金额" width="120" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Search } from '@element-plus/icons-vue'

// 响应式数据
const searchKeyword = ref('')
const searchResults = ref<any[]>([])
const currentStock = ref<any>(null)
const activeTab = ref('buy')
const bottomActiveTab = ref('positions')

// 账户信息
const accountInfo = ref({
  totalAssets: 1000000,
  availableCash: 800000,
  positionValue: 200000,
  todayPnL: 5000
})

// 订单表单
const orderForm = ref({
  stockCode: '',
  price: '',
  quantity: ''
})

// 五档行情数据
const marketDepth = ref({
  sell: [
    { price: 10.25, volume: 1000 },
    { price: 10.24, volume: 2000 },
    { price: 10.23, volume: 1500 },
    { price: 10.22, volume: 3000 },
    { price: 10.21, volume: 2500 }
  ],
  buy: [
    { price: 10.19, volume: 2000 },
    { price: 10.18, volume: 1800 },
    { price: 10.17, volume: 2200 },
    { price: 10.16, volume: 1600 },
    { price: 10.15, volume: 3000 }
  ]
})

// 持仓数据
const positions = ref([
  {
    stockCode: '000001',
    stockName: '平安银行',
    quantity: 1000,
    avgPrice: 10.50,
    currentPrice: 10.80,
    marketValue: 10800,
    pnl: 300
  }
])

// 委托数据
const orders = ref([
  {
    orderTime: '2024-01-15 09:30:00',
    stockCode: '000002',
    stockName: '万科A',
    direction: '买入',
    price: 8.50,
    quantity: 1000,
    status: '已报'
  }
])

// 成交数据
const trades = ref([
  {
    tradeTime: '2024-01-15 09:31:00',
    stockCode: '000001',
    stockName: '平安银行',
    direction: '买入',
    price: 10.50,
    quantity: 1000,
    amount: 10500
  }
])

// 计算属性
const calculatedAmount = computed(() => {
  const price = parseFloat(orderForm.value.price) || 0
  const quantity = parseInt(orderForm.value.quantity) || 0
  return price * quantity
})

const canSubmitOrder = computed(() => {
  return orderForm.value.stockCode &&
         orderForm.value.price &&
         orderForm.value.quantity &&
         parseFloat(orderForm.value.price) > 0 &&
         parseInt(orderForm.value.quantity) > 0
})

// 方法
const handleSearch = () => {
  if (searchKeyword.value.length >= 2) {
    // 模拟搜索结果
    searchResults.value = [
      { code: '000001', name: '平安银行' },
      { code: '000002', name: '万科A' },
      { code: '000858', name: '五粮液' }
    ].filter(stock =>
      stock.code.includes(searchKeyword.value) ||
      stock.name.includes(searchKeyword.value)
    )
  } else {
    searchResults.value = []
  }
}

const selectStock = (stock: any) => {
  currentStock.value = {
    ...stock,
    price: 10.20,
    change: 0.15,
    changePercent: 1.49
  }
  orderForm.value.stockCode = stock.code
  searchResults.value = []
  searchKeyword.value = `${stock.code} ${stock.name}`
}

const submitOrder = (direction: string) => {
  ElMessage.success(`${direction === 'buy' ? '买入' : '卖出'}委托已提交`)

  // 添加到委托列表
  orders.value.unshift({
    orderTime: new Date().toLocaleString(),
    stockCode: orderForm.value.stockCode,
    stockName: currentStock.value?.name || '',
    direction: direction === 'buy' ? '买入' : '卖出',
    price: parseFloat(orderForm.value.price),
    quantity: parseInt(orderForm.value.quantity),
    status: '已报'
  })

  // 清空表单
  orderForm.value.price = ''
  orderForm.value.quantity = ''
}

onMounted(() => {
  console.log('SimulatedTradingFixed 组件已挂载')
})
</script>

<style scoped>
.simulated-trading-fixed {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f5f5;
}

.trading-header {
  background: white;
  padding: 10px 20px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  gap: 20px;
}

.simulation-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.badge-text {
  background: #e6f7ff;
  color: #1890ff;
  padding: 4px 12px;
  border-radius: 4px;
  font-weight: bold;
}

.badge-tip {
  color: #666;
  font-size: 12px;
}

.account-overview {
  display: flex;
  gap: 30px;
  flex: 1;
}

.account-item {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.account-item .label {
  font-size: 12px;
  color: #666;
}

.account-item .value {
  font-size: 16px;
  font-weight: bold;
}

.profit {
  color: #f56c6c;
}

.loss {
  color: #67c23a;
}

.main-content {
  flex: 1;
  display: flex;
  gap: 10px;
  padding: 10px;
  overflow: hidden;
}

.left-panel {
  width: 400px;
  background: white;
  border-radius: 4px;
  padding: 15px;
  overflow-y: auto;
}

.right-panel {
  width: 350px;
  background: white;
  border-radius: 4px;
  padding: 15px;
}

.stock-search {
  position: relative;
  margin-bottom: 20px;
}

.search-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-top: none;
  border-radius: 0 0 4px 4px;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
}

.search-item {
  padding: 8px 12px;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
}

.search-item:hover {
  background: #f5f7fa;
}

.stock-code {
  font-weight: bold;
  color: #409eff;
}

.stock-info {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 15px;
}

.stock-header h3 {
  margin: 0 0 10px 0;
  color: #303133;
}

.price-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.current-price {
  font-size: 24px;
  font-weight: bold;
}

.change {
  font-size: 14px;
}

.up {
  color: #f56c6c;
}

.down {
  color: #67c23a;
}

.market-depth {
  margin-top: 20px;
}

.depth-header {
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.depth-content {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.depth-row {
  display: flex;
  justify-content: space-between;
  padding: 4px 8px;
  font-size: 12px;
}

.depth-row.sell {
  background: #fef0f0;
  color: #f56c6c;
}

.depth-row.buy {
  background: #f0f9ff;
  color: #67c23a;
}

.depth-row.current {
  background: #f5f7fa;
  font-weight: bold;
}

.trading-form {
  height: 100%;
}

.form-tabs {
  display: flex;
  margin-bottom: 20px;
}

.tab-btn {
  flex: 1;
  padding: 10px;
  border: 1px solid #dcdfe6;
  background: white;
  cursor: pointer;
  transition: all 0.3s;
}

.tab-btn:first-child {
  border-radius: 4px 0 0 4px;
}

.tab-btn:last-child {
  border-radius: 0 4px 4px 0;
  border-left: none;
}

.tab-btn.active {
  background: #409eff;
  color: white;
  border-color: #409eff;
}

.form-content {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  flex-direction: column;
  gap: 5px;
}

.form-row label {
  font-size: 14px;
  color: #606266;
}

.calculated-amount {
  font-size: 16px;
  font-weight: bold;
  color: #409eff;
}

.form-actions {
  margin-top: 20px;
}

.bottom-panel {
  height: 300px;
  background: white;
  margin: 0 10px 10px 10px;
  border-radius: 4px;
  padding: 15px;
}

:deep(.el-tabs__content) {
  height: calc(100% - 40px);
  overflow-y: auto;
}
</style>
