<template>
  <div class="risk-view">
    <div class="page-header">
      <h1>🛡️ 风险管理</h1>
      <p>投资风险监控与控制系统</p>
    </div>

    <div class="risk-content">
      <div class="risk-card">
        <h2>⚠️ 风险监控</h2>
        <p>风险管理系统正在构建中...</p>
        
        <div class="feature-list">
          <div class="feature-item">
            <span class="feature-icon">📊</span>
            <span>实时风险监控</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🎯</span>
            <span>风险指标分析</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🚨</span>
            <span>风险预警系统</span>
          </div>
          <div class="feature-item">
            <span class="feature-icon">🛡️</span>
            <span>风险控制策略</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 风险管理视图组件
</script>

<style scoped>
.risk-view {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  text-align: center;
  margin-bottom: 2rem;
}

.page-header h1 {
  font-size: 2.5rem;
  color: #2c3e50;
  margin-bottom: 0.5rem;
}

.page-header p {
  color: #6b7280;
  font-size: 1.1rem;
}

.risk-content {
  display: grid;
  gap: 2rem;
}

.risk-card {
  background: white;
  padding: 2rem;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.risk-card h2 {
  color: #2c3e50;
  margin-bottom: 1rem;
}

.feature-list {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-top: 1.5rem;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  background: #f8fafc;
  border-radius: 8px;
  border-left: 4px solid #ef4444;
}

.feature-icon {
  font-size: 1.5rem;
}
</style>
