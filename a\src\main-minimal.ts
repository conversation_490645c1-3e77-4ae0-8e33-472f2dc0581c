/**
 * 最小化的Vue应用入口文件 - 用于调试
 */

import { createApp } from 'vue'
import App from './App-minimal.vue'

console.log('🚀 启动最小化Vue应用...')

try {
  const app = createApp(App)

  // 全局错误处理
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue应用错误:', err)
    console.error('错误信息:', info)
  }

  // 挂载应用
  app.mount('#app')

  console.log('✅ 最小化Vue应用启动成功')

} catch (error) {
  console.error('❌ 最小化Vue应用启动失败:', error)
}
