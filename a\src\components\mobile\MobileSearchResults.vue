<template>
  <div class="mobile-search-results">
    <div v-if="loading" class="loading-state">
      <el-skeleton :rows="5" animated />
    </div>
    
    <div v-else-if="results.length === 0" class="empty-state">
      <el-icon size="48" color="#909399">
        <Search />
      </el-icon>
      <p>{{ query ? '未找到相关结果' : '请输入搜索关键词' }}</p>
    </div>
    
    <div v-else class="results-list">
      <div
        v-for="result in results"
        :key="result.id"
        class="result-item"
        @click="handleResultClick(result)"
      >
        <div class="result-icon">
          <el-icon :color="getResultColor(result.type)">
            <component :is="getResultIcon(result.type)" />
          </el-icon>
        </div>
        
        <div class="result-content">
          <h4 class="result-title">{{ result.title }}</h4>
          <p class="result-description">{{ result.description }}</p>
          <div class="result-meta">
            <span class="result-type">{{ getResultTypeText(result.type) }}</span>
            <span class="result-price" v-if="result.price">
              ¥{{ result.price }}
            </span>
          </div>
        </div>
        
        <div class="result-action">
          <el-icon>
            <ArrowRight />
          </el-icon>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search, ArrowRight, TrendCharts, Document, Setting } from '@element-plus/icons-vue'

interface SearchResult {
  id: string
  type: 'stock' | 'strategy' | 'function'
  title: string
  description: string
  price?: number
  url?: string
}

const props = defineProps<{
  query: string
  loading?: boolean
}>()

const emit = defineEmits<{
  resultClick: [result: SearchResult]
}>()

const results = ref<SearchResult[]>([])

// 模拟搜索结果
const mockResults: SearchResult[] = [
  {
    id: '1',
    type: 'stock',
    title: '000001 平安银行',
    description: '中国平安银行股份有限公司',
    price: 12.34
  },
  {
    id: '2',
    type: 'strategy',
    title: '均线策略',
    description: '基于移动平均线的量化交易策略'
  },
  {
    id: '3',
    type: 'function',
    title: '交易终端',
    description: '专业的股票交易界面'
  }
]

const getResultIcon = (type: string) => {
  switch (type) {
    case 'stock':
      return TrendCharts
    case 'strategy':
      return Document
    case 'function':
      return Setting
    default:
      return Document
  }
}

const getResultColor = (type: string) => {
  switch (type) {
    case 'stock':
      return '#409EFF'
    case 'strategy':
      return '#67C23A'
    case 'function':
      return '#E6A23C'
    default:
      return '#909399'
  }
}

const getResultTypeText = (type: string) => {
  switch (type) {
    case 'stock':
      return '股票'
    case 'strategy':
      return '策略'
    case 'function':
      return '功能'
    default:
      return '其他'
  }
}

const handleResultClick = (result: SearchResult) => {
  emit('resultClick', result)
}

// 监听搜索查询变化
watch(() => props.query, (newQuery) => {
  if (newQuery) {
    // 模拟搜索
    results.value = mockResults.filter(result =>
      result.title.toLowerCase().includes(newQuery.toLowerCase()) ||
      result.description.toLowerCase().includes(newQuery.toLowerCase())
    )
  } else {
    results.value = []
  }
}, { immediate: true })
</script>

<style scoped>
.mobile-search-results {
  padding: 16px;
}

.loading-state {
  padding: 20px;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
  color: #909399;
}

.results-list {
  space-y: 8px;
}

.result-item {
  display: flex;
  align-items: center;
  padding: 12px;
  background: white;
  border-radius: 8px;
  border: 1px solid #ebeef5;
  transition: all 0.3s;
}

.result-item:active {
  background: #f5f7fa;
  transform: scale(0.98);
}

.result-icon {
  margin-right: 12px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-size: 14px;
  font-weight: 500;
  margin: 0 0 4px 0;
  color: #303133;
}

.result-description {
  font-size: 13px;
  color: #606266;
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.result-meta {
  display: flex;
  align-items: center;
  gap: 8px;
}

.result-type {
  font-size: 12px;
  color: #909399;
  background: #f0f2f5;
  padding: 2px 6px;
  border-radius: 4px;
}

.result-price {
  font-size: 12px;
  color: #f56c6c;
  font-weight: 500;
}

.result-action {
  margin-left: 8px;
  color: #c0c4cc;
}
</style>
