{"timestamp": "2025-08-07T12:00:04.701Z", "baseUrl": "http://localhost:5173", "tests": [{"name": "首页 - 页面加载", "status": "failed", "timestamp": "2025-08-07T12:00:07.461Z", "message": "加载失败: this.page.waitForTimeout is not a function", "error": "TypeError: this.page.waitForTimeout is not a function\n    at FrontendTester.testPageLoad (file:///C:/Users/<USER>/Desktop/frontend/test-frontend.js:149:23)\n    at async FrontendTester.runAllTests (file:///C:/Users/<USER>/Desktop/frontend/test-frontend.js:482:7)"}, {"name": "导航功能", "status": "failed", "timestamp": "2025-08-07T12:00:17.468Z", "message": "导航测试失败: Waiting for selector `.navbar-nav` failed: Waiting failed: 10000ms exceeded", "error": "TimeoutError: Waiting for selector `.navbar-nav` failed: Waiting failed: 10000ms exceeded\n    at new WaitTask (file:///C:/Users/<USER>/Desktop/frontend/node_modules/puppeteer-core/lib/esm/puppeteer/common/WaitTask.js:45:34)\n    at IsolatedWorld.waitForFunction (file:///C:/Users/<USER>/Desktop/frontend/node_modules/puppeteer-core/lib/esm/puppeteer/api/Realm.js:22:26)\n    at CSSQueryHandler.waitFor (file:///C:/Users/<USER>/Desktop/frontend/node_modules/puppeteer-core/lib/esm/puppeteer/common/QueryHandler.js:175:95)\n    at async CdpFrame.waitForSelector (file:///C:/Users/<USER>/Desktop/frontend/node_modules/puppeteer-core/lib/esm/puppeteer/api/Frame.js:545:21)\n    at async CdpPage.waitForSelector (file:///C:/Users/<USER>/Desktop/frontend/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:1378:20)\n    at async FrontendTester.testNavigation (file:///C:/Users/<USER>/Desktop/frontend/test-frontend.js:206:7)\n    at async FrontendTester.runAllTests (file:///C:/Users/<USER>/Desktop/frontend/test-frontend.js:485:7)"}, {"name": "性能 - DOM加载", "status": "passed", "timestamp": "2025-08-07T12:00:17.471Z", "message": "DOM加载时间: 1.50ms"}, {"name": "性能 - 首次内容绘制", "status": "passed", "timestamp": "2025-08-07T12:00:17.471Z", "message": "FCP: 1092.00ms"}], "summary": {"total": 4, "passed": 2, "failed": 2, "warnings": 0}, "performance": {"domContentLoaded": 1.5, "loadComplete": 0.7000000000698492, "firstPaint": 1092, "firstContentfulPaint": 1092}, "errors": [], "screenshots": []}